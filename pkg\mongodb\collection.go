package mongodb

import (
	"context"
	"fmt"

	"liteframe/pkg/mongodb/operator"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Collection struct {
	coll *mongo.Collection
	m    *Mongo
}

// CollectionConfig 集合配置
type CollectionConfig struct {
	Name    string
	Indexes []mongo.IndexModel
}

// CreateCollection 显式创建集合
func (m *Mongo) CreateCollection(ctx context.Context, config CollectionConfig) (*Collection, error) {
	// 创建集合
	err := m.db.CreateCollection(ctx, config.Name)
	if err != nil && !mongo.IsDuplicateKeyError(err) {
		return nil, fmt.Errorf("failed to create collection: %w", err)
	}

	// 获取集合
	coll := m.db.Collection(config.Name)

	// 设置索引
	if len(config.Indexes) > 0 {
		indexView := coll.Indexes()
		_, err := indexView.CreateMany(ctx, config.Indexes)
		if err != nil {
			return nil, fmt.Errorf("failed to create indexes: %w", err)
		}
	}

	return &Collection{coll: coll}, nil
}

func NewCollection(coll *mongo.Collection) *Collection {
	c := &Collection{coll: coll}
	return c
}

func (m *Mongo) Collection(name string) *Collection {
	return &Collection{
		coll: m.db.Collection(name),
		m:    m,
	}
}

func (m *Mongo) CollectionUpsert(name string, upsert bool) *Collection {
	return &Collection{
		coll: m.db.Collection(name),
		m:    m,
	}
}

// FindOne 查询单个文档
func (c *Collection) FindOne(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) *mongo.SingleResult {
	return c.coll.FindOne(ctx, filter, opts...)
}

// FindOneAndUpdate 查询并更新单个文档
func (c *Collection) FindOneAndUpdate(ctx context.Context, filter interface{}, update interface{}, opts ...*options.FindOneAndUpdateOptions) *mongo.SingleResult {
	return c.coll.FindOneAndUpdate(ctx, filter, update, opts...)
}

// CreateIndexes 创建索引
func (c *Collection) CreateIndexes(ctx context.Context, indexes []mongo.IndexModel) error {
	if len(indexes) == 0 {
		return nil
	}

	_, err := c.coll.Indexes().CreateMany(ctx, indexes)
	return err
}

// UpdateOne 更新单条文档
func (c *Collection) UpdateOne(ctx context.Context, filter interface{}, update interface{}, opts ...*options.UpdateOptions) error {
	ctx, cancel := context.WithTimeout(ctx, c.m.conf.OperateTimeout)
	defer cancel()

	_, err := c.coll.UpdateOne(ctx, filter, update, opts...)
	return err
}

// InsertOne 插入单条文档
func (c *Collection) InsertOne(ctx context.Context, document interface{}, opts ...*options.InsertOneOptions) error {
	ctx, cancel := context.WithTimeout(ctx, c.m.conf.OperateTimeout)
	defer cancel()

	_, err := c.coll.InsertOne(ctx, document, opts...)
	return err
}

// DeleteOne 删除单条文档
func (c *Collection) DeleteOne(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions) error {
	ctx, cancel := context.WithTimeout(ctx, c.m.conf.OperateTimeout)
	defer cancel()

	_, err := c.coll.DeleteOne(ctx, filter, opts...)
	return err
}

func (c *Collection) Raw() *mongo.Collection {
	return c.coll
}

func (c *Collection) InsertMany(ctx context.Context, data []interface{}, opts ...*options.InsertManyOptions) error {
	_, err := c.coll.InsertMany(ctx, data, opts...)
	return err
}

func (c *Collection) DeleteByID(ctx context.Context, id interface{}, opts ...*options.DeleteOptions) error {
	filter := bson.D{{Key: "_id", Value: id}}
	_, err := c.coll.DeleteOne(ctx, filter, opts...)
	return err
}

func (c *Collection) DeleteOneByFilter(ctx context.Context, filter bson.M, opts ...*options.DeleteOptions) error {
	_, err := c.coll.DeleteOne(ctx, filter, opts...)
	return err
}

func (c *Collection) DeleteManyByFilter(ctx context.Context, filter bson.M, opts ...*options.DeleteOptions) error {
	_, err := c.coll.DeleteMany(ctx, filter, opts...)
	return err
}

func (c *Collection) DeleteAll(ctx context.Context, opts ...*options.DeleteOptions) error {
	_, err := c.coll.DeleteMany(ctx, bson.D{}, opts...)
	return err
}

func (c *Collection) UpdateByID(ctx context.Context, id interface{}, data interface{}, opts ...*options.UpdateOptions) error {
	filter := bson.D{{Key: "_id", Value: id}}
	update := bson.D{{Key: operator.Set, Value: data}}
	_, err := c.coll.UpdateOne(ctx, filter, update, opts...)
	return err
}

func (c *Collection) UpsertByID(ctx context.Context, id interface{}, data interface{}) error {
	filter := bson.D{{Key: "_id", Value: id}}
	update := bson.D{{Key: operator.Set, Value: data}}
	opts := options.Update().SetUpsert(true)
	_, err := c.coll.UpdateOne(ctx, filter, update, opts)
	return err
}

func (c *Collection) UpsertOneByFilter(ctx context.Context, filter bson.M, data interface{}) error {
	update := bson.D{{Key: operator.Set, Value: data}}
	opts := options.Update().SetUpsert(true)
	_, err := c.coll.UpdateOne(ctx, filter, update, opts)
	return err
}

func (c *Collection) UpdateFieldsByID(ctx context.Context, id interface{}, fields bson.M, opts ...*options.UpdateOptions) error {
	filter := bson.D{{Key: "_id", Value: id}}
	update := bson.D{{Key: operator.Set, Value: fields}}
	_, err := c.coll.UpdateOne(ctx, filter, update, opts...)
	return err
}

func (c *Collection) UpdateOneByFilter(ctx context.Context, filter bson.M, data interface{}, opts ...*options.UpdateOptions) error {
	update := bson.D{{Key: operator.Set, Value: data}}
	_, err := c.coll.UpdateOne(ctx, filter, update, opts...)
	return err
}

func (c *Collection) UpdateFieldsByFilter(ctx context.Context, filter bson.M, fields bson.M, opts ...*options.UpdateOptions) error {
	update := bson.D{{Key: operator.Set, Value: fields}}
	_, err := c.coll.UpdateMany(ctx, filter, update, opts...)
	return err
}

func (c *Collection) FindOneAndUpdateByID(ctx context.Context, id interface{}, data interface{}, out interface{}, opts ...*options.FindOneAndUpdateOptions) error {
	filter := bson.D{{Key: "_id", Value: id}}
	update := bson.D{{Key: operator.Set, Value: data}}
	if len(opts) == 0 {
		opt := options.FindOneAndUpdate()
		opt.SetReturnDocument(options.After)
		opts = append(opts, opt)
	}
	return c.coll.FindOneAndUpdate(ctx, filter, update, opts...).Decode(out)
}

func (c *Collection) FindOneAndIncFieldByID(ctx context.Context, id interface{}, field string, step int, out interface{}) error {
	filter := bson.D{bson.E{Key: "_id", Value: id}}
	update := bson.D{{Key: operator.Inc, Value: bson.D{{Key: field, Value: step}}}}
	opts := options.FindOneAndUpdate()
	opts.SetUpsert(true)
	opts.SetReturnDocument(options.After)
	return c.coll.FindOneAndUpdate(ctx, filter, update, opts).Decode(out)
}

func (c *Collection) ReplaceByID(ctx context.Context, id interface{}, data interface{}, opts ...*options.ReplaceOptions) error {
	filter := bson.D{{Key: "_id", Value: id}}
	_, err := c.coll.ReplaceOne(ctx, filter, data, opts...)
	return err
}

func (c *Collection) ReplaceOneByFilter(ctx context.Context, filter bson.M, data interface{}, opts ...*options.ReplaceOptions) error {
	_, err := c.coll.ReplaceOne(ctx, filter, data, opts...)
	return err
}

func (c *Collection) FindByID(ctx context.Context, id interface{}, out interface{}, opts ...*options.FindOneOptions) error {
	filter := bson.D{{Key: "_id", Value: id}}
	return c.coll.FindOne(ctx, filter, opts...).Decode(out)
}

func (c *Collection) FindOneByFilter(ctx context.Context, filter bson.M, out interface{}, opts ...*options.FindOneOptions) error {
	return c.coll.FindOne(ctx, filter, opts...).Decode(out)
}

func (c *Collection) FindAll(ctx context.Context, out interface{}, opts ...*options.FindOptions) error {
	cur, err := c.coll.Find(ctx, bson.D{}, opts...)
	if err != nil {
		return err
	}
	err = cur.All(ctx, out)
	if err != nil {
		return err
	}
	return nil
}

func (c *Collection) FindAllByFilter(ctx context.Context, filter bson.M, out interface{}, opts ...*options.FindOptions) error {
	cur, err := c.coll.Find(ctx, filter, opts...)
	if err != nil {
		return err
	}
	return cur.All(ctx, out)
}

func (c *Collection) Count(ctx context.Context, opts ...*options.CountOptions) (int64, error) {
	return c.coll.CountDocuments(ctx, bson.D{}, opts...)
}

func (c *Collection) CountByFilter(ctx context.Context, filter bson.M, opts ...*options.CountOptions) (int64, error) {
	return c.coll.CountDocuments(ctx, filter, opts...)
}

func (c *Collection) GetCollection() *mongo.Collection {
	return c.coll
}
