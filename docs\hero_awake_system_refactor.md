# 英雄觉醒系统重构文档

## 概述

本次重构将英雄觉醒系统从使用独立的 `TableHeroEvo` 表改为使用 `TableHero` 表中的新字段，实现了更灵活的觉醒配置。

## 表格设计变化

### TableHero 新增字段
- `ItemId []int32` - 觉醒消耗物品组ID数组
- `ItemCost []int32` - 不同觉醒等级消耗的物品数量数组
- `HeroLv []int32` - 英雄等级限制数组（下标对应觉醒等级）

### 移除的表格
- `TableHeroEvo` - 独立的觉醒配置表（将被移除）
- `TableHeroLevel.WakeUpLV` - 觉醒等级限制字段（将被移除）

## 新的觉醒系统规则

### 1. 觉醒支持判断
英雄是否支持觉醒由以下条件决定：
- `ItemCost` 数组不为空

`ItemCost` 才是控制觉醒消耗的关键，即使 `HeroLv` 有限制也没意义。如果 `ItemCost` 为空，则该英雄不支持觉醒，升级不受觉醒控制。

### 2. 觉醒等级限制
- 觉醒等级从0开始（0表示未觉醒）
- 最大觉醒等级 = `ItemCost` 数组长度
- 每个觉醒等级对应的最大英雄等级 = `HeroLv[觉醒等级]`

### 3. 觉醒消耗
- 物品ID列表 = `ItemId`（支持多个物品，为后期扩容做准备）
- 消耗数量 = `ItemCost[目标觉醒等级-1]`（数组索引从0开始）
- 每个物品都会消耗相同的数量

### 4. 英雄等级要求
- 所需英雄等级 = `HeroLv[目标觉醒等级-1]`（数组索引从0开始）

## 代码修改详情

### 新增辅助方法

#### 1. `isHeroSupportAwake(heroId int32) bool`
判断英雄是否支持觉醒系统。

#### 2. `getHeroMaxLevelByAwakeLevel(heroId int32, awakeLevel int32) int32`
根据当前觉醒等级获取英雄可升级的最大等级。

#### 3. `getHeroAwakeMaxLevel(heroId int32) int32`
获取英雄的最大觉醒等级。

#### 4. `getHeroAwakeCost(heroId int32, targetAwakeLevel int32) ([]int32, int32, bool)`
获取指定觉醒等级所需的物品ID列表和数量（支持多个物品消耗）。

#### 5. `getHeroAwakeRequiredLevel(heroId int32, targetAwakeLevel int32) (int32, bool)`
获取指定觉醒等级所需的英雄等级。

#### 6. `CanHeroLevelUp(heroId int32) (bool, error_code.Code)`
检查英雄是否可以升级（包含觉醒限制检查）。

#### 7. `CanHeroAwakeLevelUp(heroId int32) (bool, error_code.Code)`
检查英雄是否可以觉醒升级。

### 修改的核心方法

#### 1. `HeroLevelUp(heroId int32) (int32, int32, error_code.Code)`
- 移除了对 `TableHeroLevel.WakeUpLV` 的依赖
- 改为使用 `TableHero.HeroLv` 数组进行等级限制检查
- 只有支持觉醒的英雄才会进行觉醒等级限制检查

#### 2. `HeroAwakeLevelUp(heroId int32) (int32, error_code.Code)`
- 完全重构，移除了对 `TableHeroEvo` 表的依赖
- 改为使用 `TableHero` 的 `ItemId` 和 `ItemCost` 字段
- 支持多个物品的消耗，为后期扩容做准备
- 使用普通的 `DelItem` 方法扣除觉醒消耗物品

## 配置示例

### 支持觉醒的英雄（ID: 301）
```json
{
  "ID": 301,
  "ItemId": [1000001, 1000002],
  "ItemCost": [2, 8, 14, 20],
  "HeroLv": [8, 10, 12, 14]
}
```

**解释：**
- 支持4级觉醒（ItemCost数组长度为4）
- 觉醒1级：需要英雄8级，消耗物品1000001 x2个 + 物品1000002 x2个
- 觉醒2级：需要英雄10级，消耗物品1000001 x8个 + 物品1000002 x8个
- 觉醒3级：需要英雄12级，消耗物品1000001 x14个 + 物品1000002 x14个
- 觉醒4级：需要英雄14级，消耗物品1000001 x20个 + 物品1000002 x20个

### 不支持觉醒的英雄（ID: 101）
```json
{
  "ID": 101,
  "ItemId": [101],
  "ItemCost": [],
  "HeroLv": []
}
```

**解释：**
- `ItemCost` 和 `HeroLv` 为空，不支持觉醒
- 升级不受觉醒控制，只要 `TableHeroLevel` 有配置就能升级

## 兼容性说明

1. **向后兼容**：现有的英雄数据不会受到影响
2. **配置迁移**：需要将 `TableHeroEvo` 的数据迁移到 `TableHero` 中
3. **客户端适配**：客户端需要适配新的觉醒判断逻辑

## 优势

1. **配置统一**：所有英雄相关配置集中在 `TableHero` 表中
2. **灵活性增强**：不同英雄可以有不同的觉醒次数和要求
3. **维护简化**：减少了表格数量，降低维护复杂度
4. **扩展性好**：未来可以轻松添加新的觉醒相关字段
