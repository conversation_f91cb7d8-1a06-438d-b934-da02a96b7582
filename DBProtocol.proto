syntax = "proto3";


package dbstruct;
option go_package = "liteframe/internal/common/protos/dbstruct";

import "PublicMessage.proto";
import "PublicEnum.proto";

//--------------------------------玩家数据--------------------------------//
message UserDB {
  BaseDB base = 1;
  GameDB game = 2;
}

//玩家基础数据
message BaseDB {
  string account = 1;
  uint64 uid = 2;
  uint64 server_id = 3; //服务器 id
  string name = 4; //名字
  uint32 career = 5; //职业
  int32  head_icon = 6; //头像
  int32  head_frame = 7; //头像框
  string summary = 8; // 个性签名
  string init_device_id = 9; //初始的设备 id
  int32  model = 10; //形象
  string op_id = 11; // op id
  int64  create_time = 12; //创建角色时间
  uint32 level = 13; //角色等级
  int32  version = 14; //数据版本号
  int64  last_login = 15; //上一次登陆时间
  int64  last_logout = 16; //上一次登出时间
  int64  last_save_time = 17; //最后保存时间
  repeated int32 allMoneys = 18; //所有 money 数组
  int64 exp = 19; // 经验
  EGenderType gender = 20; //玩家性别，对应 GenderType 枚举
  int32 military_level = 21; //军衔
}

//玩家模块数据
message GameDB {
  Bag bag = 1;
  Money money = 2;
  PlayerAttr player_attr = 3;
  PlayerEvent player_event = 4;
  PBSettingData player_setting = 5;
  UserMailDB mail = 6;
  PlayerGuild player_guild = 7;
  HeadIconList head_icons = 8;
  HeadFrameList head_frames = 9;
  GoodsDB goods = 10; //商城商品数据（暂时只用于 RMB 商城和礼包功能）
  SevenSignIn seven_signin = 11;
  FirstChargeDB first_charge = 12;
  TopupRebate topup_rebate = 13;
  MonthlyCard monthly_card = 14;
  GradedFund graded_fund = 15;
  MissionData Mission = 16;
  FuncOpenData func_open = 17;
  ActivityData activity = 18;
  CommonExpBoxData common_box_reward = 19;
  DailySignIn daily_signin = 20;
  MonthlyCardNewList  monthly_card_new = 21;
  GachaBonus gachaBonus = 22;
  TimeGoodsDB time_goods = 23; //商城(日周月)数据
  Questionnaire questionnaire = 24;
  NewGuideData newGuideData = 25;//新手
  TowerData towerData = 26;//爬塔

  TotalRecharge totalRecharge = 28;//日进斗金
  InviteTasks inviteTask = 29; // 好友邀请
  HeavenlyDao heavenlyDao = 30; // 天道修为
  WeekCardData weekCardData = 31;//每日特惠
  Arena	arena = 32; // 竞技场
  PlayerHookData hookData = 33;  //挂机奖励
  Hero hero = 34; //英雄
  Lineup lineup = 35; //阵容
  Trophy trophy = 36; // 奖杯
  Treasure treasure = 37; // 宝物
  RedDot redDot = 38; // 红点系统
}

//背包
message Bag {
  map<int32, int32> items = 1; // itemId -> count
}

//货币
message Money {
  repeated MoneyData moneys = 1;
  PowerData power = 2;
}

//玩家属性
message PlayerAttr{
  repeated FightAttribute attrs = 1;
  repeated int32 missiondata = 2;
}

//玩家事件
message PlayerEvent{
  repeated EventData fixation_events = 1;  // 固定时间事件列表
  repeated EventData custom_events = 2;    // 自定义时间事件列表
}

// 邮件
message UserMailDB{
  map<uint64, PBMail> mails = 1;//邮件 key:邮件id value:邮件信息
  uint64 lastMailId = 2;
}


//好友邀请任务列表
message InviteTasks {
	repeated PBInviteTask task = 1;
}

message PlayerGuild {
    int64 guild_id = 1;                      	// 公会 ID，0 表示未加入公会
    GuildPosition position = 2;              	// 职位 (使用新的 GuildPosition 枚举)
    int64 join_time = 3;                     	// 加入时间
    int64 contribution = 4;                  	// 公会贡献度 (类型改为 int64 以支持更大数值)
    repeated int64 pending_apply_guild_ids = 5; // 当前已申请但未处理的联盟ID列表 (新增)
    int64 create_cooldown_end_time = 6;      	// 创建联盟冷却结束时间戳 (新增)
    int64 leave_cooldown_end_time = 7;       	// 退出联盟冷却结束时间戳 (新增)
	map<int32, PBGuildShopOrder> guild_shop_records = 8; // 商店相关 key: 商品ID (itemId)
	int64 last_daily_reset_time = 9;  			// 上次执行每日刷新逻辑的时间戳 (用于捐献次数、商店每日限购)
    int64 last_weekly_reset_time = 10; 			// 上次执行每周刷新逻辑的时间戳 (用于商店每周限购)
	map<int32, int32> daily_donate_counts = 11; // 各类捐献的次数 key: donateOpt 1 2 3
}

// GuildSystem
message GuildDB {
    map<int64, GuildData> guild_data_map = 1;  // key 为公会 ID
}

message GuildData {
    int64 guild_id = 1;                      // 公会 ID
    string name = 2;                         // 公会名称
    uint64 leader_id = 3;                    // 会长玩家 ID
    int32 level = 4;                         // 公会等级
    int64 exp = 5;                           // 公会经验
    int32 member_count = 6;                  // 当前成员数量
    int32 max_member = 7;                    // 最大成员数量 (随等级变化)
    int64 create_time = 8;                   // 创建时间戳
    map<uint64, GuildMember> members = 9;    // 成员列表，key 为玩家 ID
    // int64 fund = 10;                      // 公会资金 (暂不做)
    string notice = 11;                      // 联盟宣言 (新增)
    string announcement = 12;                // 联盟公告 (新增)
    int32 icon_id = 13;                      // 徽章ID (新增)
    bool free_join = 14;                     // 是否允许自由加入 (用于随机加入) (新增)
    int32 req_stage = 15;                    // 加入申请的关卡条件 (新增)
    map<uint64, GuildApplicationEntry> applications = 16; // 新增：内嵌的联盟申请记录列表 key: 申请者 player_id
	int32 today_joined_count = 17;           // 今日已通过申请/快速加入的人数
    int64 last_join_count_reset_time = 18;   // 上次重置今日入盟计数的时间戳(无效)	
	int64 contribution = 19;                 // 公会总贡献
}

message GuildMember {
    uint64 player_id = 1;        // 玩家 ID
    // string name = 2;          // 玩家名称 ( usersnap 获取)
    GuildPosition position = 3;  // 职位 (使用新的 GuildPosition 枚举)
    int64 join_time = 4;         // 加入时间
    int64 contribution = 5;      // 贡献度 (类型改为 int64)
    // int32 level = 6;          // 玩家等级 (从 usersnap 获取)
    // int64 fight_score = 7;    // 战力 (从 usersnap 获取)
    // int64 last_login = 8;     // 最后登录时间 (从 usersnap 获取)
}
// 内嵌在 GuildData 中的单个申请条目
message GuildApplicationEntry {
    uint64 player_id = 1;        		// 申请者玩家 ID
    int64 apply_timestamp = 2;   		// 申请时间戳
    GuildApplicationStatus status = 3;  // 申请状态
}

// ArenaSystem
message ArenaSystemDB {
    map<uint64, ArenaMember> arena_data_map = 1;  // key 为玩家 ID
	uint64 top_player_id = 2;        // 第一名玩家 ID
	uint64 last_player_id = 3;       // 最后一名玩家 ID
}

message ArenaMember {
    uint64 player_id = 1;       // 玩家 ID
    string name = 2;          	// 玩家名称
	uint32 level = 3;          	// 玩家等级
	uint32 score = 4;          	// 积分
	uint64 prev_player = 5;     // 上一个玩家 ID
	uint64 next_player = 6;     // 下一个玩家 ID
	int32 headIcon = 7; 	// 头像
	int32 headFrame = 8; 	// 头像框
}

// PaymentSystem
message PaymentDB {
    map<string, PBOrderData> order_data_map = 1;  // key 为订单 唯一ID
}

//HeadIconList
message HeadIconList {
  repeated HeadIconData headIconList = 1;
}

//HeadFrame
message HeadFrameList {
  repeated HeadFrameData headFrameList = 1;
}

//SevenSignIn
message DBSevenSign{
	int32 	signInDay = 1; 		// 签到天数
	int32 	signInState = 2; 	// 签到状态[0-未签到; 1-已签到未领取; 2-已签到已领取]
}
message SevenSignIn {
	repeated DBSevenSign signData = 1;		// 签到数据
}
message DBDailySign
{
	int32 days = 1;								// 已经签到天数
	int32 group = 2;								// 奖励组
	repeated int32 receivedDays = 3;			// 已领奖天
	repeated int32 receivedAccrued = 4;			// 已领奖累签
}
message DailySignIn {
	DBDailySign dailySignData = 1;			// 每日签到数据
}


//TopupRebate
message DBTopupRebate{
	int32 taskId = 1; 			// 任务Id
    int32 taskState = 2; 		// 任务状态[-1-未解锁;0-未领取; 1-已领取]
	int32 taskParam = 3; 		// 任务参数
}
message TopupRebate {
	repeated DBTopupRebate topupRebateData = 1;		// 充值返利数据
}

//MonthlyCard
message MonthlyCard {
	int32 expirationTime = 1; 	// 过期时间[0-未买; >0-剩余天数]
}

//GradedFund
message DBGradedFund
{
    int32 levelStageIdx = 1; // 等级阶段索引
    int32 comWealState = 2; // 普通福利状态[0-未领取; 1-已领取]
	int32 superWealState = 3; // 超级福利状态[-1-未解锁;0-未领取; 1-已领取]
}
message GradedFund {
	int32 gradedFundIdx = 1; // 基金阶段索引
	int32 gradedFundState = 2; // 基金阶段状态[0-未购买; 1-已购买]
	map<int32, DBGradedFund> gradedFundData = 3;	// 基金阶段信息
}

//-------------------------------任务数据--------------------------------//
message MissionInfo{
  PBMissionInfo common = 1;		// 任务信息
}

message MissionData {
	repeated MissionInfo missionData = 1;		// 任务数据
}
//-------------------------------任务数据--------------------------------//

//--------------------------------账号数据--------------------------------//
message AccountDB {
  string account = 1;
  uint64 uid = 2;
}

//--------------------------------服务器数据--------------------------------//
message GameData {
  int64 open_server_time = 1; // 开服时间
  int64 time_offset = 2; // 时间偏移
  int64 last_stop_server_time = 3; // 上次关服时间
  int32 register_count = 4; // 玩家数量
  int32 server_id = 5;  // 服务器 id
}

//--------------------------------Server 迁移而来--------------------------------//
message EventData {
  int64 event_id = 1;
  int32 event_type = 2;
  int64 execute_time = 3;
  int32 parameter_int = 4;
  string parameter_str = 5;
  int64 parameter_long = 6;
}
message PlayerGuildData {
  bool unlock = 1;
  int64 guild_id = 2;
  int32 last_quit_guild_time = 3;
  int32 apply_cDEnd_time = 4;
  int32 day_apply_count = 5;
  int32 free_donate_count = 6;
  int32 gold_donate_count = 7;
  int32 diamond_donate_count = 8;
  repeated GuildTech tech_list = 9;
  repeated PBCommonKeyValue buy_shop_list = 10;
  repeated PBCommonKeyValue fixed_shop_list = 11;
  bool red_goods_refresh = 12;
  bool red_bargaining_goods_refresh = 13;
  bool bargaining_gift = 14;
  int32 bargining_cDEnd_time = 15;
  bool buy_guild_bargaining_gift = 16;
  int32 buy_bargining_cDEnd_time = 17;
  int32 boss_buy_count = 18;
  bool boss_unlock = 19;
  bool tech_unlock = 20;
}
message PlayerGachaData {
  NormalGacha equip_gacha = 1;
  NormalGacha skill_gacha = 2;
  HallowsGacha hallows_gacha = 3;
  NormalGacha pets_gacha = 4;
}
message PlayerMiningData {
  bool unlock = 1;
  repeated MiningSlot slot_list = 2;
  repeated MiningSlot cache_list = 3;
  int32 mining_count = 4;
  int32 depth = 5;
  int32 inner_depth = 6;
  int32 shovel_video_count = 7;
  bool can_video_add_shovel = 8;
  int32 shovel_start_time = 9;
  int32 shovel_end_time = 10;
  repeated int32 new_steps = 11;
  int32 bomb_video_count = 12;
  int32 drill_bit_video_count = 13;
  int32 boss4_total_num = 14;
  int32 boss8_total_num = 15;
  repeated MiningEvent event_list = 16;
}
message PokedexData {
  int32 id = 1;
  int32 level = 2;
  int32 type = 3;
  BigDecimal _own_pro = 4;
}
message DragonBuildItem {
  int32 dragon_build_id = 1;
  int32 dragon_build_type = 2;
  int32 level = 3;
  int32 lock = 4;
  Hook hook = 5;
  repeated DragonBuildItemPartLock lock_list = 6;
}
message PlayerSettingData {
  bool is_show_push = 1;
}
message BiDeviceInfo {
  string v_client_iP = 1;
  string client_version = 2;
  string system_software = 3;
  string system_hardware = 4;
  string telecom_oper = 5;
  string network = 6;
  int32 screen_width = 7;
  int32 screen_hight = 8;
  float density = 9;
  string cpu_hardware = 10;
  int32 memory = 11;
  string g_lRender = 12;
  string g_lVersion = 13;
  string device_id = 14;
  int32 plat_iD = 15;
  string v_client_iPV6 = 16;
  int32 reg_channel = 17;
  int32 login_channel = 18;
  string device_uDID = 19;
  bool is_emulator = 20;
  string android_id = 21;
  string idfv = 22;
  string mac = 23;
  string oaid = 24;
  string caid = 25;
}
message FuntionUnLockData {
  int32 funtion_iD = 1;
  int32 unlock_type = 2;
  int32 unlock_statu = 3;
  int32 single_partical = 4;
  int32 partical = 5;
  int32 server_time_lock = 6;
}
message PlayerTitleData {
  int32 title_id = 1;
  int32 status = 2;
  int64 title_expire_time = 3;
}
message StageRewardData {
  int32 table_id = 1;
  int32 trigger_id = 2;
  int32 status = 3;
}
message HallowsData {
  int32 hallows_id = 1;
  int32 level = 2;
  int32 type = 3;
  int32 stack_count = 4;
  int32 attr_id = 5;
  int32 attr_value = 6;
  int32 new_flag = 7;
}
message PlayerResearchData {
  repeated FightAttribute attribute_list = 1;
  int32 level = 2;
  int32 unlock = 3;
}
message HeroData {
  int32 herouuid = 1;
  int32 hero_id = 2;
  int64 get_time = 3;
  int32 show_new = 4;
  int32 level = 5;
  int32 exp = 6;
  int32 star_level = 7;
  int32 awake_level = 8;
  int32 sublimation_level = 9;
  repeated int32 attr_arr = 10;
}
message PlayerChapterNodeData {
  int32 stage_max_id = 1;
  int32 stage_cur_id = 2;
  int32 stage_money_id = 3;
  int32 stage_status = 4;
  int32 stage_kill_num = 5;
  int32 stage_horse_pro = 6;
  int32 stage_ticket_ad_count = 7;
  int64 stage_ticket_ad_time = 8;
  int64 max_count = 9;
  repeated int32 dragon_drop_count = 10;
  int64 radom_seed = 11;
  int32 last_stage_end_time = 12;
}
message EquipData {
  int32 equip_id = 1;
  int32 level = 2;
  int32 type = 3;
  int32 stack_count = 4;
  int32 _own_pro = 5;
  int32 _own_pro1 = 6;
  int32 _drees_pro = 7;
  int32 _drees_pro1 = 8;
}
message DragonCompound {
  Dragon dragon_take_on = 1;
  int32 dragon_slot_level = 2;
  BigDecimal base_own_pro_id = 3;
  repeated DragonCompoundItem compound_item_list = 4;
  int32 total_times = 5;
  repeated int32 compound_fail_times = 6;
  int32 total_egg_times = 7;
}
message FightAttribute {
  int32 attr_id = 1;
  int32 type = 2;
  int32 level = 3;
  int32 value = 4;
}
message ActivityTimeData {
  int32 type = 1;
  int32 status = 2;
  int32 cur_activity_id = 3;
  int32 start_time = 4;
  int32 end_time = 5;
  int32 close_time = 6;
  bool call_start = 7;
  bool call_end = 8;
  bool call_close = 9;
}

message MonthCardData {
  int64 over_time = 1;
  int32 daily_reward_status = 2;
  int32 month_card_status = 3;
  int32 month_card_mail = 4;
}

message TDeviceInfo {
  string client_version = 1;
  string system_software = 2;
  string system_hardware = 3;
  string telecom_oper = 4;
  string network = 5;
  int32 screen_width = 6;
  int32 screen_hight = 7;
  float density = 8;
  string cpu_hardware = 9;
  int32 memory = 10;
  string g_lRender = 11;
  string g_lVersion = 12;
  string device_id = 13;
  int32 plat_iD = 14;
  string v_client_iP = 15;
  string v_client_iPV6 = 16;
  int32 reg_channel = 17;
  int32 login_channel = 18;
  string device_uDID = 19;
  bool is_emulator = 20;
  string android_id = 21;
  string idfv = 22;
  string mac = 23;
  string oaid = 24;
  string caid = 25;
}

message ActivityBalloonData {
  bool unlock = 1;
  int32 cur_activity_id = 2;
  repeated DragonDigSlot dig_slot_list = 3;
  int32 dig_count = 4;
  int32 cur_dig_count = 5;
  int32 dig_layer = 6;
  bool notify_boss = 7;
}

message ItemData {
  int32 data_id = 1;
  int32 stack_count = 2;
  int64 get_time = 3;
  int32 new_flag = 4;
}

message MoneyData {
  int32 money_id = 1;
  int64 money_num = 2;
}

message PowerData {
  PowerBuyData powerRemains = 1;
  repeated PBPowerRewardDataInfo powerRewards = 2;
  int64 lastPowerRewardTime = 3; // 玩家最后一次生成体力包的时间
}

message PowerBuyData {
  int32 normal_remains = 1;
  int32 miss_remains = 2;
  int32 video_remains = 3;
}

message SevenDayTaskData {
  int32 un_lock_day = 1;
  int64 create_time = 2;
  int64 expire_time = 3;
  int64 record_time = 4;
  int32 point = 5;
  repeated int32 stage_rewards = 6;
}

message FirstRechargeGiftData {
  int32 first_recharge_gift_status = 1;
  int32 first_recharge_gift_id = 2;
}

message MiniGameData {
  int32 game_type = 1;
  int32 game_open_type = 2;
  int64 exp = 3;
  int64 totalexp = 4;
  int32 scene_daily_drop_count = 5;
  repeated int32 stage_reward_type = 6;
  bool first = 7;
  int32 next_get_item_time = 8;
  int32 has_get_index = 9;
  int32 status = 10;
}

message PlayerLimitedGiftData {
  int32 rmb_consume = 1;
  repeated LimitedGiftData limited_gift_data_list = 2;
  repeated PBCommonKeyValue limited_gift_progress_list = 3;
  repeated int32 limited_gift_active_flags = 4;
  repeated int32 to_be_tri_limited_gift = 5;
}

message HeadFrameData {
  int32 frame_id = 1;
  int32 status = 2;
  bool new_get = 3;
  int64 frame_expire_time = 4;
}

message CDKeyUseInfo {
  int64 group_id = 1;
  int32 group_use_limit = 2;
  repeated string code_list = 3;
}

message ActivityListData {
  bool unlock = 1;
  repeated int32 type_list = 2;
}

message PlayerRefinementData {
  bool unlock1 = 1;
  bool unlock2 = 2;
  int32 count = 3;
  repeated RefinementSlot slots = 4;
  FightAttribute attack = 5;
  FightAttribute hp = 6;
}
message ActivityRChamptionData {
  bool unlock = 1;
  int32 cur_activity_id = 2;
  int32 race_num = 3;
  int32 rank_reward = 4;
  int32 rank_num = 5;
  repeated CommonLongKeyValue slots = 6;
}
message PlayerBoxData {
  int32 mini_game_type = 1;
  int32 mini_game_count = 2;
  repeated DropItemTypeInfo cache_drop = 3;
}
message ActivityLotteryData {
  bool unlock = 1;
  int32 cur_activity_id = 2;
  int32 lottery_num = 3;
  int32 rank_reward = 4;
  int32 rank_num = 5;
}
message PlayerFightData {
  repeated FightAttribute attr_list = 1;
  int64 pow_combat = 2;
}
message PlayerBlessData {
  bool unlock = 1;
  repeated Bless bless_list = 2;
  int32 ad_vip_level = 3;
  int32 ad_vip_exp = 4;
  FightAttribute ad_attr1 = 5;
  FightAttribute ad_attr2 = 6;
  bool auto_bless = 7;
}
message PowerRewardData {
  int64 start_time = 1;
}

message PlayerDragonFactoryData {
  int32 factory_level = 1;
  int32 auto_flag = 2;
  int32 auto_lock = 3;
  int32 auto_free = 4;
  int32 ad_over_time = 5;
  repeated PBCommonKeyValue auto_select = 6;
  int32 slimer_num = 7;
  int32 refresh_end_time = 8;
  int32 res_diamond_refresh_count = 9;
  int32 res_aDRefresh_end_time = 10;
  int32 res_aDRefresh_count = 11;
  repeated int64 loot_list = 12;
  repeated int64 foe_list = 13;
  repeated PlayerRedisBase foe_list_redis_base = 14;
  repeated PlayerDragonLogInfo dragon_log_infos = 15;
  repeated DropItemTypeInfo dragon_rewards = 16;
  int32 auto_over_time = 17;
}

message MailList {
  repeated PBMail list = 1;
}


message ItemExchangeDetailData {
  int32 id = 1;
  int32 count = 2;
}
message PVPData {
  bool unlock = 1;
  int32 refresh_end_time = 2;
  int32 first = 3;
  repeated int64 pk_id_list = 4;
  repeated Builder cache_pBPlayer_base_info = 5;
  double score = 6;
  repeated PVPRecordData record_data_list = 7;
  bool every_day_refresh_atk_count = 8;
}
message BattlePassData {
  int32 id = 1;
  int64 expired_time = 2;
  int32 item_id = 3;
  int32 level = 4;
  int32 exp = 5;
  bool buy_flag = 6;
  repeated int32 common_award_status = 7;
  repeated int32 senior_award_status = 8;
}
message ActivityDigData {
  bool unlock = 1;
  int32 cur_activity_id = 2;
  int32 item_num = 3;
  int32 collection_time = 4;
  int32 cur_query_rank = 5;
  int32 cache_award = 6;
  repeated int32 cache_team_awards = 7;
  bool cache_trigger_end = 8;
  int32 join = 9;
  repeated DropItemTypeInfo trans_item_list = 10;
  bool red_new_activity = 11;
}
message QuestData {
  int32 id = 1;
  bool clicked = 2;
  bool received = 3;
}
message PlayerDressData {
  int32 fight_dress_id = 1;
  repeated DressData list = 2;
}
message SignInToRewardData {
  int64 record_seven_day_time = 1;
  int32 seven_day_login_num = 2;
  repeated int32 seven_day_state_list = 3;
  bool seven_today_first = 4;
  int64 record_every_day_time = 5;
  int32 month_login_num = 6;
  int32 month_sign_num = 7;
  repeated int32 every_day_state_list = 8;
  int32 every_day_refresh_count = 9;
  bool every_day_first = 10;
  repeated int32 month_stage_state_list = 11;
}
message InviteFriendData {
  int64 _invite_me_gUID = 1;
  repeated InviteFriendSingleData invite_friend_single_data = 2;
}
message PlayerDragonData {
  bool unlock = 1;
  int32 fight_skin_id = 2;
  repeated DragonSkin own_skin_list = 3;
  repeated int32 from_dragon_skin_list = 4;
}
message NewGuideData {
  PBIntPair curGuide = 1;
  repeated int32 finishedGroupIds = 2;
  repeated int32 waitingForGuideIds = 3;
}
message DoorGiftData {
  bool unlock = 1;
  int32 cur_activity_id = 2;
  int32 gift_pos = 3;
  bool gift_refresh = 4;
}
// 挂机奖励
message PlayerHookData {
  bool unlock = 1;
  int32 daily_extra_award_count = 2;  // 每日额外奖励剩余次数
  int64 hook_start_time = 3;  // 开始挂机时间
  int64 last_reward_time = 4; // 上一次生成奖励时间，只有奖励物品发送变化时才记录
  map<int32, int32> reward_map = 5; // 已生成奖励 物品id -> 数量
  // Hook nh = 3;     // 不知道什么用
  // Hook eh = 4;
}
message ShopData {
  int64 unlock_time = 1;
  repeated CommonLongKeyValue mission_time_list = 2;
  repeated GiftPacksData list_gift_packs_bought = 3;
  repeated int32 list_gift_packs_avaliable = 4;
  repeated int32 list_gift_packs_new_on_shell = 5;
}
message HeadIconData {
  int32 head_icon_id = 1;
  int32 status = 2;
}
message TowerData {
  bool unlock = 1;
  int32 cur_stage_id = 2;
  int32 max_stage_id = 3;
  repeated TowerBuffSlot slots = 4;
  repeated TowerBuff31 buff31_list = 5;
  repeated int32 presets = 6;
  bool auto_use_pre_sets = 7;
  bool red_dot1 = 8;
  repeated PBCommonKeyValue buy_list = 9;
}
message SetGroupData {
  int32 group_id = 1;
  repeated int32 skill_slots = 2;
  repeated int32 pet_slots = 3;
  repeated int32 hallows_slots = 4;
  int64 dragon_uID = 5;
  int32 dress_id = 6;
  string name = 7;
}
message PlayerMoneyData {
  int32 money_coin = 1;
  int32 money_diamond = 2;
  int32 nick_power = 3;
  int32 money_refinement = 4;
  int32 money_hallows = 5;
  int32 money_guild_coin = 6;
  int32 sum_save_diamond = 7;
  int32 sum_midas_item = 8;
}
message PlayerTalentData {
  repeated FightAttribute attribute_talent_list = 1;
  int32 reset_talent_count = 2;
  int32 spare_talent_count = 3;
  int32 total_talent_count = 4;
}
message SkillData {
  int32 skill_id = 1;
  int32 level = 2;
  int32 stack_count = 3;
  int32 cd_start_time = 4;
  int32 status = 5;
  int32 base_own_pro_id = 6;
  int32 new_flag = 7;
}
message PVEVPData {
  bool unlock = 1;
  int32 activity_id = 2;
  int32 activity_condition_type = 3;
  int32 status = 4;
  int32 start_time = 5;
  int32 end_time = 6;
  int32 init_progress = 7;
  int32 dest_progress = 8;
  repeated PVEVPlayer players = 9;
  repeated int32 ranks = 10;
  repeated DropItemTypeInfo show_infos = 11;
  bool passed = 12;
  int32 next_join_time = 13;
  int32 old_show_progress = 14;
  int32 rank_status = 15;
  int32 pass_cDTime = 16;
}
message PromotionData {
  int32 promotion_id = 1;
  BigDecimal _own_pro1 = 2;
  BigDecimal _own_pro2 = 3;
}

message FriendData {
  repeated int64 have_friend_list = 1;
  repeated int64 send_apply_friend_list = 2;
  repeated int64 receive_apply_friend_list = 3;
  repeated int64 black_list = 4;
  repeated CommonLongKeyValue create_friend_time_list = 5;
  repeated CommonLongKeyValue cache_rec_gift_list = 6;
  repeated int64 sent_gift_player_ids = 7;
  repeated string received_gift_player_ids = 8;
  repeated CommonLongKeyValue gift_status_list = 9;
}
message CommonIntKeyValueLong {
  int32 key = 1;
  int64 value = 2;
}

message BuffAttribute {
  int32 attr_id = 1;
  BigDecimal value = 2;
  int32 buff_type = 3;
  int32 online_time = 4;
  int32 end_time = 5;
  int32 from_type = 6;
  int32 param = 7;
}
message PetData {
  int32 pet_id = 1;
  int32 level = 2;
  int32 type = 3;
  int32 stack_count = 4;
  int32 base_atk = 5;
  int32 base_own_pro_id = 6;
  int32 new_flag = 7;
}
message GuildTech {
  int32 id = 1;
  int32 level = 2;
  int32 _own_pro = 3;
}
message CommonLongKeyValue {
  int64 key = 1;
  int64 value = 2;
  int32 type = 3;
}
message HallowsGacha {
  int32 adv_gacha_count = 1;
  int32 last_watch_time = 2;
  int32 daily_watch_count = 3;
  int64 total_gacha_count = 4;
  bool first_gacha = 5;
}
message Hook {
  int32 last_award_time = 1;
  int32 gold_value = 2;
  int32 exp_value = 3;
  repeated DropItemTypeInfo items = 4;
  int32 cur_gold_speed = 5;
  int32 cur_exp_speed = 6;
  int32 cur_item_speed = 7;
  repeated int32 drop_group = 8;
  int32 gold_settlement_time = 9;
  int32 exp_settlement_time = 10;
  int32 item_settlement_time = 11;
  int32 stop_collect_time = 12;
  int32 gold_second = 13;
  int32 exp_second = 14;
  int32 item_second = 15;
}
message RefinementSlot {
  int32 slot_id = 1;
  int32 group_id = 2;
  int32 status = 3;
  RefinementAttribute attribute = 4;
}
message GiftPacksData {
  int32 id = 1;
  int32 gift_packs_type = 2;
  int32 consume_count = 3;
  int32 buy_type = 4;
  int32 buy_price = 5;
  int64 buy_time = 6;
  int64 invalid_time = 7;
  int32 refresh_type = 8;
  int32 sort_order = 9;
  bool new_on_shell = 10;
}
message DragonDigSlot {
  int32 id = 1;
  int32 status = 2;
  DragonDigItem dig_item = 3;
}
message BigDecimal {
  BigInteger int_val = 1;
  int32 scale = 2;
  int32 precision = 3;
  string string_cache = 4;
  int64 int_compact = 5;
}
message TowerBuffSlot {
  int32 slot_id = 1;
  int32 status = 2;
  int32 buff_id = 3;
  int32 buff_num = 4;
}
message Bless {
  int32 id = 1;
  int32 level = 2;
  int32 exp = 3;
  int32 attr_id = 4;
  BigDecimal value = 5;
  bool unlock = 6;
  bool first_expired_tip = 7;
}
message DressData {
  int32 dress_id = 1;
  int32 level = 2;
  int32 starlevel = 3;
  int32 compound_sucess = 4;
  int32 _own_pro1 = 5;
  int32 _own_pro2 = 6;
}
message DragonCompoundItem {
  int32 compound_slot_id = 1;
  Dragon compound_slot_dragon = 2;
}
message TowerBuff31 {
  repeated int32 buff31s = 1;
  bool created = 2;
  int32 stage_id = 3;
}
message PVEVPlayer {
  int32 id = 1;
  bool myself = 2;
  int64 robot_id = 3;
  int32 robot_type = 4;
  string nick_name = 5;
  string icon = 6;
  int32 progress = 7;
  bool finish = 8;
  int32 finish_time = 9;
  repeated int32 mock_times = 10;
  repeated int32 mock_stage_count = 11;
  repeated int32 rand_bounds = 12;
  int32 follow_inter_time = 13;
  int32 follow_time = 14;
}
message PlayerRedisBase {
  int64 platform_iD = 1;
  string nick_name = 2;
  int32 level = 3;
  int32 is_online = 4;
  int32 offline_start_time = 5;
  int64 pow_combat = 6;
  int32 main_stage_id = 7;
  string head_icon = 8;
  int32 dragon_skin_id = 9;
  int32 dress_id = 10;
  int32 weapon_id = 11;
  int32 promotion_id = 12;
  int64 exp = 13;
  int32 dragon_slot_level = 14;
  DressData fight_dress = 15;
  repeated EquipData fight_equip_list = 16;
  repeated PetData fight_pet_list = 17;
  repeated SkillData fight_skill_list = 18;
  repeated HallowsData fighthallow_list = 19;
  repeated FightAttribute fight_attribute_list = 20;
  int32 take_on_dragon_id = 21;
  repeated int64 black_list = 22;
  int64 guild_id = 23;
  bool bargaining_gift = 24;
  int32 bargining_cDEnd_time = 25;
  bool buy_guild_bargaining_gift = 26;
  int32 buy_bargining_cDEnd_time = 27;
  int32 head_frame_id = 28;
  int32 title_id = 29;
}
message DropItemTypeInfo {
  int32 item_id = 1;
  int32 item_count = 2;
  int32 item_type = 3;
  int32 item_from_type = 4;
  bool high_effect = 5;
}
message MiningSlot {
  int32 type = 1;
  int32 status = 2;
  int32 boss_row = 3;
  int32 boss_group_id = 4;
  DropItemTypeInfo item = 5;
  repeated DropItemTypeInfo info_list = 6;
  int32 turn_drop_group_id = 7;
  bool light_status = 8;
}
message Dragon {
  int64 dragon_seq_id = 1;
  int32 dragon_id = 2;
}
message InviteFriendSingleData {
  int64 _invite_friend_gUID = 1;
  CommonLongKeyValue reward_staus = 2;
  repeated MissionData mission_data_list = 3;
}
message NormalGacha {
  int32 level = 1;
  int64 exp = 2;
  int32 adv_gacha_count = 3;
  int32 last_watch_time = 4;
  int32 daily_watch_count = 5;
  repeated int32 level_awards = 6;
  int64 total_gacha_count = 7;
  bool first_gacha = 8;
}
message Builder {
  int64 platform_iD_ = 1;
  Object nick_name_ = 2;
  Object head_icon_ = 3;
  int32 level_ = 4;
  int64 comm_value0_ = 5;
  int64 comm_value1_ = 6;
  int32 dragon_skin_id_ = 7;
  int32 dress_id_ = 8;
  int32 weapon_id_ = 9;
  int32 rank_ = 10;
  int32 promotion_id_ = 11;
  int64 pow_combat_ = 12;
  int32 online_ = 13;
  int32 offline_start_time_ = 14;
  int32 main_stage_id_ = 15;
  int32 dress_star_ = 16;
  int32 head_frame_id_ = 17;
  int32 title_id_ = 18;
}
message PVPRecordData {
  int64 record_id = 1;
  int64 battle_id = 2;
  int32 win = 3;
  int32 revenge_success = 4;
  int32 goafter_success = 5;
  int32 addscore = 6;
  int64 time = 7;
  int32 red = 8;
  int64 fighter_player_id = 9;
}
message DragonSkin {
  int32 id = 1;
  int32 status = 2;
}
message float {
}
message PlayerDragonLogInfo {
  string head_icon = 1;
  int64 player_iD = 2;
  int32 item_iD = 3;
  int32 item_num = 4;
  int32 award_type = 5;
  string name = 6;
  int64 time = 7;
  int32 level = 8;
}
message MiningEvent {
  int32 item_id = 1;
  int32 item_type = 2;
  int32 slot_id = 3;
  int32 turn_drop_group_id = 4;
  repeated DropItemTypeInfo info_list = 5;
}
message LimitedGiftData {
  int32 gift_type = 1;
  int32 gift_id = 2;
  int64 active_time = 3;
  int32 consume_count = 4;
}
message DragonBuildItemPartLock {
  int32 lock_id = 1;
  int32 lock_status = 2;
}
message Object {
}
message RefinementAttribute {
  int32 table_id = 1;
}
message DragonDigItem {
  int32 type = 1;
  DropItemTypeInfo item = 2;
  DragonDigEvent event = 3;
}
message BigInteger {
  int32 signum = 1;
  repeated int32 mag = 2;
  int32 bit_count_plus_one = 3;
  int32 bit_length_plus_one = 4;
  int32 lowest_set_bit_plus_two = 5;
  int32 first_nonzero_int_num_plus_two = 6;
}
message DragonDigEvent {
  int32 item_id = 1;
  int32 item_type = 2;
  int32 layer = 3;
  int32 slot_id = 4;
  int32 turn_drop_group_id = 5;
  repeated DropItemTypeInfo info_list = 6;
}

//商品数据
message GoodsDB {
	map<int32, GoodsData> goods_data_map = 1;  // key 为商品 id
}
message GoodsData {
	EGoodsBuySourceType source_type = 1;//商品购买来源类型
	int32 goods_id = 2;//商品 ID
	int64 buy_time = 3;//最近购买时间
	int32 buy_count = 4;//购买次数
	int64 fresh_time = 5;//刷新时间（如果是需要定时刷新的商品）
	int32 goods_type = 6;//礼包类型
}

//首冲礼包奖励数据
message FirstChargeGiftData {
	int32 days = 1;//第几天
	int64 giftTime = 2;//礼包领取时间
}
//首冲礼包数据
message FirstChargeData {
	int32 id = 1;//礼包档位
	int64 buy_time = 2;//购买首冲礼包时间
	map<int32, FirstChargeGiftData> gift_data_map = 3;  // key 为天
}
message FirstChargeDB {
	map<int32, FirstChargeData> firstcharge_data_map = 2;  // key 首冲礼包档位
}
enum EGoodsBuySourceType {
	NormalGoods = 0; //非 RMB 商品
	RmbGoods = 1;//RMB 商品
}

enum EGenderType
{
	Default = 0; //神秘
	Man = 1; //男性
	Woman = 2; //女性
}

message FuncOpenData {
  map<int32, int32> func_open_data_map = 1;  // key 为功能 ID
  map<int32, int32> func_prev_reward = 2;   // 功能预告奖励领取状态
}

// 活动数据
message ActivityData {
  repeated ActivityCommonInfo activity_list = 1;  // key 为活动 ID
}

message ActivityCommonInfo {
  int32 activity_id = 1; // 活动 ID./
  ActivityStatus activity_status = 2; // 活动状态
}

//阶段宝箱相关数据
message CommonExpBoxData
{
  repeated PBCommonExpBoxInfo box_list = 1;	    //宝箱信息
}

//MonthlyCardNewList
message MonthlyCardNewList {
  repeated MonthlyCardNewData monthlyCardNews = 1; //月卡列表
  int64 extraRewardTime = 2; //额外奖励领取时间
}

message MonthlyCardNewData{
  int32 monthlyCardNewId = 1; //月卡2.0类型[0-超值月卡; 1-至尊月卡]
  int32 expirationTime = 2; //过期时间[0-未买; >0-剩余天数]
}

message TimeGoodsDB {
	map<int32, TimeGoodsData> time_goods_data_map = 1;  // key 为商品 id
}

message TimeGoodsData {
	int32 time_goods_id = 1;//商品 ID
	int32 buy_count = 2;//购买次数
	int32 life_buy_count = 3;//累计购买次数
}

//千抽
message DBGachaBonus
{
	int32 stage = 1;							// 关卡
	int32 group = 2;							// 奖励组
	int32 state = 3;							// 千抽状态 [0-未抽; 1-已抽未领取; 2-已抽已领取]			
	repeated int32 receivedStages = 4;			// 已领奖关卡
}
message GachaBonus {
	DBGachaBonus gachaBonusData = 1;				// 千抽数据
}

// 问卷
message Questionnaire
{
	 map<int32, bool> questionnaires = 1;  // 问卷ID -> 是否已领取
}

//日进斗金
message TotalRecharge
{
	int64 endTime = 1;							// vip经验宝箱 
    map<int32, int32> rewardData =2;    // vip等级礼包
    int32 nextTime = 3;							// 下次计数生效时间戳 	
	int32 totalDay = 4;							// 累计天数
}
//竞技场
message Arena 
{
	int32 challengeTimes = 1;		//挑战次数
}
//天道修为
message  HeavenlyDao
{
  int32 level = 1; //天道修为等级
}
//每日特惠
message WeekCardData
{
	int64 overTime 								= 1;//特惠结束时间
	repeated PBCommonKeyValue dailyRewardStatus	= 2;//每日奖励领取状态
	int32 giftStatus							= 3;//生效状态
}
// TipOffSystem 举报系统DB
message TipOffDB {
   repeated TipOffData TipOffDatas	= 1;//举报数据
}
// TipOffSystem 举报系统DB
message TipOffData {
	uint64 sendTipOffPId 								= 1;//举报者
	uint64 receiveTipOffPId 							= 2;//被举报者
    int32 TipOffType                                     = 3;  // 举报类型
    string TipOffContent                                =  4;  // 举报说明
	int32 TipOffStaus                                     = 5;  // 举报处理状态
	int32 TipOffPara                                    = 6;  // 举报后处理参数
}

//英雄数据
message Hero
{
  repeated PBHeroInfo heroList = 1; // 英雄列表
}

//阵容数据
message Lineup {
  repeated int32 unlocked_ids = 1;        //已解锁阵容
  int32 current_id = 2;                   //当前阵容id
  repeated PBLineupInfo lineup_list = 4;  //阵容列表
}

// SeasonBuffDB 赛季BUFF DB
message SeasonBuffDB {
  PBSeasonBuff data	= 1; //赛季BUFF数据
}

// 奖杯信息
message Trophy {
    // 核心数据
    int32 current_trophy = 1;       // 当前总奖杯数
    int32 current_win_streak = 2;   // 当前连胜次数

    // 奖励领取记录
    repeated int32 claimed_rewards = 3;   // 已领取的普通段位奖励ID列表 (对应MainRank_Reward)
    
    // 赛季相关
    int32 current_season_id = 4;                	// 数据最后同步的赛季ID
    repeated int32 claimed_season_rewards = 5; 		// 当前赛季已领取的段位奖励ID列表
    repeated SeasonTrophyInfo season_history = 6; 	// 历史赛季奖杯数据

    // 每日额外奖励
    int32 supply_times = 7;          // "对战补给"已用次数
    int32 blessing_times = 8;        // "对战庇佑"已用次数
    int64 last_daily_reset_time = 9; // 上次每日重置时间
}

// 宝物信息
message Treasure {
	 // 玩家当前拥有的所有宝物列表及其详细状态
    repeated PBTreasureInfo treasureList = 1;

    // 记录每种宝物历史累计获得的总数量, key: treasureId, value: totalCount
    map<int32, int32> totalAcquiredCount = 2;

    // 玩家在不同卡池的抽卡状态, key: gachaId, value: pityCount
    map<int32, int32> gachaPityCounts = 3;
	
	// 每日广告抽取次数记录
	int32 dailyAdGachaCounts = 4;

	// 上次广告次数重置时间戳
    int64 lastAdGachaResetTime = 5;
}

// 红点系统数据
message RedDot {
    // 红点状态映射，key: RedDotType枚举值, value: 是否显示红点
    map<int32, bool> red_dot_states = 1;
}