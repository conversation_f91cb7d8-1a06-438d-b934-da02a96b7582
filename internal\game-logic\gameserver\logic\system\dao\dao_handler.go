package dao

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"

	"liteframe/internal/common/protos/public"

	"liteframe/internal/common/error_code"
	"liteframe/internal/common/protos/dbstruct"
	"liteframe/internal/common/protos/g2m"
	"liteframe/internal/game-logic/gameserver/gameutil/dbutil"
	"liteframe/internal/game-logic/gameserver/global"
	"liteframe/pkg/actor"
	"liteframe/pkg/log"
	"liteframe/pkg/mongodb"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"google.golang.org/protobuf/proto"
)

type accountInfo struct {
	Account string `bson:"_id"`
	Uid     uint64 `bson:"uid"`
}

func (d *DAO) secondHandler(ctx context.Context, req *actor.Message) error {
	return nil
}

func (d *DAO) pingHandler(ctx context.Context, req *actor.Message) error {
	err := d.Db.Ping(ctx)
	req.Response(actor.RespMessage{Err: err})
	return nil
}

// createAccount 创建新账号并分配 UID
func (d *DAO) createAccount(account string, serverId uint64) (uint64, uint32, error) {
	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return 0, uint32(error_code.ERROR_PARAMS), fmt.Errorf("invalid db type")
	}

	// 1. 原子操作：递增计数器并检查上限
	serverDataColl := mdb.Collection("game")
	result := bson.M{}
	err := serverDataColl.FindOneAndUpdate(
		context.Background(),
		bson.M{
			"server_id":      serverId,
			"register_count": bson.M{"$lt": d.maxRegister},
		},
		bson.M{"$inc": bson.M{"register_count": 1}},
		options.FindOneAndUpdate().SetUpsert(true),
		options.FindOneAndUpdate().SetReturnDocument(options.After),
	).Decode(&result)

	if err != nil {
		return 0, uint32(error_code.ERROR_PARAMS), fmt.Errorf("increase register count failed: %v", err)
	}

	// 从 result 中获取计数值
	count := result["register_count"].(int32)

	// 2. 生成 UID 并创建账号映射
	uid := global.GenUserUID(serverId, uint32(count))
	accountsColl := mdb.Collection("accounts")
	err = accountsColl.InsertOne(
		context.Background(),
		bson.M{
			"_id": account,
			"uid": uid,
		},
	)

	if err != nil {
		return 0, uint32(error_code.ERROR_PARAMS), fmt.Errorf("create account failed: %v", err)
	}
	log.Info("account create success", log.Kv("account", account), log.Kv("uid", uid))
	return uid, uint32(error_code.ERROR_OK), nil
}

// loadOrCreateUidHandler 加载或创建账号的 UID
func (d *DAO) loadOrCreateUidHandler(ctx context.Context, req *actor.Message) error {
	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return fmt.Errorf("invalid db type")
	}

	msg := req.Data.(*g2m.G2M_LoadCreateUid)
	var e error
	respData := &g2m.M2G_LoadCreateUid{
		Code: uint32(error_code.ERROR_PARAMS),
	}
	defer func() {
		req.Response(actor.RespMessage{Err: e, Data: respData})
	}()

	// 查找现有账号
	accountsColl := mdb.Collection("accounts")
	result := accountInfo{}
	err := accountsColl.FindOne(
		ctx,
		bson.M{
			"_id": msg.Account,
		},
	).Decode(&result)

	if errors.Is(err, mongo.ErrNoDocuments) {
		// 账号不存在，创建新账号
		uid, code, e := d.createAccount(msg.Account, msg.ServerId)
		if e != nil {
			respData.Code = code
			return e
		}
		respData.Uid = uid
		respData.Code = uint32(error_code.ERROR_OK)
		return nil
	} else if err != nil {
		return fmt.Errorf("find account failed: %v", err)
	} else {
		respData.Code = uint32(error_code.ERROR_OK)
		respData.Uid = result.Uid
		return e
	}
}

// loadUidByAccountHandler 仅加载账号对应的 UID
func (d *DAO) loadUidByAccountHandler(ctx context.Context, req *actor.Message) error {
	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return fmt.Errorf("invalid db type")
	}

	msg := req.Data.(*g2m.G2M_LoadCreateUid)
	accountsColl := mdb.Collection("accounts")

	result := accountInfo{}
	err := accountsColl.FindOne(
		ctx,
		bson.M{
			"_id": msg.Account,
		},
	).Decode(&result)

	if err != nil {
		req.Response(actor.RespMessage{
			Err: err,
		})
		return err
	}
	req.Response(actor.RespMessage{
		Data: result.Uid,
	})
	return nil
}

// loadOrCreateUserHandler 加载或创建玩家数据
func (d *DAO) loadOrCreateUserHandler(ctx context.Context, req *actor.Message) error {
	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return fmt.Errorf("invalid db type")
	}

	msg := req.Data.(*g2m.G2M_LoadOrCreateUser)
	var e error
	respData := &g2m.M2G_LoadOrCreateUser{
		User:  nil,
		IsNew: false,
	}
	defer func() {
		req.Response(actor.RespMessage{Err: e, Data: respData})
	}()

	account := msg.Account
	uid := msg.Uid
	if uid == 0 {
		respData.Code = uint32(error_code.ERROR_OK)
		e = fmt.Errorf("account %s uid invalid", account)
		return e
	}

	playersColl := mdb.Collection("players")
	userDB := &dbstruct.UserDB{}
	err := playersColl.FindOne(
		ctx,
		bson.M{"_id": msg.Uid},
	).Decode(userDB)

	if errors.Is(err, mongo.ErrNoDocuments) {
		// 创建新玩家
		userDB = CreateUserDB(msg.Account, "", msg.ServerId, msg.Uid, global.Now().Unix())
		err = playersColl.InsertOne(
			ctx,
			bson.M{
				"_id":  uid,
				"base": userDB.Base,
				"game": userDB.Game,
			},
		)
		if err != nil {
			respData.Code = uint32(error_code.ERROR_PARAMS)
			e = err
			return e
		}
		respData.User = userDB
		respData.IsNew = true
		respData.Code = uint32(error_code.ERROR_OK)
		log.Info("create new user success",
			log.Kv("uid", respData.User.Base.Uid),
			log.Kv("account", respData.User.Base.Account),
		)
		return nil
	} else if err != nil {
		respData.Code = uint32(error_code.ERROR_PARAMS)
		e = fmt.Errorf("account %s uid %d load err %s", account, uid, err.Error())
		return e
	}

	respData.Code = uint32(error_code.ERROR_OK)
	respData.User = userDB

	log.Info("user load success",
		log.Kv("uid", respData.User.Base.Uid),
		log.Kv("name", respData.User.Base.Name),
		log.Kv("new", respData.IsNew),
	)
	return nil
}

// saveUserHandler 保存用户数据
func (d *DAO) saveUserHandler(ctx context.Context, req *actor.Message) error {
	var err error
	defer func() {
		req.Response(actor.RespMessage{
			Err: err,
		})
	}()

	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return fmt.Errorf("invalid db type")
	}

	saveMsg := req.Data.(*g2m.G2M_SaveUser)
	uid := req.Uid

	// 解析用户数据
	userDB := saveMsg.User
	// 更新到 MongoDB
	playersColl := mdb.Collection("players")
	err = playersColl.UpdateOne(
		ctx,
		bson.M{"_id": uid},
		bson.M{"$set": userDB},
	)
	if err != nil {
		log.Error("save to mongodb failed", log.Kv("uid", uid), log.Err(err))
		return saveUserToJson(uid, saveMsg.Data[0], saveMsg.Data[1])
	}

	log.Info("save user success", log.Kv("uid", uid))
	return nil
}

// createUser 创建新用户
func (d *DAO) createUser(ctx context.Context, req *g2m.G2M_LoadOrCreateUser) (*dbstruct.UserDB, error_code.Code, error) {
	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return nil, error_code.ERROR_PARAMS, fmt.Errorf("invalid db type")
	}

	account := req.Account
	serverId := req.ServerId
	uid := req.Uid
	name := "" // TODO: 处理名字生成逻辑

	// 创建新用户数据
	userDB := CreateUserDB(account, name, serverId, uid, global.Now().Unix())

	// 插入到 MongoDB
	playersColl := mdb.Collection("players")
	err := playersColl.InsertOne(
		context.Background(),
		bson.M{
			"_id":  userDB.Base.Uid,
			"base": userDB.Base,
			"game": userDB.Game,
		},
	)

	if err != nil {
		return nil, error_code.ERROR_PARAMS, fmt.Errorf("create user failed: %v", err)
	}

	log.Info("create user success",
		log.Kv("account", account),
		log.Kv("uid", uid),
		log.Kv("name", name),
	)

	return userDB, error_code.ERROR_OK, nil
}

// loadUser 加载用户数据
func (d *DAO) loadUser(uid uint64) (*dbstruct.UserDB, error) {
	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return nil, fmt.Errorf("invalid db type")
	}

	playersColl := mdb.Collection("players")
	userDB := &dbstruct.UserDB{}

	err := playersColl.FindOne(
		context.Background(),
		bson.M{"_id": uid},
	).Decode(userDB)

	if errors.Is(err, mongo.ErrNoDocuments) {
		return nil, fmt.Errorf("user not found")
	} else if err != nil {
		return nil, fmt.Errorf("load user failed: %v", err)
	}

	return userDB, nil
}

// loadUserHandler 处理加载用户请求
func (d *DAO) loadUserHandler(ctx context.Context, req *actor.Message) error {
	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return fmt.Errorf("invalid db type")
	}

	msg := req.Data.(*g2m.G2M_LoadUser)
	uid := msg.Uid
	var err error
	respData := &g2m.M2G_LoadUser{}
	defer func() {
		req.Response(actor.RespMessage{Data: respData, Err: err})
	}()

	accountsColl := mdb.Collection("accounts")
	result := &accountInfo{}
	if len(msg.Account) > 0 {
		err := accountsColl.FindOne(
			ctx,
			bson.M{"_id": msg.Account},
		).Decode(result)

		if err != nil {
			log.Error("load account failed", log.Kv("account", msg.Account), log.Err(err))
			return err
		} else {
			uid = result.Uid
		}
	} else if len(msg.Name) > 0 {
		//TODO 通过名字查询
	}

	if uid == 0 {
		err = fmt.Errorf("load user uid 0 req info %+v", msg)
		return err
	}

	playersColl := mdb.Collection("players")
	result1 := &dbstruct.UserDB{}
	err = playersColl.FindOne(
		ctx,
		bson.M{"_id": msg.Uid},
	).Decode(result1)

	if err != nil {
		return err
	}

	respData.User = result1
	log.Info("load user success", log.Kv("uid", msg.Uid), log.Kv("name", result1.Base.Name))
	return nil
}

// LoadUserSnapDataBatch 加载所有用户快照数据
//func (d *DAO) LoadUserSnapDataBatch(ctx context.Context, req *actor.Message) error {
//	mdb, ok := d.Db.(*mongodb.Mongo)
//	if !ok {
//		return fmt.Errorf("invalid db type")
//	}
//
//	msg := req.Data.(*g2m.G2M_LoadUserSnapDataBatch)
//	respData := &g2m.M2G_LoadUserSnapDataBatch{}
//	var e error
//	defer func() {
//		req.Response(actor.RespMessage{Err: e, Data: respData})
//	}()
//
//	// 设置分页参数
//	pageSize := int64(300) // 默认每页数量
//	pageNum := int64(1)    // 默认页码
//
//	if msg.GetLimit() < pageSize {
//		pageSize = msg.GetLimit()
//	}
//
//	// 构建查询条件
//	filter := bson.M{}
//	if msg.GetLastUid() > 0 {
//		// 采用 uid > lastUid 的方式进行增量查询
//		filter["_id"] = bson.M{"$gt": msg.GetLastUid()}
//	}
//
//	// 设置查询选项
//	findOptions := options.Find()
//	findOptions.SetLimit(pageSize)
//	findOptions.SetSkip((pageNum - 1) * pageSize)
//	findOptions.SetSort(bson.M{"_id": 1}) // 按 uid 升序排序确保分页一致性
//
//	// 只查询需要的字段以减少传输数据量 todo:后面有需要再加
//	//findOptions.SetProjection(bson.M{
//	//	"base.uid":              1,
//	//	"base.name":             1,
//	//})
//
//	// 执行查询
//	cursor, err := mdb.Collection("players").Raw().Find(ctx, filter, findOptions)
//	if err != nil {
//		log.Error("加载用户快照数据失败", log.Err(err))
//		return err
//	}
//	defer func(cursor *mongo.Cursor, ctx context.Context) {
//		err = cursor.Close(ctx)
//		if err != nil {
//			log.Error("LoadUserSnapDataBatch cursor.Close", log.Err(err))
//		}
//	}(cursor, ctx)
//
//	// 处理数据
//	userSnapData := &public.UserSnapUsers{}
//	var lastUid uint64 = 0
//	var processedCount int64 = 0 // 添加处理计数器
//
//	for cursor.Next(ctx) {
//		processedCount++ // 增加处理计数
//		userDB := &dbstruct.UserDB{}
//		if err = cursor.Decode(userDB); err != nil {
//			// 存在不符合数据跳过
//			continue
//		}
//
//		// 记录最后一个用户的 uid，用于下次查询
//		if userDB.Base.Uid > lastUid {
//			lastUid = userDB.Base.Uid
//		}
//
//		// 转换为快照数据
//		userSnapData.Users = append(userSnapData.Users, user_snap.TransFormUserDBToUserInfo(userDB))
//	}
//
//	// 序列化数据
//	data, err := proto.Marshal(userSnapData)
//	if err != nil {
//		log.Error("序列化用户快照数据失败", log.Err(err))
//		return err
//	}
//
//	// 设置响应数据
//	respData.UserData = data
//	respData.LastUid = lastUid
//	respData.HasMore = processedCount >= pageSize // 判断是否还有更多数据
//
//	return nil
//}

// loadDataHandler 加载服务器通用数据
func (d *DAO) loadDataHandler(ctx context.Context, req *actor.Message) error {
	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return fmt.Errorf("invalid db type")
	}

	msg := req.Data.(*g2m.G2M_LoadData)
	respData := &g2m.M2G_LoadData{}
	var e error
	defer func() {
		req.Response(actor.RespMessage{Err: e, Data: respData})
	}()

	// 从 game 集合中获取单一的 GameData 文档
	result := &dbstruct.GameData{}
	err := mdb.Collection("game").FindOne(
		ctx,
		bson.M{"server_id": msg.ServerId}, // 使用 server_id 作为查询条件
	).Decode(result)

	if errors.Is(err, mongo.ErrNoDocuments) {
		// 如果文档不存在，返回空数据
		respData.Data = nil
		return nil
	} else if err != nil {
		log.Error("load game data failed", log.Kv("server_id", msg.ServerId), log.Err(err))
		return err
	}

	// 将 GameData 序列化为字节数组
	data, err := proto.Marshal(result)
	if err != nil {
		log.Error("marshal game data failed", log.Kv("server_id", msg.ServerId), log.Err(err))
		return err
	}

	respData.Data = data
	return nil
}

// saveDataHandler 保存服务器通用数据
func (d *DAO) saveDataHandler(ctx context.Context, req *actor.Message) error {
	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return fmt.Errorf("invalid db type")
	}

	msg := req.Data.(*g2m.G2M_SaveData)
	var err error
	defer func() {
		req.Response(actor.RespMessage{Err: err})
	}()

	// 解析 GameData
	gameData := &dbstruct.GameData{}
	err = proto.Unmarshal(msg.Data, gameData)
	if err != nil {
		log.Error("unmarshal game data failed", log.Kv("server_id", msg.ServerId), log.Err(err))
		return err
	}

	// 使用 upsert 更新 GameData 文档

	if err = mdb.Collection("game").FindOne(
		ctx,
		bson.M{"server_id": msg.ServerId},
	).Err(); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			err = mdb.Collection("game").InsertOne(
				ctx,
				bson.M{"server_id": msg.ServerId},
			)

			if err != nil {
				log.Error("insert game data failed", log.Kv("server_id", msg.ServerId), log.Err(err))
				return err
			}
		} else {
			log.Error("find game data failed", log.Kv("server_id", msg.ServerId), log.Err(err))
			return err
		}
	}

	err = mdb.Collection("game").UpdateOne(
		ctx,
		bson.M{"server_id": msg.ServerId},
		bson.M{"$set": gameData},
	)

	if err != nil {
		log.Error("save game data failed", log.Kv("server_id", msg.ServerId), log.Err(err))
		return err
	}

	log.Info("save game data success", log.Kv("server_id", msg.ServerId))
	return nil
}

// deleteDataHandler 删除服务器通用数据
func (d *DAO) deleteDataHandler(ctx context.Context, req *actor.Message) error {
	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return fmt.Errorf("invalid db type")
	}

	msg := req.Data.(*g2m.G2M_DeleteData)
	var err error
	defer func() {
		req.Response(actor.RespMessage{Err: err, Data: nil})
	}()

	// 删除对应 server_id 的 GameData 文档
	err = mdb.Collection("game").DeleteOne(
		ctx,
		bson.M{"server_id": msg.ServerId},
	)

	if err != nil {
		log.Error("delete game data failed", log.Kv("server_id", msg.ServerId), log.Err(err))
		return err
	}

	log.Info("delete game data success", log.Kv("server_id", msg.ServerId))
	return nil
}

func (d *DAO) findDuplicateNames(ctx context.Context, req *actor.Message) error {
	var err error
	var respData g2m.M2G_FindDuplicateNamesData
	defer func() {
		req.Response(actor.RespMessage{Err: err, Data: &respData})
	}()
	msg := req.Data.(*g2m.G2M_FindDuplicateNamesData)

	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return fmt.Errorf("invalid db type")
	}
	var resultRaw []*dbstruct.UserDB

	err = mdb.Collection("players").FindAllByFilter(ctx, bson.M{"base.name": msg.GetName()}, &resultRaw)

	respData = g2m.M2G_FindDuplicateNamesData{
		Num: int64(len(resultRaw)),
	}
	if err != nil {
		return err
	}

	return nil
}

func (d *DAO) changeName(ctx context.Context, req *actor.Message) error {
	var err error
	msg := req.Data.(*g2m.G2M_ChangeNameData)

	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return fmt.Errorf("invalid db type")
	}

	err = mdb.Collection("players").UpdateOne(
		ctx,
		// 过滤条件：匹配玩家的旧名称
		bson.D{{Key: "base.name", Value: msg.GetOldName()}},
		// 更新操作：设置玩家的新名称
		bson.D{{
			Key:   "$set",
			Value: bson.D{{Key: "base.name", Value: msg.GetName()}},
		}},
	)
	if err != nil {
		return err
	}

	return nil
}

func saveUserToJson(uid uint64, b1, b2 []byte) error {
	u, err := dbutil.UnmarshalMerge(b1, b2)
	if err != nil {
		return saveUserToPB(uid, b1, b2)
	}
	data, err := json.Marshal(u)
	if err != nil {
		log.Error("save to json unmarshal err", log.Kv("uid", uid), log.Err(err))
		return saveUserToPB(uid, b1, b2)
	}
	fileName := fmt.Sprintf("u_%d_%d.json", uid, u.Base.LastSaveTime)
	if err := os.WriteFile(fileName, data, os.FileMode(0666)); err != nil {
		log.Error("save to json err", log.Kv("uid", uid), log.Err(err))
		return saveUserToPB(uid, b1, b2)
	}
	log.Warn("save to json file", log.Kv("uid", uid), log.Kv("file", fileName))
	return nil
}

func saveUserToPB(uid uint64, b1, b2 []byte) error {
	now := global.Now().Unix()
	fileName := fmt.Sprintf("base_%d_%d.pb", uid, now)
	var err error
	if err = os.WriteFile(fileName, b1, os.FileMode(0666)); nil != err {
		log.Error("write base pb file error", log.Kv("uid", uid), log.Kv("file", fileName), log.Err(err))
	}
	fileName1 := fmt.Sprintf("game_%d_%d.pb", uid, now)
	if err = os.WriteFile(fileName1, b2, os.FileMode(0666)); nil != err {
		log.Error("write game pb file error", log.Kv("uid", uid), log.Kv("file", fileName1), log.Err(err))
	}
	log.Warn("save to pb part", log.Kv("file", fileName), log.Kv("file1", fileName1))
	return err
}

// loadGlobalMailHandler 加载全局邮件数据
func (d *DAO) loadGlobalMailHandler(ctx context.Context, req *actor.Message) error {
	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return fmt.Errorf("invalid db type")
	}

	respData := &g2m.M2G_LoadGlobalMail{}
	var e error
	defer func() {
		req.Response(actor.RespMessage{Err: e, Data: respData})
	}()

	result := &dbstruct.MailList{}
	collection := mdb.Collection("gmail").GetCollection()
	err := collection.FindOne(ctx, bson.M{}).Decode(result)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			// 如果文档不存在，返回空数据
			respData.List = nil
			return nil
		}
		log.Error("load gmail data failed", log.Err(err))
		return err
	}

	respData.List = result
	return nil
}

// saveGlobalMailHandler 保存全局邮件数据
func (d *DAO) saveGlobalMailHandler(ctx context.Context, req *actor.Message) error {
	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return fmt.Errorf("invalid db type")
	}

	msg := req.Data.(*g2m.G2M_SaveGlobalMail)
	var err error
	defer func() {
		req.Response(actor.RespMessage{Err: err})
	}()

	collection := mdb.Collection("gmail").GetCollection()
	opts := options.Update().SetUpsert(true)
	update := bson.M{
		"$set": msg.List,
	}
	_, err = collection.UpdateOne(ctx, bson.M{}, update, opts)
	if err != nil {
		log.Error("save gmail data failed", log.Err(err))
		return err
	}

	log.Info("save gmail data success")
	return nil
}

// handlerLoadGuildData 加载公会数据
func (d *DAO) handlerLoadGuildData(ctx context.Context, req *actor.Message) error {
	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return fmt.Errorf("invalid db type")
	}

	respData := &g2m.M2G_LoadGuildData{}
	var e error
	defer func() {
		req.Response(actor.RespMessage{Err: e, Data: respData})
	}()

	result := &dbstruct.GuildDB{
		GuildDataMap: make(map[int64]*dbstruct.GuildData),
	}

	err := mdb.Collection("guilds").FindOne(
		ctx,
		bson.M{"_id": "guild_db"},
	).Decode(result)

	if errors.Is(err, mongo.ErrNoDocuments) {
		log.Info("no guild data found")
		data, err := proto.Marshal(result)
		if err != nil {
			log.Error("marshal empty guild data failed", log.Err(err))
			return err
		}
		respData.GuildData = data
		return nil
	} else if err != nil {
		log.Error("load guild data failed", log.Err(err))
		return err
	}

	data, err := proto.Marshal(result)
	if err != nil {
		log.Error("marshal guild data failed", log.Err(err))
		return err
	}

	respData.GuildData = data
	log.Info("load guild data success", log.Kv("guild_count", len(result.GuildDataMap)))
	return nil
}

// handlerSaveGuildData 保存单个公会数据
func (d *DAO) handlerSaveGuildData(ctx context.Context, req *actor.Message) error {
	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return fmt.Errorf("invalid db type")
	}

	msg := req.Data.(*g2m.G2M_SaveGuildData)
	var err error
	defer func() {
		req.Response(actor.RespMessage{Err: err})
	}()

	guildDB := &dbstruct.GuildDB{}
	err = proto.Unmarshal(msg.GuildData, guildDB)
	if err != nil {
		log.Error("unmarshal guild db failed", log.Err(err))
		return err
	}

	opts := options.Update().SetUpsert(true)
	err = mdb.Collection("guilds").UpdateOne(
		ctx,
		bson.M{"_id": "guild_db"},
		bson.M{"$set": guildDB},
		opts,
	)

	if err != nil {
		log.Error("save guild db failed", log.Err(err))
		return err
	}

	log.Info("save guild db success", log.Kv("total_guilds", len(guildDB.GuildDataMap)))
	return nil
}

// handlerDeleteGuildData 删除公会数据
func (d *DAO) handlerDeleteGuildData(ctx context.Context, req *actor.Message) error {
	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return fmt.Errorf("invalid db type")
	}

	msg := req.Data.(*g2m.G2M_DeleteGuildData)
	var err error
	defer func() {
		req.Response(actor.RespMessage{Err: err, Data: nil})
	}()

	err = mdb.Collection("guilds").DeleteOne(
		ctx,
		bson.M{"guild_id": msg.GuildId},
	)

	if err != nil {
		log.Error("delete guild data failed", log.Kv("guild_id", msg.GuildId), log.Err(err))
		return err
	}

	log.Info("delete guild data success", log.Kv("guild_id", msg.GuildId))
	return nil
}

// handlerLoadPaymentData 加载支付数据
func (d *DAO) handlerLoadPaymentData(ctx context.Context, req *actor.Message) error {

	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return fmt.Errorf("invalid db type")
	}

	respData := &g2m.M2G_LoadPaymentData{}
	var e error
	defer func() {
		req.Response(actor.RespMessage{Err: e, Data: respData})
	}()

	result := &dbstruct.PaymentDB{
		OrderDataMap: make(map[string]*public.PBOrderData),
	}

	err := mdb.Collection("payments").FindOne(
		ctx,
		bson.M{"_id": "payment_db"},
	).Decode(result)

	if errors.Is(err, mongo.ErrNoDocuments) {
		log.Info("no payment data found")
		data, err := proto.Marshal(result)
		if err != nil {
			log.Error("marshal empty payment data failed", log.Err(err))
			return err
		}
		respData.PaymentData = data
		return nil
	} else if err != nil {
		log.Error("load payment data failed", log.Err(err))
		return err
	}

	data, err := proto.Marshal(result)
	if err != nil {
		log.Error("marshal payment data failed", log.Err(err))
		return err
	}

	respData.PaymentData = data
	log.Info("load payment data success", log.Kv("order_count", len(result.OrderDataMap)))
	return nil
}

// handlerSavePaymentData 保存支付数据
func (d *DAO) handlerSavePaymentData(ctx context.Context, req *actor.Message) error {
	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return fmt.Errorf("invalid db type")
	}

	msg := req.Data.(*g2m.G2M_SavePaymentData)
	var err error
	defer func() {
		req.Response(actor.RespMessage{Err: err})
	}()

	paymentDB := &dbstruct.PaymentDB{}
	err = proto.Unmarshal(msg.PaymentData, paymentDB)
	if err != nil {
		log.Error("unmarshal payment db failed", log.Err(err))
		return err
	}

	opts := options.Update().SetUpsert(true)
	err = mdb.Collection("payments").UpdateOne(
		ctx,
		bson.M{"_id": "payment_db"},
		bson.M{"$set": paymentDB},
		opts,
	)

	if err != nil {
		log.Error("save payment db failed", log.Err(err))
		return err
	}

	log.Info("save payment db success", log.Kv("total_orders", len(paymentDB.OrderDataMap)))
	return nil
}

func (d *DAO) loadAllAccountMapHandler(ctx context.Context, req *actor.Message) error {
	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return fmt.Errorf("invalid db type")
	}

	//msg := req.Data.(*g2m.G2M_LoadAllAccountMap)
	var e error
	respData := &g2m.M2G_LoadAllAccountMap{
		Code: uint32(error_code.ERROR_PARAMS),
	}
	defer func() {
		req.Response(actor.RespMessage{Err: e, Data: respData})
	}()

	// 查找现有账号
	accountsColl := mdb.Collection("accounts")
	var arr []*accountInfo
	err := accountsColl.FindAll(
		ctx,
		&arr,
	) // TODO use FindAllByFilter use serverId

	if errors.Is(err, mongo.ErrNoDocuments) {
		respData.Code = uint32(error_code.ERROR_OK)
		return nil
	} else if err != nil {
		return fmt.Errorf("find account failed: %v", err)
	}
	respData.Code = uint32(error_code.ERROR_OK)
	respData.AccountMap = make(map[string]uint64, len(arr))
	for _, info := range arr {
		respData.AccountMap[info.Account] = info.Uid
	}
	return e
}

// loadArenaSystemHandler 加载竞技场数据
func (d *DAO) loadArenaSystemHandler(ctx context.Context, req *actor.Message) error {
	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return fmt.Errorf("invalid db type")
	}

	msg := req.Data.(*g2m.G2M_LoadData)
	respData := &g2m.M2G_LoadData{}
	var e error
	defer func() {
		req.Response(actor.RespMessage{Err: e, Data: respData})
	}()

	// 从 game 集合中获取单一的 GameData 文档
	result := &dbstruct.ArenaSystemDB{}
	err := mdb.Collection("arena").FindOne(
		ctx,
		bson.M{"server_id": msg.ServerId}, // 使用 server_id 作为查询条件
	).Decode(result)

	if errors.Is(err, mongo.ErrNoDocuments) {
		// 如果文档不存在，返回空数据
		respData.Data = nil
		return nil
	} else if err != nil {
		log.Error("load arena data failed", log.Kv("server_id", msg.ServerId), log.Err(err))
		return err
	}

	// 将 GameData 序列化为字节数组
	data, err := proto.Marshal(result)
	if err != nil {
		log.Error("marshal arena data failed", log.Kv("server_id", msg.ServerId), log.Err(err))
		return err
	}

	respData.Data = data
	return nil
}

// saveArenaSystemHandler 保存竞技场数据
func (d *DAO) saveArenaSystemHandler(ctx context.Context, req *actor.Message) error {
	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return fmt.Errorf("invalid db type")
	}

	msg := req.Data.(*g2m.G2M_SaveData)
	var err error
	defer func() {
		req.Response(actor.RespMessage{Err: err})
	}()

	// 解析 GameData
	arenaData := &dbstruct.ArenaSystemDB{}
	err = proto.Unmarshal(msg.Data, arenaData)
	if err != nil {
		log.Error("unmarshal arena data failed", log.Kv("server_id", msg.ServerId), log.Err(err))
		return err
	}

	// 使用 upsert 更新 GameData 文档
	err = mdb.Collection("arena").UpdateOne(
		ctx,
		bson.M{"server_id": msg.ServerId},
		bson.M{"$set": arenaData},
		options.Update().SetUpsert(true),
	)

	if err != nil {
		log.Error("save arena data failed", log.Kv("server_id", msg.ServerId), log.Err(err))
		return err
	}

	log.Info("save arena data success", log.Kv("server_id", msg.ServerId))
	return nil
}

// handlerLoadTipOffData 加载举报数据
func (d *DAO) handlerLoadTipOffData(ctx context.Context, req *actor.Message) error {

	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return fmt.Errorf("invalid db type")
	}

	respData := &g2m.M2G_LoadTipOffData{}
	var e error
	defer func() {
		req.Response(actor.RespMessage{Err: e, Data: respData})
	}()

	result := &dbstruct.TipOffDB{
		TipOffDatas: make([]*dbstruct.TipOffData, 0),
	}

	err := mdb.Collection("tipoff").FindOne(
		ctx,
		bson.M{"_id": "tipoff_db"},
	).Decode(result)

	if errors.Is(err, mongo.ErrNoDocuments) {
		log.Info("no tipoff data found")
		data, err := proto.Marshal(result)
		if err != nil {
			log.Error("marshal empty tipoff data failed", log.Err(err))
			return err
		}
		respData.TipOffData = data
		return nil
	} else if err != nil {
		log.Error("load tipoff data failed", log.Err(err))
		return err
	}

	data, err := proto.Marshal(result)
	if err != nil {
		log.Error("marshal tipoff data failed", log.Err(err))
		return err
	}

	respData.TipOffData = data
	log.Info("load tipoff data success", log.Kv("TipOffData_count", len(result.TipOffDatas)))
	return nil
}

// handlerSaveTipOffData 加载举报数据
func (d *DAO) handlerSaveTipOffData(ctx context.Context, req *actor.Message) error {
	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return fmt.Errorf("invalid db type")
	}

	msg := req.Data.(*g2m.G2M_SaveTipOffData)
	var err error
	defer func() {
		req.Response(actor.RespMessage{Err: err})
	}()

	tipOffDB := &dbstruct.TipOffDB{}
	err = proto.Unmarshal(msg.TipOffData, tipOffDB)
	if err != nil {
		log.Error("unmarshal tipoff db failed", log.Err(err))
		return err
	}

	opts := options.Update().SetUpsert(true)
	err = mdb.Collection("tipoff").UpdateOne(
		ctx,
		bson.M{"_id": "tipoff_db"},
		bson.M{"$set": tipOffDB},
		opts,
	)

	if err != nil {
		log.Error("save tipoff db failed", log.Err(err))
		return err
	}

	log.Info("save tipoff db success", log.Kv("total_tipOffs", len(tipOffDB.TipOffDatas)))
	return nil
}

// handlerLoadSeasonBuffData 加载赛季buff数据
func (d *DAO) handlerLoadSeasonBuffData(ctx context.Context, req *actor.Message) error {
	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return fmt.Errorf("invalid db type")
	}

	respData := &g2m.M2G_LoadSeasonBuffData{}
	var e error
	defer func() {
		req.Response(actor.RespMessage{Err: e, Data: respData})
	}()

	result := &dbstruct.SeasonBuffDB{
		Data: &public.PBSeasonBuff{},
	}

	err := mdb.Collection("season_buff").FindOne(
		ctx,
		bson.M{"_id": "season_buff_db"},
	).Decode(result)

	if errors.Is(err, mongo.ErrNoDocuments) {
		log.Info("no season buff data found")
		data, err := proto.Marshal(result)
		if err != nil {
			log.Error("marshal empty season buff data failed", log.Err(err))
			return err
		}
		respData.SeasonBuffData = data
		return nil
	} else if err != nil {
		log.Error("load season buff data failed", log.Err(err))
		return err
	}

	data, err := proto.Marshal(result)
	if err != nil {
		log.Error("marshal season buff data failed", log.Err(err))
		return err
	}

	respData.SeasonBuffData = data
	log.Info("load season buff data success", log.Kv("buff_id", result.Data.BuffId))
	return nil
}

// handlerSaveSeasonBuffData 保存赛季buff数据
func (d *DAO) handlerSaveSeasonBuffData(ctx context.Context, req *actor.Message) error {
	mdb, ok := d.Db.(*mongodb.Mongo)
	if !ok {
		return fmt.Errorf("invalid db type")
	}

	msg := req.Data.(*g2m.G2M_SaveSeasonBuffData)
	var err error
	defer func() {
		req.Response(actor.RespMessage{Err: err})
	}()

	seasonBuffDB := &dbstruct.SeasonBuffDB{}
	err = proto.Unmarshal(msg.SeasonBuffData, seasonBuffDB)
	if err != nil {
		log.Error("unmarshal season buff db failed", log.Err(err))
		return err
	}

	opts := options.Update().SetUpsert(true)
	err = mdb.Collection("season_buff").UpdateOne(
		ctx,
		bson.M{"_id": "season_buff_db"},
		bson.M{"$set": seasonBuffDB},
		opts,
	)

	if err != nil {
		log.Error("save season buff db failed", log.Err(err))
		return err
	}

	log.Info("save season buff db success",
		log.Kv("buff_id", seasonBuffDB.Data.BuffId),
		log.Kv("end_time", seasonBuffDB.Data.EndTime))
	return nil
}
