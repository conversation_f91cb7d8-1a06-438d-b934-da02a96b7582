package system

import (
	"context"
	"fmt"
	"liteframe/internal/game-logic/gameserver/logic/player"
	"liteframe/internal/game-logic/gameserver/logic/system/mail"
	"liteframe/pkg/sdk/pay"
	"strconv"
	"time"

	"liteframe/internal/common/constant/def"
	"liteframe/internal/common/protos/dbstruct"
	"liteframe/internal/common/table"
	"liteframe/internal/game-logic/gameserver/config"
	"liteframe/internal/game-logic/gameserver/game_def/actor_def"
	"liteframe/internal/game-logic/gameserver/global"
	"liteframe/internal/game-logic/gameserver/logic/system/dao"
	"liteframe/internal/game-logic/gameserver/logic/system/dao/dbhelper"
	"liteframe/pkg/actor"
	"liteframe/pkg/db"
	"liteframe/pkg/log"
	"liteframe/pkg/timer"

	"google.golang.org/protobuf/proto"
)

type logAdapter struct{}

func (logAdapter) Info(format string, v ...interface{}) {
	log.Info(fmt.Sprintf(format, v...))
}

func (logAdapter) Error(format string, v ...interface{}) {
	log.Error(fmt.Sprintf(format, v...))
}

func (logAdapter) Fatal(format string, v ...interface{}) {
	log.Fatal(fmt.Sprintf(format, v...))
}

// GameMgr  游戏管理器 非线程安全， 只在主线程调用！！！所有系统都在init里初始化！！！
type GameMgr struct {
	lastTime  time.Time
	startTime time.Time // 启服时间

	sysPids   []actor.PID
	tickCount uint64
	curTime   time.Time

	runnging bool

	data *dbstruct.GameData

	timerMgr *timer.TimerExManager
	dropMgr  *player.Drop

	payService pay.Service
}

func NewGameMgr(payService pay.Service) *GameMgr {
	return &GameMgr{
		startTime:  time.Now(),
		data:       &dbstruct.GameData{},
		timerMgr:   timer.NewTimerExManager(timer.Size(1024)),
		payService: payService,
	}
}

func (m *GameMgr) Init(conf config.Config) error {
	actor.SetLogger(logAdapter{})
	// monitor 先初始化
	mnt := NewMonitorActor()
	actor.RegisterActor(mnt, global.MaxOnline*2)
	m.sysPids = append(m.sysPids, mnt.PID())
	actor.SetCb(OnNewActor, OnDestroyActor)

	// db op
	mc := conf.DB.Mongo
	dc := &db.Config{
		Type:           "mongodb",
		Address:        mc.URL,
		Database:       mc.Database,
		Username:       mc.User,
		Password:       mc.Password,
		AuthSource:     mc.AuthSource,
		ConnectTimeout: mc.ConnectTimeout,
		OperateTimeout: mc.OperateTimeout,
	}

	mdb, err := dao.NewDao(dc)
	if err != nil {
		log.Error(err.Error())
		return err
	}

	actor.RegisterActor(mdb, global.MaxOnline)
	m.sysPids = append(m.sysPids, mdb.PID())

	pmSys := NewPaymentSystem(m.payService)
	actor.RegisterActor(pmSys, global.MaxOnline*2)
	m.sysPids = append(m.sysPids, pmSys.PID())
	err = pmSys.LoadPaymentSync()
	if err != nil {
		return err
	}

	// player system
	ps := NewPlayerSystem()
	actor.RegisterActor(ps, global.MaxOnline*2)
	m.sysPids = append(m.sysPids, ps.PID())

	gs := NewGuildSystem()
	actor.RegisterActor(gs, global.MaxOnline)
	m.sysPids = append(m.sysPids, gs.PID())
	err = gs.LoadGuildsSync()
	if err != nil {
		return err
	}

	// 初始化全局邮件系统
	gms := mail.NewMailMgr()
	gms.InitDb()
	actor.RegisterActor(gms, global.MaxOnline*2)
	m.sysPids = append(m.sysPids, gms.PID())
	if err != nil {
		return err
	}

	//// User Snap 系统
	//us := user_snap.NewUserSnap()
	//if err = actor.RegisterActor(us, global.MaxOnline); err != nil {
	//	return errors.Errorf("register user snap system failed: %v", err)
	//}
	//m.sysPids = append(m.sysPids, us.PID())
	//us.LoadAllUserSnapData()

	// 初始化竞技场系统
	arena := NewArenaSystem()
	actor.RegisterActor(arena, global.MaxOnline)
	m.sysPids = append(m.sysPids, arena.PID())
	err = arena.LoadArenaSystemSync()
	if err != nil {
		return err
	}
	log.Info("Arena system initialized")

	// 初始化举报系统
	ts := NewTipOffSystem()
	actor.RegisterActor(ts, global.MaxOnline)
	m.sysPids = append(m.sysPids, ts.PID())
	err = ts.LoadTipOffSync()
	if err != nil {
		return err
	}
	log.Info("tipOff system initialized")

	// 初始化赛季buff系统
	sb := NewSeasonBuffSystem()
	actor.RegisterActor(sb, global.MaxOnline)
	m.sysPids = append(m.sysPids, sb.PID())
	err = sb.LoadSeasonBuffSync()
	if err != nil {
		return err
	}
	log.Info("seasonBuff system initialized")

	// 初始化赛季系统
	ss := NewSeasonSystem()
	actor.RegisterActor(ss, global.MaxOnline)
	m.sysPids = append(m.sysPids, ss.PID())
	log.Info("season system initialized")

	m.runnging = true
	err = m.Load()
	if err != nil {
		return err
	}

	// 在Load后初始化赛季系统，此时开服时间已经加载
	ss.OnStarted()
	log.Info("season system started after loading server time")

	return nil
}

func (m *GameMgr) Load() error {
	data, err := dbhelper.LoadDataSync(actor_def.SystemPID, strconv.FormatUint(uint64(global.ServerId), 10))
	if err != nil {
		return err
	}

	if err := proto.Unmarshal(data, m.data); err != nil {
		return err
	}

	if m.data.OpenServerTime == 0 {
		m.data.OpenServerTime = global.OpenServerTime // 记录开服时间

	} else if m.data.OpenServerTime != global.OpenServerTime {
		log.Warn("openServerTime changed", log.Kv("openServerTime", m.data.OpenServerTime))
		// TODO: 处理开服时间不一致,先按照数据时间来算
		global.OpenServerTime = m.data.OpenServerTime
	}

	if global.ServerType == global.ServerTypeDevelop {
		global.SetTimeOffset(m.data.TimeOffset) // 时间偏移
		m.timerMgr.Run(global.Now(), 100)
	}

	return nil
}

func (m *GameMgr) Start() error {
	crossDayHour := table.GetTable().CrossDayHour
	m.timerMgr.AddTimerFunc(fmt.Sprintf("0 %d * * *", crossDayHour), m.crossDay)
	m.timerMgr.AddTimerFunc(fmt.Sprintf("0 %d * * 1", crossDayHour), m.crossWeek)
	m.timerMgr.AddTimerFunc(fmt.Sprintf("0 %d 1 * *", crossDayHour), m.crossMonth)

	return nil
}

func (m *GameMgr) Tick(t time.Time) bool {
	if !m.runnging {
		return false
	}

	m.timerMgr.Run(t, 100)

	m.curTime = t
	m.tickCount++

	m.tick100MilliSeconds()

	if m.tickCount%10 != 0 {
		return m.runnging
	}
	m.secondTick()

	if m.tickCount%100 != 0 {
		return m.runnging
	}
	m.tenSecondsTick()

	if m.tickCount%600 != 0 {
		return m.runnging
	}
	m.minuteTick()

	return m.runnging
}

func (m *GameMgr) tick100MilliSeconds() {
	actor.Send(actor_def.SystemPID, actor_def.PlayerSystemPID, &actor.Message{
		Id:   def.MsgId_Actor_ServerTick,
		Data: m.curTime,
	})
}

func (m *GameMgr) secondTick() {
	m.asyncBroadCastToSys(&actor.Message{Id: def.MsgId_Actor_Second})

	actor.AsyncRequest(actor_def.DBSystemPID, actor_def.DBSystemPID, &actor.Message{
		Id: def.MsgId_Mongo_Ping,
	}, func(msg actor.RespMessage) { // execute in dao actor !!! only use m.runnging
		if msg.Err != nil {
			m.runnging = false
			log.Fatal("db failed, exit game", log.Err(msg.Err))
		}
	})
}

func (m *GameMgr) tenSecondsTick() {
	actor.Send(actor_def.SystemPID, actor_def.MonitorSystemPID, &actor.Message{Id: def.MsgId_Actor_Monitor})
}

func (m *GameMgr) minuteTick() {

}

// asyncBroadCastToSys 异步发送消息给所有系统
func (m *GameMgr) asyncBroadCastToSys(msg *actor.Message) {
	for _, pid := range m.sysPids {
		actor.Send(actor_def.SystemPID, pid, msg)
	}
}

// requestAllSys 同步请求所有系统
func (m *GameMgr) requestAllSys(msg *actor.Message) {
	for _, pid := range m.sysPids {
		resp := actor.SyncRequest(actor_def.SystemPID, pid, msg)
		if resp.Err != nil {
			log.Info("request failed", log.Kv("pid", pid), log.Err(resp.Err))
		}
	}
}

// SetTimeStamp 设置服务器时间
func (m *GameMgr) SetTimeStamp(timestamp int64) {
	global.SetTimeStamp(timestamp)
	m.data.TimeOffset = global.TimeOffset()
	now := global.Now()
	//clientMgr.BroadcastAll(rpc_def.NotifyServerTime, &cs.SCServerTime{
	//	Code:      retcode.Code_OK,
	//	Timestamp: now.Unix(),
	//})
	dbhelper.SaveData(actor_def.DBSystemPID, strconv.FormatUint(uint64(global.ServerId), 10), m.data, func(err error) {
		if err != nil {
			log.Error("save data", log.Err(err))
		}
	})
	log.Warn("SetTimeStamp", log.Kv("timestamp", timestamp), log.Kv("now", now.String()))
}

// ResetTimeStamp 重置服务器时间 TODO data race
func (m *GameMgr) ResetTimeStamp() {
	global.ClearTimeOffset()
	m.data.TimeOffset = 0
	now := global.Now()
	//clientMgr.BroadcastAll(rpc_def.NotifyServerTime, &cs.SCServerTime{
	//	Code:      retcode.Code_OK,
	//	Timestamp: now.Unix(),
	//})
	dbhelper.SaveData(actor_def.DBSystemPID, strconv.FormatUint(uint64(global.ServerId), 10), m.data, func(err error) {
		if err != nil {
			log.Error("save data", log.Err(err))
		}
	})
	log.Warn("ResetTimeStamp", log.Kv("now", now.String()))
}

func (m *GameMgr) crossDay(nowUnix int64) {
	m.onCrossDay(true, nowUnix)
}

func (m *GameMgr) crossWeek(nowUnix int64) {
	m.onCrossWeek(true, nowUnix)
}

func (m *GameMgr) crossMonth(nowUnix int64) {
	m.onCrossMonth(true, nowUnix)
}

func (m *GameMgr) onCrossDay(natural bool, nowUnix int64) {
	if natural {
		actor.Send(actor_def.SystemPID, actor_def.PlayerSystemPID, &actor.Message{
			Id:   def.MsgId_Actor_CrossDay,
			Data: actor_def.CrossData{Natural: natural, NowUnix: nowUnix},
		})
	}
	log.Info("onCrossDay", log.Kv("natural", natural), log.Kv("nowUnix", nowUnix))
}

func (m *GameMgr) onCrossWeek(natural bool, nowUnix int64) {
	if natural {
		actor.Send(actor_def.SystemPID, actor_def.PlayerSystemPID, &actor.Message{
			Id:   def.MsgId_Actor_CrossWeek,
			Data: actor_def.CrossData{Natural: natural, NowUnix: nowUnix},
		})
	}
	log.Info("onCrossWeek", log.Kv("natural", natural), log.Kv("nowUnix", nowUnix))
}

func (m *GameMgr) onCrossMonth(natural bool, nowUnix int64) {
	if natural {
		actor.Send(actor_def.SystemPID, actor_def.PlayerSystemPID, &actor.Message{
			Id:   def.MsgId_Actor_CrossMonth,
			Data: actor_def.CrossData{Natural: natural, NowUnix: nowUnix},
		})
	}
	log.Info("onCrossMonth", log.Kv("natural", natural), log.Kv("nowUnix", nowUnix))
}

// ReloadTable 重载配置表 同步请求所有系统 最多挂起所有goroutine 100毫秒 ！！！只在主线程调用！！！
func (m *GameMgr) ReloadTable() {
	gs := actor.NewGroupSync(time.Millisecond * 100)
	gs.Add(1)
	resp := actor.SyncRequest(actor_def.SystemPID, actor_def.PlayerSystemPID, &actor.Message{
		Id:   def.MsgId_Actor_SyncConfig,
		Data: gs,
	})
	if resp.Err != nil {
		log.Error("sync config failed", log.Err(resp.Err))
	} else {
		gs.Done()
	}
	gs.Wait(func(b bool) {
		log.Info("group sync return", log.Kv("ret", b))
		if err := table.Reload(); err != nil {
			log.Error("reload table failed", log.Err(err))
		}
	})
}

func (m *GameMgr) Close() {
	m.runnging = false
	actor.Send(actor_def.SystemPID, actor_def.PlayerSystemPID, &actor.Message{Id: def.MsgId_Actor_StopGame})
	// TODO notify other system exit

	// TODO wait player and other system exit
	playerSystemExit := false
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute) // 等待1分钟保存数据 阻塞主goroutine
	defer cancel()
	for {
		select {
		case <-time.After(time.Second):
			if !playerSystemExit {
				resp := actor.SyncRequest(actor_def.SystemPID, actor_def.PlayerSystemPID,
					&actor.Message{Id: def.MsgId_Actor_PlayerSystem_Exit_Status})
				if resp.Err != nil {
					log.Error("request player system failed", log.Err(resp.Err))
					continue
				}
				playerSystemExit, _ = resp.Data.(bool)
			}

			if playerSystemExit { // TODO other
				log.Info("all player saved!")
				return // TODO TODEL
			}
		case <-ctx.Done():
			log.Error("wait player system exit timeout")
			return
		}
	}
}
