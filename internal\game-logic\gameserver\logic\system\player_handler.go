package system

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"liteframe/internal/common/error_code"
	"liteframe/internal/common/rpc_def"
	"liteframe/internal/game-logic/gameserver/game_def/actor_def"

	"liteframe/internal/common/protos/g2m"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/game-logic/gameserver/gateway"

	"google.golang.org/protobuf/proto"

	"liteframe/internal/common/constant/def"
	"liteframe/internal/game-logic/gameserver/game_def"
	"liteframe/internal/game-logic/gameserver/gameutil/metrics"
	"liteframe/internal/game-logic/gameserver/global"
	"liteframe/internal/game-logic/gameserver/logic/gm"
	"liteframe/internal/game-logic/gameserver/logic/player"
	"liteframe/internal/game-logic/gameserver/logic/system/dao/dbhelper"

	"liteframe/internal/common/protos/cs"
	"liteframe/pkg/actor"
	"liteframe/pkg/log"
)

const (
	// 每秒最大登录请求数
	maxLoginPerSecond = 50
)

func (s *playerSystem) loginHandler(ctx context.Context, req *actor.Message) error {
	cMsg := req.Data.(*gateway.ClientMessage)
	sid := req.Uid // login and logout uid is client's session id
	packet := cMsg.Packet

	msg := &cs.CL_LOGIN_REQ{}
	retMsg := &cs.LC_LOGIN_RET{}

	// 增加当前秒的登录计数
	s.loginCount++

	// 检查是否超过每秒限制人数
	if s.loginCount > maxLoginPerSecond {
		log.Warn("server is busy, too many logins per second",
			log.Kv("session_id", sid),
			log.Kv("login_count", s.loginCount))
		retMsg.Result = int32(error_code.Error_DisConnectCode_0011) // 服务器爆满，请稍后再试
		clientMgr.SendPBMessage(rpc_def.LC_LOGIN_RET, sid, retMsg)
		return fmt.Errorf("server busy, login rate exceeds limit")
	}

	if err := proto.Unmarshal(packet.Data, msg); err != nil {
		clientMgr.SendPBMessage(rpc_def.LC_LOGIN_RET, sid, retMsg)
		log.Error("unmarshal failed", log.Kv("session_id", sid), log.Err(err))
		return err
	}
	strs := strings.Split(msg.Token, ".")
	account := strs[3]

	if _, ok := s.sessions[sid]; ok { // same session  rcv multi login
		return fmt.Errorf("sid %d rcv repeated login", sid)
	}

	if len(account) == 0 { // account invalid
		clientMgr.SendPBMessage(rpc_def.LC_LOGIN_RET, sid, retMsg)
		err := fmt.Errorf("session id %d account empty", sid)
		log.Error("account empty", log.Err(err))
		return err
	}

	// 验证token是否与prepareLoginCache中的匹配
	//loginInfo, exists := s.prepareLoginCache[account]
	//if !exists {
	//	log.Error("login failed: account not prepared", log.Kv("account", account), log.Kv("sid", sid))
	//	retMsg.Result = int32(error_code.ERROR_ACCOUNT_EMPTY) // 使用已定义的错误码
	//	clientMgr.SendPBMessage(rpc_def.LC_LOGIN_RET, sid, retMsg)
	//	return fmt.Errorf("account not prepared for login: %s", account)
	//}
	//
	//// 验证token
	//if msg.Token != loginInfo.Token {
	//	log.Error("login failed: token mismatch", log.Kv("account", account), log.Kv("sid", sid))
	//	retMsg.Result = int32(error_code.ERROR_ACCOUNT_EMPTY) // 使用已定义的错误码
	//	clientMgr.SendPBMessage(rpc_def.LC_LOGIN_RET, sid, retMsg)
	//	return fmt.Errorf("token verification failed for account: %s", account)
	//}
	//
	//// 检查缓存是否过期
	//if time.Since(loginInfo.CreateTime).Seconds() > prepareLoginCacheExpireSec {
	//	log.Error("login failed: prepare login cache expired", log.Kv("account", account), log.Kv("sid", sid))
	//	retMsg.Result = int32(error_code.ERROR_ACCOUNT_EMPTY) // 使用已定义的错误码
	//	clientMgr.SendPBMessage(rpc_def.LC_LOGIN_RET, sid, retMsg)
	//	delete(s.prepareLoginCache, account) // 清理过期缓存
	//	return fmt.Errorf("prepare login cache expired for account: %s", account)
	//}
	//
	//// 验证通过后移除缓存中的token
	//delete(s.prepareLoginCache, account)

	s.addSession(sid, account)
	log.Info("rcv login",
		log.Kv("account", account),
		log.Kv("sid", sid),
	)

	loginFunc := func(uid uint64) {
		if uid == 0 {
			log.Error("load invalid uid 0", log.Kv("account", account))
			return
		}
		if clientMgr != nil && clientMgr.GetClient(sid) == nil { // gateway offline
			log.Error("login failed gateway offline", log.Kv("account", account), log.Kv("sid", sid))
			return
		}
		clientMgr.GetClient(sid).SetUid(uid)

		info := s.sendToPlayer(uid, req)
		s.addAccountMap(account, uid)
		// update playerInfo
		info.account = account
		info.cid = sid
		info.st = ps_login

		now := global.Now().Unix()
		s.changeList.PushBack(uid, now)
		log.Info("login dispatch to player", log.Kv("account", account), log.Kv("sid", sid), log.Kv("uid", uid))
	}

	uid := s.accounts[account]
	if uid == 0 { // create account
		dbhelper.AccountLogin(s.PID(), account, uint64(global.ServerId), func(d *g2m.M2G_LoadCreateUid, err error) {
			if err != nil {
				clientMgr.SendPBMessage(rpc_def.LC_LOGIN_RET, sid, retMsg)
				s.removeSession(sid)
				log.Error("account login failed", log.Err(err))
				return
			}
			loginFunc(d.Uid)
		})
		return nil
	}
	loginFunc(uid)

	return nil
}

func (s *playerSystem) prepareLoginHandler(ctx context.Context, req *actor.Message) error {
	defer req.Response(actor.RespMessage{})

	// 获取请求参数
	prepareLoginReq, ok := req.Data.(*game_def.PrepareLoginRequest)
	if !ok {
		log.Error("invalid prepare login request data type", log.Kv("data_type", fmt.Sprintf("%T", req.Data)))
		return fmt.Errorf("invalid prepare login request data type")
	}

	account := prepareLoginReq.Account
	token := prepareLoginReq.Token

	if account == "" || token == "" {
		log.Error("prepare login failed: empty account or token", log.Kv("account", account))
		return fmt.Errorf("account and token are required")
	}

	// 缓存账号和令牌
	s.prepareLoginCache[account] = &prepareLoginInfo{
		Token:      token,
		CreateTime: time.Now(),
	}
	log.Info("prepare login success", log.Kv("account", account))

	return nil
}

func (s *playerSystem) logoutHandler(ctx context.Context, req *actor.Message) error {
	defer req.Response(actor.RespMessage{})
	sid := req.Uid

	accInfo, ok := s.sessions[sid]
	if !ok { // already delete
		log.Info("already logout", log.Kv("sid", sid))
		return nil
	}
	s.removeSession(sid)
	account := accInfo.account

	notifyFunc := func(uid uint64) {
		info := s.actors[uid]
		if info == nil { //can't be here
			log.Error("player info nil", log.Kv("account", account), log.Kv("uid", uid))
			return
		}

		if err := actor.Send(s.PID(), info.PID(), &actor.Message{
			Id:  uint32(def.MsgId_Gate_Logout),
			Uid: sid,
		}); err != nil {
			log.Error("send to player MsgId_Gate_Logout failed", log.Err(err))
		}

		log.Info("rcv logout",
			log.Kv("uid", uid),
			log.Kv("sid", sid),
			log.Kv("st", info.st),
		)
	}

	uid, ok := s.accounts[account]
	if ok {
		notifyFunc(uid)
	}

	return nil
}

// playerOnline 登录成功后 player 同步 playerSystem
func (s *playerSystem) playerOnlineHandler(ctx context.Context, req *actor.Message) error {
	uid := req.Uid
	info := s.actors[uid]
	gInfo := req.Data.(*game_def.ClientInfo)
	cid := gInfo.Cid
	if info == nil { // can't be here
		log.Fatal("login return info nil",
			log.Kv("uid", uid),
			log.Kv("cid", cid))
		return nil
	}

	info.st = ps_online
	info.cid = cid
	info.loginIdx = s.loginIndex
	s.removeOffline(uid) // remove offline
	s.onlineActors[uid] = info
	metrics.SetOnlineGauge(len(s.onlineActors))

	s.loginIndex++
	log.Info("player online", log.Kv("uid", uid), log.Kv("cid", info.cid))
	log.Info("Player online statistics",
		log.Kv("event", "player_login"),
		log.Kv("uid", uid),
		log.Kv("current_online_count", len(s.onlineActors)))
	return nil
}

// playerOffline 玩家下线后同步playerSystem，更新system 缓存数据, 处理等待退出而阻塞的登录
func (s *playerSystem) playerOfflineHandler(ctx context.Context, req *actor.Message) error {
	defer req.Response(actor.RespMessage{})
	uid := req.Uid
	sid := req.Data.(uint64)
	s.removeSession(sid) // 移除session 信息 处理未通过logout登出的玩家退出，比如gateway close/顶号踢人等

	info := s.actors[uid]
	if info == nil { // can't be here
		log.Fatal("logout return info nil",
			log.Kv("uid", uid))
		return nil
	}
	info.st = ps_offline
	info.cid = 0
	delete(s.onlineActors, uid)
	metrics.SetOnlineGauge(len(s.onlineActors))
	s.addOffline(uid, info)
	log.Info("player offline", log.Kv("uid", uid))
	log.Info("Player online statistics",
		log.Kv("event", "player_logout"),
		log.Kv("uid", uid),
		log.Kv("current_online_count", len(s.onlineActors)))
	if s.exiting { // system exit clear account and offline
		actor.StopActor(info.pid)
		delete(s.actors, uid)
		s.removeOffline(uid)
		return nil
	}

	return nil
}

func (s *playerSystem) createPlayerActor(uid uint64) *playerInfo {
	p := player.NewPlayer(uid, false)
	info := &playerInfo{
		uid: uid,
		st:  ps_offline,
		pid: p.PID(),
	}
	s.actors[uid] = info

	actor.RegisterActor(p, player.MaxMailSize)
	actor.Send(s.PID(), p.PID(), &actor.Message{
		Id:  uint32(def.MsgId_Actor_InitPlayer),
		Uid: uid,
	})
	s.addOffline(uid, info) // 登录后再删除 避免 不存在的玩家 不会被清除
	return info
}

func (s *playerSystem) sendToPlayer(uid uint64, msg *actor.Message) *playerInfo {
	info := s.actors[uid]
	if info == nil {
		info = s.createPlayerActor(uid)
		log.Info("create player actor", log.Kv("uid", msg.Uid), log.Kv("msg_id", msg.Id))
	}

	actor.Send(s.PID(), info.PID(), msg)
	return info
}

func (s *playerSystem) clientMsgDispatch(msg *actor.Message) {
	uid := msg.Uid
	if uid == 0 {
		log.Error("invalid uid 0", log.Kv("mid", msg.Id), log.Kv("from", msg.From()))
		return
	}
	info := s.sendToPlayer(uid, msg)
	now := global.Now().Unix()
	s.changeList.PushBack(uid, now)

	if info.st == ps_offline { // 更新离线玩家交互时间
		s.offlineList.PushBack(uid, now)
	}

	metrics.AddRpcMsgCount(msg.Id)
	log.Debug("send to player", log.Kv("uid", msg.Uid), log.Kv("id", msg.Id))
}

func (s *playerSystem) gmHandle(ctx context.Context, msg *actor.Message) error {
	if msg.Data == nil {
		msg.Response(actor.RespMessage{Err: errors.New("no data")})
		return nil
	}
	gmMessage, ok := msg.Data.(*game_def.GmCmd)
	if !ok {
		msg.Response(actor.RespMessage{Err: errors.New("no gmMessage")})
		return nil
	}

	switch gmMessage.CmdId {
	case uint32(gm.OnlinePlayer):
		data := make(map[uint64]string, len(s.onlineActors))
		for _, v := range s.onlineActors {
			data[v.uid] = v.account
		}
		msg.Response(actor.RespMessage{Data: data})
	case uint32(gm.LoadPlayerByAccount):
		account := gmMessage.Params.(string)
		uid, ok := s.accounts[account]
		if !ok {
			msg.Response(actor.RespMessage{Err: errors.New("account not found")})
			return nil
		}
		msg.Data = &game_def.GmCmd{
			CmdId:  uint32(gm.LoadPlayer),
			Params: nil,
		}
		s.sendToPlayer(uid, msg)

	default:
		s.sendToPlayer(msg.Uid, msg)
		//actor.AsyncRequest(s.PID(), actor.NewPID(msg.Uid, "player"), req, func(respMsg actor.RespMessage) {
		//	msg.Response(actor.RespMessage{Err: respMsg.Err, Data: respMsg.Data})
		//})
	}

	return nil
}

func (s *playerSystem) gmtHandle(ctx context.Context, msg *actor.Message) error {
	if msg.Data == nil {
		msg.Response(actor.RespMessage{Err: errors.New("no data")})
		return nil
	}
	s.sendToPlayer(msg.Uid, msg)
	return nil
}

func (s *playerSystem) serverTickHandler(ctx context.Context, data *actor.Message) error {
	s.saveIndex = (s.saveIndex + 1) % allSaveSeconds
	t := data.Data.(time.Time)
	s.Tick(t)

	return nil
}

func (s *playerSystem) secondHandler(ctx context.Context, data *actor.Message) error {
	// 在线玩家广播
	for _, info := range s.onlineActors {
		if info.st == ps_offline {
			continue
		}
		actor.Send(s.PID(), info.PID(), data)
	}

	// 每分钟记录一次，使用当前时间的秒数判断
	currentTime := time.Now()
	if currentTime.Second() == 0 {
		serverID := fmt.Sprintf("%d", global.ServerId)
		onlineCount := len(s.onlineActors)

		// 记录在线人数心跳日志
		if err := log.LogHeartbeat(serverID, onlineCount); err != nil {
			log.Error("Failed to write heartbeat log", log.Err(err))
		} else {
			log.Info("Heartbeat log recorded",
				log.Kv("server_id", serverID),
				log.Kv("online_count", onlineCount),
				log.Kv("time", currentTime.Format("2006-01-02 15:04:05")))
		}
	}

	return nil
}

func (s *playerSystem) syncHandler(ctx context.Context, data *actor.Message) error {
	defer data.Response(actor.RespMessage{})
	gs := data.Data.(*actor.GroupSync)

	gs.Add(1)

	for _, info := range s.actors {
		gs.Add(1)
		actor.Send(s.PID(), info.PID(), &actor.Message{
			Id:   uint32(actor.SysId_Sync),
			Data: gs,
		})
	}

	gs.Done()
	return nil
}

//func (s *playerSystem) onClientCloseHandler(ctx context.Context, data *actor.Message) error {
//	for _, info := range s.actors {
//		if info.st == ps_offline { // 离线玩家不处理
//			continue
//		}
//
//		if info.gid != data.Uid { // 过滤不是关闭gateway的玩家
//			continue
//		}
//		actor.Send(s.PID(), info.PID(), data)
//	}
//	return nil
//}

func (s *playerSystem) crossDayHandler(ctx context.Context, data *actor.Message) error {
	for _, info := range s.actors {
		if info.st == ps_offline {
			continue
		}
		if err := actor.Send(s.PID(), info.PID(), data); err != nil {
			return err
		}
	}
	return nil
}

func (s *playerSystem) crossWeekHandler(ctx context.Context, data *actor.Message) error {
	for _, info := range s.actors {
		if info == nil {
			continue
		}
		if info.st == ps_offline {
			continue
		}
		if err := actor.Send(s.PID(), info.PID(), data); err != nil {
			return err
		}
	}
	return nil
}

func (s *playerSystem) crossMonthHandler(ctx context.Context, data *actor.Message) error {
	for _, info := range s.actors {
		if info == nil {
			continue
		}
		if info.st == ps_offline {
			continue
		}
		if err := actor.Send(s.PID(), info.PID(), data); err != nil {
			return err
		}
	}
	return nil
}

// sendToOnlinePlayerHandler 发送消息给在线玩家 不在线 不处理
func (s *playerSystem) sendToOnlinePlayerHandler(ctx context.Context, data *actor.Message) error {
	defer data.Response(actor.RespMessage{})
	info := s.onlineActors[data.Uid]
	if info == nil || info.st != ps_online { // 玩家不在线, 不需处理
		log.Debug("player offline", log.Kv("uid", data.Uid))
		return nil
	}
	s.sendToPlayer(data.Uid, data)
	return nil
}

func (s *playerSystem) stopGameHandler(ctx context.Context, data *actor.Message) error {
	defer data.Response(actor.RespMessage{})
	s.exiting = true
	for _, info := range s.actors {
		if info == nil {
			continue
		}
		if err := actor.Send(s.PID(), info.PID(), data); err != nil {
			return err
		}
	}
	return nil
}

func (s *playerSystem) exitStatusHandler(ctx context.Context, data *actor.Message) error {
	defer data.Response(actor.RespMessage{
		Data: len(s.actors) == 0,
	})
	return nil
}

func (s *playerSystem) guildSystemMessageHandler(ctx context.Context, msg *actor.Message) error {
	if msg == nil {
		log.Error("msg nil")
		return nil
	}
	if msg.Data == nil {
		log.Error("msg.Data nil")
		return nil
	}

	switch data := msg.Data.(type) {
	case *cs.G2PCreateGuildRsp: // 创建公会响应
		// 使用msg.Uid或者消息中的PlayerId字段
		playerId := msg.Uid

		// 创建转发消息
		createGuildRspMsg := &actor.Message{
			Id:   uint32(def.MsgId_Actor_PlayerSystem2Player),
			Uid:  playerId,
			Data: data,
		}
		// 发送消息给玩家
		s.sendToPlayer(playerId, createGuildRspMsg)

	case *cs.G2PGuildCreated: // 玩家创建公会成功
		// 获取玩家ID，从消息中获取
		playerId := msg.Uid

		guildCreatedMsg := &actor.Message{
			Id:   uint32(def.MsgId_Actor_PlayerSystem2Player),
			Uid:  playerId,
			Data: data,
		}
		// 发送消息给玩家
		s.sendToPlayer(playerId, guildCreatedMsg)

	case *cs.G2PGuildDonateRsp: // 捐献响应
		playerId := msg.Uid // 从消息中获取玩家ID
		// 创建一个转发消息
		donateRspMsg := &actor.Message{
			Id:   uint32(def.MsgId_Actor_PlayerSystem2Player),
			Uid:  playerId,
			Data: data,
		}
		// 发送消息给玩家
		s.sendToPlayer(playerId, donateRspMsg)
		return nil

	case *cs.G2PJoinGuildRsp: // 加入公会响应
		// 获取玩家ID，从消息中获取
		playerId := msg.Uid

		joinGuildRspMsg := &actor.Message{
			Id:   uint32(def.MsgId_Actor_PlayerSystem2Player),
			Uid:  playerId,
			Data: data,
		}
		// 发送消息给玩家
		s.sendToPlayer(playerId, joinGuildRspMsg)
	case *cs.G2PSNotifyPlayerPositionChanged:
		s.notifyPlayerPositionChangedHandle(msg.Data.(*cs.G2PSNotifyPlayerPositionChanged))
	case *cs.G2PSNotifyRemovePendingApply:
		s.notifyRemovePendingApplyHandle(msg.Data.(*cs.G2PSNotifyRemovePendingApply))
	case *cs.G2PSUpdateGuildMembersReq:
		s.updateGuildMembersHandle(msg.Data.(*cs.G2PSUpdateGuildMembersReq))
	case *cs.G2PSBatchUpdatePlayerGuildStatusReq:
		s.updateBatchPlayerGuildStatusHandle(msg.Data.(*cs.G2PSBatchUpdatePlayerGuildStatusReq))
	case *cs.G2PSNotifyPlayerJoinedGuild:
		s.notifyPlayerJoinedGuildHandle(msg.Data.(*cs.G2PSNotifyPlayerJoinedGuild))
	case *cs.G2PSNotifyPlayerLeftGuild:
		s.notifyPlayerLeftGuildHandle(msg.Data.(*cs.G2PSNotifyPlayerLeftGuild))
	default:
		log.Error("msg.Data type not support", log.Kv("type", fmt.Sprintf("%T", msg.Data)))
	}

	return nil
}

// 处理更新公会成员状态的请求
func (s *playerSystem) updateGuildMembersHandle(req *cs.G2PSUpdateGuildMembersReq) {
	rsp := &cs.PS2GUpdateGuildMembersRsp{
		Code:       0,
		GuildId:    req.GuildId,
		FailedUids: make([]uint64, 0),
	}

	// 遍历所有需要更新的玩家
	for _, uid := range req.Uids {
		updateMsg := &cs.PS2PUpdateGuildStatusReq{
			GuildId: req.GuildId,
		}

		s.sendToPlayer(uid, &actor.Message{
			Id:   uint32(def.MsgId_Actor_PlayerSystem2Player),
			Uid:  uid,
			Data: updateMsg,
		})
	}

	// 回复guild system
	err := actor.Send(s.PID(), actor_def.GuildSystemPID, &actor.Message{
		Id:   uint32(def.MsgId_Actor_PlayerSystem2GuildSystem),
		Data: rsp,
	})
	if err != nil {
		log.Error("updateGuildMembersHandle failed to send to guild system",
			log.Kv("from", s.PID()),
			log.Kv("to", actor_def.GuildSystemPID),
			log.Kv("guildId", req.GuildId),
			log.Err(err))
	}
}

// 处理批量更新玩家公会状态的请求
func (s *playerSystem) updateBatchPlayerGuildStatusHandle(req *cs.G2PSBatchUpdatePlayerGuildStatusReq) {
	rsp := &cs.PS2GUpdateGuildMembersRsp{
		Code:       0,
		GuildId:    req.GuildId,
		FailedUids: make([]uint64, 0),
	}

	// 根据操作类型创建相应的更新消息
	switch req.ActionType {
	case public.GuildSystemInternalActionType_INTERNAL_ACTION_DISMISS_GUILD_MEMBERS:
		// 公会解散，通知所有成员
		for _, uid := range req.Uids {
			// 使用通用更新通知消息
			updateMsg := &cs.G2PGuildGeneralUpdateNtf{
				UpdateType: public.GuildUpdateType_GuildUpdateType_GUILD_DISMISSED,
				GuildId:    req.GuildId,
				TargetUid:  uid,
			}

			// 发送解散通知
			s.sendToPlayer(uid, &actor.Message{
				Id:   uint32(def.MsgId_Actor_PlayerSystem2GuildSystem),
				Uid:  uid,
				Data: updateMsg,
			})

			// 更新玩家的公会归属信息
			affiliationUpdate := &cs.PS2PUpdateGuildAffiliationReq{
				GuildId:  0, // 设置为0表示玩家不再属于任何公会
				Position: 0, // 清除职位
				Opt:      public.GuildOpt_GuildOpt_Dismiss,
			}

			s.sendToPlayer(uid, &actor.Message{
				Id:   uint32(def.MsgId_Actor_PlayerSystem2Player),
				Uid:  uid,
				Data: affiliationUpdate,
			})
		}

		log.Info("Guild disbanded and member status is updated in batches",
			log.Kv("guildId", req.GuildId),
			log.Kv("memberCount", len(req.Uids)))

	case public.GuildSystemInternalActionType_INTERNAL_ACTION_GUILD_LEVEL_UP_NOTIFY_MEMBERS:
		// 公会升级，通知所有成员
		for _, uid := range req.Uids {
			// 使用通用更新通知消息
			updateMsg := &cs.G2PGuildGeneralUpdateNtf{
				UpdateType:    public.GuildUpdateType_GuildUpdateType_GUILD_LEVEL_UP,
				GuildId:       req.GuildId,
				NewGuildLevel: req.NewGuildLevel,
				TargetUid:     uid,
			}

			s.sendToPlayer(uid, &actor.Message{
				Id:   uint32(def.MsgId_Actor_PlayerSystem2Player),
				Uid:  uid,
				Data: updateMsg,
			})
		}

		log.Info("Guild upgrade and batch update member status",
			log.Kv("guildId", req.GuildId),
			log.Kv("newLevel", req.NewGuildLevel),
			log.Kv("newMaxMembers", req.NewMaxMembers))

	default:
		log.Error("Unknown bulk update operation type",
			log.Kv("guildId", req.GuildId),
			log.Kv("actionType", req.ActionType))
		rsp.Code = uint32(error_code.ERROR_PARAMS)
	}

	// 回复guild system
	err := actor.Send(s.PID(), actor_def.GuildSystemPID, &actor.Message{
		Id:   uint32(def.MsgId_Actor_PlayerSystem2GuildSystem),
		Data: rsp,
	})
	if err != nil {
		log.Error("updateBatchPlayerGuildStatusHandle failed to send to guild system",
			log.Kv("from", s.PID()),
			log.Kv("to", actor_def.GuildSystemPID),
			log.Kv("guildId", req.GuildId),
			log.Err(err))
	}
}

// 处理通知玩家加入公会的请求
func (s *playerSystem) notifyPlayerJoinedGuildHandle(req *cs.G2PSNotifyPlayerJoinedGuild) {
	updateMsg := &cs.PS2PUpdateGuildAffiliationReq{
		GuildId:   req.GuildId,
		GuildName: req.GuildName,
		Position:  req.Position,
		Opt:       public.GuildOpt_GuildOpt_ApplyMgrAgree,
	}

	s.sendToPlayer(req.Uid, &actor.Message{
		Id:   uint32(def.MsgId_Actor_PlayerSystem2Player),
		Uid:  req.Uid,
		Data: updateMsg,
	})

	log.Info("notify player joined guild",
		log.Kv("uid", req.Uid),
		log.Kv("guildId", req.GuildId),
		log.Kv("position", req.Position))
}

// 处理通知玩家离开公会的请求
func (s *playerSystem) notifyPlayerLeftGuildHandle(req *cs.G2PSNotifyPlayerLeftGuild) {
	updateMsg := &cs.PS2PUpdateGuildAffiliationReq{
		GuildId:  0, // 设置为0表示离开公会
		Position: 0,
	}

	s.sendToPlayer(req.Uid, &actor.Message{
		Id:   uint32(def.MsgId_Actor_PlayerSystem2Player),
		Uid:  req.Uid,
		Data: updateMsg,
	})

	log.Info("notify player left guild",
		log.Kv("uid", req.Uid),
		log.Kv("guildId", req.GuildId))
}

// 处理通知玩家职位变更的请求
func (s *playerSystem) notifyPlayerPositionChangedHandle(req *cs.G2PSNotifyPlayerPositionChanged) {
	updateMsg := &cs.PS2PUpdateGuildAffiliationReq{
		GuildId:  req.GuildId,
		Position: req.NewPosition,
		Opt:      req.Opt,
	}

	s.sendToPlayer(req.Uid, &actor.Message{
		Id:   uint32(def.MsgId_Actor_PlayerSystem2Player),
		Uid:  req.Uid,
		Data: updateMsg,
	})

	log.Info("notify player position changed",
		log.Kv("uid", req.Uid),
		log.Kv("guildId", req.GuildId),
		log.Kv("newPosition", req.NewPosition))
}

// 处理移除待处理申请的请求
func (s *playerSystem) notifyRemovePendingApplyHandle(req *cs.G2PSNotifyRemovePendingApply) {
	updateMsg := &cs.PS2PRemovePendingApplyReq{
		GuildId: req.GuildId,
		Opt:     req.Opt,
	}

	s.sendToPlayer(req.Uid, &actor.Message{
		Id:   uint32(def.MsgId_Actor_PlayerSystem2Player),
		Uid:  req.Uid,
		Data: updateMsg,
	})

	log.Info("notify player to remove pending guild application",
		log.Kv("uid", req.Uid),
		log.Kv("guildId", req.GuildId))
}

func (s *playerSystem) paymentSystemMessageHandler(ctx context.Context, msg *actor.Message) error {
	if msg == nil {
		log.Error("msg nil")
		return nil
	}
	if msg.Data == nil {
		log.Error("msg.Data nil")
		return nil
	}
	switch msg.Data.(type) {
	case *cs.PM2PSDeliverReq:
		s.deliverHandler(msg.Data.(*cs.PM2PSDeliverReq))
	default:
		log.Error("msg.Data type not support")
	}
	return nil
}

func (s *playerSystem) deliverHandler(req *cs.PM2PSDeliverReq) {
	if req.Goods != nil {
		s.deliverPayHandler(req)
	}
	if req.Quest != nil {
		s.deliverQuestHandler(req)
	}
}

func (s *playerSystem) deliverPayHandler(req *cs.PM2PSDeliverReq) {
	rsp := &cs.PS2PMDeliverRsp{
		Code:  0,
		Goods: req.Goods,
	}

	msg := &cs.PS2PDeliverReq{
		Goods: req.Goods,
	}

	s.sendToPlayer(req.Goods.PlayerId, &actor.Message{
		Id:   uint32(def.MsgId_Actor_PlayerSystem2Player),
		Uid:  req.Goods.PlayerId,
		Data: msg,
	})

	actor.Send(s.PID(), actor_def.PaymentSystemPID, &actor.Message{
		Id:   uint32(def.MsgId_Actor_PlayerSystem2PaymentSystem),
		Data: rsp,
	})
	log.Info("deliverHandler", log.Kv("uid", req.Goods.PlayerId))
}

func (s *playerSystem) deliverQuestHandler(req *cs.PM2PSDeliverReq) {
	msg := &cs.PS2PQuestReq{
		Quest: req.Quest,
	}
	s.sendToPlayer(req.Quest.PlayerId, &actor.Message{
		Id:   uint32(def.MsgId_Actor_PlayerSystem2Player),
		Uid:  req.Quest.PlayerId,
		Data: msg,
	})
}

// seasonBuffSystemMessageHandler 处理来自赛季Buff系统的消息
func (s *playerSystem) seasonBuffSystemMessageHandler(ctx context.Context, msg *actor.Message) error {
	if msg == nil || msg.Data == nil {
		log.Error("seasonBuffSystemMessageHandler: msg or msg.Data is nil")
		return nil
	}

	// 直接解析SB2PSSeasonBuffSync消息
	syncMsg, ok := msg.Data.(*cs.SB2PSSeasonBuffSync)
	if !ok {
		log.Error("seasonBuffSystemMessageHandler: invalid message type, expect *cs.SB2PSSeasonBuffSync",
			log.Kv("actual", fmt.Sprintf("%T", msg.Data)))
		return nil
	}

	// 构造转发给玩家模块的消息
	response := &cs.PS2PSeasonBuffSync{
		Info: syncMsg.Info,
	}

	// 广播给所有在线玩家
	for uid, info := range s.onlineActors {
		// 获取玩家的PID
		pid := info.pid

		// 发送给每个在线玩家
		err := actor.Send(actor_def.PlayerSystemPID, pid, &actor.Message{
			Id:   uint32(def.MsgId_Actor_SeasonBuffSystem2Player),
			Data: response,
		})
		if err != nil {
			log.Error("send season buff sync to player failed",
				log.Kv("uid", uid),
				log.Err(err))
		}
	}

	return nil
}

// seasonSystemMessageHandler 处理来自赛季系统的消息
func (s *playerSystem) seasonSystemMessageHandler(ctx context.Context, msg *actor.Message) error {
	if msg == nil || msg.Data == nil {
		log.Error("seasonSystemMessageHandler: msg or msg.Data is nil")
		return nil
	}

	// 解析SeasonResetReq消息
	resetMsg, ok := msg.Data.(*cs.SeasonResetReq)
	if !ok {
		log.Error("seasonSystemMessageHandler: invalid message type, expect *cs.SeasonResetReq",
			log.Kv("actual", fmt.Sprintf("%T", msg.Data)))
		return nil
	}

	log.Info("Processing season reset from SeasonSystem",
		log.Kv("old_season_id", resetMsg.OldId),
		log.Kv("new_season_id", resetMsg.NewId))

	// 发送给所有玩家（包括离线玩家）
	s.broadcastSeasonResetToAllPlayers(resetMsg)

	return nil
}

// broadcastToAllPlayers 广播消息给所有在线玩家
func (s *playerSystem) broadcastToAllPlayers(data proto.Message) {
	for uid, info := range s.onlineActors {
		msg := &actor.Message{
			Id:   uint32(def.MsgId_Actor_PlayerSystem2Player),
			Data: data,
			Uid:  uid,
		}

		actor.Send(s.PID(), info.PID(), msg)
	}

	log.Info("Broadcasted season reset to all online players",
		log.Kv("online_count", len(s.onlineActors)))
}

// broadcastSeasonResetToAllPlayers 广播赛季重置消息给所有玩家（包括离线玩家）
func (s *playerSystem) broadcastSeasonResetToAllPlayers(resetMsg *cs.SeasonResetReq) {
	totalCount := 0
	processedCount := 0

	// 遍历所有账号，获取所有玩家的UID
	for _, uid := range s.accounts {
		totalCount++

		// 创建赛季重置消息
		msg := &actor.Message{
			Id:   uint32(def.MsgId_Actor_PlayerSystem2Player),
			Data: resetMsg,
			Uid:  uid,
		}

		s.sendToPlayer(uid, msg)
		processedCount++
	}

	log.Info("Broadcasted season reset to all players",
		log.Kv("total_accounts", totalCount),
		log.Kv("processed_count", processedCount),
		log.Kv("online_count", len(s.onlineActors)),
		log.Kv("old_season_id", resetMsg.OldId),
		log.Kv("new_season_id", resetMsg.NewId))
}

//// globalMailSystemMessageHandler 处理来自全局邮件系统的消息
//func (s *playerSystem) globalMailSystemMessageHandler(ctx context.Context, msg *actor.Message) error {
//	if msg.Data == nil {
//		return errors.New("data nil")
//	}
//
//	switch data := msg.Data.(type) {
//	case *cs.GM2PS_SendGlobalMail:
//		// 处理发送全局邮件的请求
//		s.handleSendGlobalMail(data)
//	default:
//		return fmt.Errorf("unknown message type from global mail system: %T", msg.Data)
//	}
//
//	return nil
//}
//
//// handleSendGlobalMail 处理发送全局邮件请求
//func (s *playerSystem) handleSendGlobalMail(req *cs.GM2PS_SendGlobalMail) {
//	if len(req.Mails) == 0 {
//		log.Warn("empty global mail list")
//		return
//	}
//
//	// 向所有在线玩家发送邮件
//	for _, info := range s.onlineActors {
//		for _, mailData := range req.Mails {
//			actor.Send(s.PID(), info.PID(), &actor.Message{
//				Id:   uint32(def.MsgId_Actor_PlayerSystem2Player),
//				Uid:  info.uid,
//				Data: mailData,
//			})
//		}
//	}
//
//	// 记录离线玩家需要接收的邮件
//	// 这里简化处理，实际可能需要更复杂的逻辑
//	log.Info("sent global mails to online players",
//		log.Kv("online", len(s.onlineActors)),
//		log.Kv("offline", len(s.offlineActors)))
//}

// 添加一封全局邮件
func (s *playerSystem) addGlobalMailHandler(ctx context.Context, data *actor.Message) error {
	if data.Data == nil {
		return nil
	}
	mailInfo := data.Data.(*public.PBMail)
	for _, p := range s.onlineActors {
		mailInfoEx := proto.Clone(mailInfo)
		s.sendToPlayer(p.uid, &actor.Message{
			Id:   def.MsgId_Actor_Mail_Player_AddMail,
			Data: mailInfoEx,
		})
	}
	mailInfoEx := proto.Clone(mailInfo)
	actor.Send(s.PID(), actor_def.MailMgrSystemPID, &actor.Message{
		Id:   def.MsgId_Actor_Mail_AddGlobalMail,
		Data: mailInfoEx,
	})
	return nil
}
