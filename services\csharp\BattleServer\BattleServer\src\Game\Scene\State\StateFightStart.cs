﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Aurora.Framework;

namespace BattleServer.Game
{
    public class StateFightStart : State
    {
        public StateFightStart(StateComponent stateComponent) : base(stateComponent)
        {
        }

        public override void OnEnter()
        {
            Log.Debug("[StateFightStart] OnEnter");

            _stateComponent.ChangeState(StateType.Fight);
        }
    }
}
