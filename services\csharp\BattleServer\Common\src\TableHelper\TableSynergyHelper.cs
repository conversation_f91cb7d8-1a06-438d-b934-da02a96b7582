﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Game.Core
{
    public struct LevelSkill
    {
        public int Level;
        public int StartCount;
        public int SkillId;
    }
    public struct CampSynergy
    {
        public int Camp;
        public int HeroCount;
        public List<LevelSkill> LevelSkills;

        public CampSynergy()
        {
            LevelSkills = new List<LevelSkill>();
        }
    }

    public sealed partial class TableSynergy
    {
        public static CampSynergy GetCampSynergy(int synergyId)
        {
            return TableManager.SynergyData.GetSynergy(synergyId);
        }

        public static List<int> GetAllSynergyId()
        {
            return TableManager.SynergyData.GetAllSynergyId();
        }
    }


    public sealed partial class TableSynergyData
    {
        private List<CampSynergy> campSynergy = new List<CampSynergy>();

        private Dictionary<int, CampSynergy> synergyDic = new Dictionary<int, CampSynergy>();
        public void InitHelper()
        {
            foreach (var tableSynergy in dataList)
            {
                if (tableSynergy.Stage != 2)
                    continue;

                if (tableSynergy.SkillList == null)
                    continue;

                //string str = tableSynergy.Param;
                //string[] strs = str.Split(',');
                var numberLists = JsonConvert.DeserializeObject<List<int>>(tableSynergy.Param);

                CampSynergy synergy = new CampSynergy();
                synergy.Camp = numberLists[0];
                synergy.HeroCount = numberLists[1];
                
                for (int i = 0; i < 3; i++)
                {
                    LevelSkill levelSkill = new LevelSkill();
                    levelSkill.Level = i+1;
                    levelSkill.StartCount = numberLists[i+2];
                    levelSkill.SkillId = tableSynergy.SkillList[i];
                    synergy.LevelSkills.Add(levelSkill);
                }

                campSynergy.Add(synergy);

                synergyDic.Add(tableSynergy.ID, synergy);
            }
        }

        public List<CampSynergy> GetSynergy()
        {
            return campSynergy;
        }

        public CampSynergy GetSynergy(int synergyId)
        {
            if (synergyDic.TryGetValue(synergyId, out CampSynergy synergy))
            {
                return synergy;
            }
            return default;
        }

        public List<int> GetAllSynergyId()
        {
            return synergyDic.Keys.ToList();
        }
    }
}
