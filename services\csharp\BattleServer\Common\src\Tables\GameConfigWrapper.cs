using Game.Core;

namespace Game.Core
{
    /// <summary>
    /// GameConfig配置访问包装类
    /// 此文件由导表工具自动生成，请勿手动修改
    /// </summary>
    public static class GameConfigWrapper
    {
        /// <summary>
        /// ID
        /// </summary>
        public static int GetID()
        {
            return TableGameConfig.GetData(1).ID;
        }

        /// <summary>
        /// 转菊花持续时间
        /// </summary>
        public static int GetWaitTime()
        {
            return TableGameConfig.GetData(1).WaitTime;
        }

        /// <summary>
        /// client心跳间隔
        /// </summary>
        public static float GetHeartBeatCheckTimer()
        {
            return TableGameConfig.GetData(1).HeartBeatCheckTimer;
        }

        /// <summary>
        /// client断网心跳检车次数
        /// </summary>
        public static int GetOffLineCheckNum()
        {
            return TableGameConfig.GetData(1).OffLineCheckNum;
        }

        /// <summary>
        /// client切后台后再返回强退游戏间隔(分钟)
        /// </summary>
        public static int GetBackgroudToLoginUI()
        {
            return TableGameConfig.GetData(1).BackgroudToLoginUI;
        }

        /// <summary>
        /// 弱网检测间隔
        /// </summary>
        public static int GetWeakNetCheckInterval()
        {
            return TableGameConfig.GetData(1).WeakNetCheckInterval;
        }

        /// <summary>
        /// 是否显示弱网菊花
        /// </summary>
        public static int GetShowWeakNetworkLoading()
        {
            return TableGameConfig.GetData(1).ShowWeakNetworkLoading;
        }

        /// <summary>
        /// 最大重连次数
        /// </summary>
        public static int GetMaxReconnecTimes()
        {
            return TableGameConfig.GetData(1).MaxReconnecTimes;
        }

        /// <summary>
        /// 重连时间间隔
        /// </summary>
        public static int GetReconnecIntervalTime()
        {
            return TableGameConfig.GetData(1).ReconnecIntervalTime;
        }

        /// <summary>
        /// 断网后是否重连
        /// </summary>
        public static int GetTryReconnect()
        {
            return TableGameConfig.GetData(1).TryReconnect;
        }

        /// <summary>
        /// Loading进入主界面超时时间
        /// </summary>
        public static float GetShowLoadingWaitTime()
        {
            return TableGameConfig.GetData(1).ShowLoadingWaitTime;
        }

        /// <summary>
        /// 子弹的之间的度值
        /// </summary>
        public static float GetZiDanAngleValue()
        {
            return TableGameConfig.GetData(1).ZiDanAngleValue;
        }

        /// <summary>
        /// 玩家的默认属性id
        /// </summary>
        public static int GetPlayerBaseAttrId()
        {
            return TableGameConfig.GetData(1).PlayerBaseAttrId;
        }

        /// <summary>
        /// 受击泛白的持续时间(单位秒)
        /// </summary>
        public static float GetHurtContinueWhiteTime()
        {
            return TableGameConfig.GetData(1).HurtContinueWhiteTime;
        }

        /// <summary>
        /// 受击泛白的渐变时间
        /// </summary>
        public static float GetHurtGradientTime()
        {
            return TableGameConfig.GetData(1).HurtGradientTime;
        }

        /// <summary>
        /// 失效：受击泛白的颜色
        /// </summary>
        public static float[] GetHurtWhiteColor()
        {
            return TableGameConfig.GetData(1).HurtWhiteColor;
        }

        /// <summary>
        /// 受击泛白的程度
        /// </summary>
        public static float GetHurtWhiteColorMaxA()
        {
            return TableGameConfig.GetData(1).HurtWhiteColorMaxA;
        }

        /// <summary>
        /// 受击缩放的比例实际是（1 + 0.2）
        /// </summary>
        public static float GetHurtScaleValueF()
        {
            return TableGameConfig.GetData(1).HurtScaleValueF;
        }

        /// <summary>
        /// 挂机系统最大时长，默认12小时。单位：秒
        /// </summary>
        public static int GetHookMaxTime()
        {
            return TableGameConfig.GetData(1).HookMaxTime;
        }

        /// <summary>
        /// 掉落的停留时间（帧）
        /// </summary>
        public static int[] GetDropSceneStayTimeInt()
        {
            return TableGameConfig.GetData(1).DropSceneStayTimeInt;
        }

        /// <summary>
        /// 掉落的飞到角色身上的时间（帧）
        /// </summary>
        public static int GetDropFlyPlayerTimeInt()
        {
            return TableGameConfig.GetData(1).DropFlyPlayerTimeInt;
        }

        /// <summary>
        /// 掉落飞向周围的时间(秒)
        /// </summary>
        public static float GetDropFlyAroundTimeFloat()
        {
            return TableGameConfig.GetData(1).DropFlyAroundTimeFloat;
        }

        /// <summary>
        /// 掉落飞向周围的最小距离最大距离
        /// </summary>
        public static float[] GetDropFlyAroundMinFMaxF()
        {
            return TableGameConfig.GetData(1).DropFlyAroundMinFMaxF;
        }

        /// <summary>
        /// 玩家距离指定点的最小距离
        /// </summary>
        public static float GetPlayerToPointMinDis()
        {
            return TableGameConfig.GetData(1).PlayerToPointMinDis;
        }

        /// <summary>
        /// 逃跑的角度范围（偶数方便随机和取一半值）
        /// </summary>
        public static int GetEscapeRandomMaxAngle()
        {
            return TableGameConfig.GetData(1).EscapeRandomMaxAngle;
        }

        /// <summary>
        /// 技能的最小CD时间帧数
        /// </summary>
        public static int GetSkillMinCDTimeValue()
        {
            return TableGameConfig.GetData(1).SkillMinCDTimeValue;
        }

        /// <summary>
        /// 角色出生点背面
        /// </summary>
        public static float[] GetPlayerBornPositionXYZ()
        {
            return TableGameConfig.GetData(1).PlayerBornPositionXYZ;
        }

        /// <summary>
        /// 角色出生点正面
        /// </summary>
        public static float[] GetPlayerFrontBornPositionXYZ()
        {
            return TableGameConfig.GetData(1).PlayerFrontBornPositionXYZ;
        }

        /// <summary>
        /// 飘血的最大时间
        /// </summary>
        public static float GetDamageBoardMaxTime()
        {
            return TableGameConfig.GetData(1).DamageBoardMaxTime;
        }

        /// <summary>
        /// 副本开始
        /// </summary>
        public static int[] GetStageBeginID()
        {
            return TableGameConfig.GetData(1).StageBeginID;
        }

        /// <summary>
        /// 挂机系统额外奖励实际获得倍数数组，分时段取倍数，几个数代表几个小时
        /// </summary>
        public static string[] GetHookExtraAwardRatio()
        {
            return TableGameConfig.GetData(1).HookExtraAwardRatio;
        }

        /// <summary>
        /// 伤害Z坐标信息
        /// </summary>
        public static float GetDamageDepthPositionZ()
        {
            return TableGameConfig.GetData(1).DamageDepthPositionZ;
        }

        /// <summary>
        /// 不在攻击范围以内玩家移动到目标的X的偏移
        /// </summary>
        public static float GetTargetPointPositionDriftX()
        {
            return TableGameConfig.GetData(1).TargetPointPositionDriftX;
        }

        /// <summary>
        /// 血条变化灰色的处理信息延迟时间
        /// </summary>
        public static float GetSceneBloodGreyDelayChangeTime()
        {
            return TableGameConfig.GetData(1).SceneBloodGreyDelayChangeTime;
        }

        /// <summary>
        /// 血条变化灰色的处理信息变化的速度
        /// </summary>
        public static float GetSceneBloodGreyChangeSpeed()
        {
            return TableGameConfig.GetData(1).SceneBloodGreyChangeSpeed;
        }

        /// <summary>
        /// 七日任务进度
        /// </summary>
        public static int[] GetSevenDayTaskProgress()
        {
            return TableGameConfig.GetData(1).SevenDayTaskProgress;
        }

        /// <summary>
        /// 七日任务阶段奖励
        /// </summary>
        public static int[] GetSevenDayTaskStageReward()
        {
            return TableGameConfig.GetData(1).SevenDayTaskStageReward;
        }

        /// <summary>
        /// 七日任务持续时间
        /// </summary>
        public static int GetSevenDayTaskDuration()
        {
            return TableGameConfig.GetData(1).SevenDayTaskDuration;
        }

        /// <summary>
        /// 掉落飞到角色身上的特效
        /// </summary>
        public static int GetDropScenePlayerEffectId()
        {
            return TableGameConfig.GetData(1).DropScenePlayerEffectId;
        }

        /// <summary>
        /// 掉落飞到角色播放特效的时间间隔
        /// </summary>
        public static float GetDropScenePlayerEffectInterval()
        {
            return TableGameConfig.GetData(1).DropScenePlayerEffectInterval;
        }

        /// <summary>
        /// 主线任务引导小手消失任务
        /// </summary>
        public static int GetMissionFingerVanish()
        {
            return TableGameConfig.GetData(1).MissionFingerVanish;
        }

        /// <summary>
        /// 省电模式无操作进入时间，单位秒
        /// </summary>
        public static int GetNoOperationEntryTime()
        {
            return TableGameConfig.GetData(1).NoOperationEntryTime;
        }

        /// <summary>
        /// 好友邀请奖励
        /// </summary>
        public static int[] GetFriendRasinReward()
        {
            return TableGameConfig.GetData(1).FriendRasinReward;
        }

        /// <summary>
        /// 用户协议的URL
        /// </summary>
        public static string GetUserAgreementURL()
        {
            return TableGameConfig.GetData(1).UserAgreementURL;
        }

        /// <summary>
        /// 各副本储存上限字段，各副本独立使用
        /// </summary>
        public static int[] GetStorageLimit()
        {
            return TableGameConfig.GetData(1).StorageLimit;
        }

        /// <summary>
        /// 月卡首次购买直送礼包
        /// </summary>
        public static int GetMonthCardFirstDrop()
        {
            return TableGameConfig.GetData(1).MonthCardFirstDrop;
        }

        /// <summary>
        /// 月卡每日礼包
        /// </summary>
        public static int GetMonthCardDailyDrop()
        {
            return TableGameConfig.GetData(1).MonthCardDailyDrop;
        }

        /// <summary>
        /// 玩家初始头像
        /// </summary>
        public static int GetPlayerInitHead()
        {
            return TableGameConfig.GetData(1).PlayerInitHead;
        }

        /// <summary>
        /// 史莱姆数量对应时间的系数（1,2,3,4）
        /// </summary>
        public static float[] GetPlayerSlamNumTimeArgsArr()
        {
            return TableGameConfig.GetData(1).PlayerSlamNumTimeArgsArr;
        }

        /// <summary>
        /// 聊天泡泡的时间显示时间和消失时间
        /// </summary>
        public static float[] GetChatBubbleShowTimeAndDisappearTime()
        {
            return TableGameConfig.GetData(1).ChatBubbleShowTimeAndDisappearTime;
        }

        /// <summary>
        /// 现金券数量阈值
        /// </summary>
        public static int GetCashCouponThreshold()
        {
            return TableGameConfig.GetData(1).CashCouponThreshold;
        }

        /// <summary>
        /// 隐私协议的URL
        /// </summary>
        public static string GetPrivateAgreementURL()
        {
            return TableGameConfig.GetData(1).PrivateAgreementURL;
        }

        /// <summary>
        /// 只受1点伤害怪ID
        /// </summary>
        public static int[] GetIsOnlyOneDamage()
        {
            return TableGameConfig.GetData(1).IsOnlyOneDamage;
        }

        /// <summary>
        /// 怪物出生点和目标点的X的比例
        /// </summary>
        public static float GetStartEndXScale()
        {
            return TableGameConfig.GetData(1).StartEndXScale;
        }

        /// <summary>
        /// 怪物终点的Z或是怪物判断距离和终点的Z
        /// </summary>
        public static float GetMonsterTargeEndZ()
        {
            return TableGameConfig.GetData(1).MonsterTargeEndZ;
        }

        /// <summary>
        /// 伤害瓢字的参数信息
        /// </summary>
        public static float[] GetDamageFlutterArgs()
        {
            return TableGameConfig.GetData(1).DamageFlutterArgs;
        }

        /// <summary>
        /// 城墙瓢字缩放
        /// </summary>
        public static float GetDamageFlutWallScale()
        {
            return TableGameConfig.GetData(1).DamageFlutWallScale;
        }

        /// <summary>
        /// 玩家身上体力上限信息
        /// </summary>
        public static int GetPowerHaveMaxNum()
        {
            return TableGameConfig.GetData(1).PowerHaveMaxNum;
        }

        /// <summary>
        /// 体力恢复的时间间隔(单位秒)
        /// </summary>
        public static int GetPowerAddTimeInterval()
        {
            return TableGameConfig.GetData(1).PowerAddTimeInterval;
        }

        /// <summary>
        /// 挑战主线关卡消耗的体力
        /// </summary>
        public static int GetMainStagePowerCost()
        {
            return TableGameConfig.GetData(1).MainStagePowerCost;
        }

        /// <summary>
        /// 挑战主队玩法消耗的体力
        /// </summary>
        public static int GetTeamUpPowerCost()
        {
            return TableGameConfig.GetData(1).TeamUpPowerCost;
        }

        /// <summary>
        /// 天气切换的小时（精确到秒）
        /// </summary>
        public static int[] GetRefreshWeatherHour()
        {
            return TableGameConfig.GetData(1).RefreshWeatherHour;
        }

        /// <summary>
        /// 体力极限上限
        /// </summary>
        public static int GetPowerLimitNum()
        {
            return TableGameConfig.GetData(1).PowerLimitNum;
        }

        /// <summary>
        /// 每波子弹数量（打多次子弹开始换弹）
        /// </summary>
        public static int GetAttackSkillNum()
        {
            return TableGameConfig.GetData(1).AttackSkillNum;
        }

        /// <summary>
        /// 换弹时间，跟技能表cd读一样的逻辑，单独配置值
        /// </summary>
        public static int GetAttackSkillChange()
        {
            return TableGameConfig.GetData(1).AttackSkillChange;
        }

        /// <summary>
        /// 先锋能量值上限
        /// </summary>
        public static int GetVanguardEnergyLimit()
        {
            return TableGameConfig.GetData(1).VanguardEnergyLimit;
        }

        /// <summary>
        /// 先锋值能量回复每N秒回复X点
        /// </summary>
        public static string GetVanguardSkillRecovery()
        {
            return TableGameConfig.GetData(1).VanguardSkillRecovery;
        }

        /// <summary>
        /// 机器人模型ID
        /// </summary>
        public static int GetCyberModelID()
        {
            return TableGameConfig.GetData(1).CyberModelID;
        }

        /// <summary>
        /// 机器人坐标
        /// </summary>
        public static float[] GetCyberBornPositionXYZ()
        {
            return TableGameConfig.GetData(1).CyberBornPositionXYZ;
        }

        /// <summary>
        /// 攻击几次播待机动画
        /// </summary>
        public static int GetAttackNumPlayIdleAni()
        {
            return TableGameConfig.GetData(1).AttackNumPlayIdleAni;
        }

        /// <summary>
        /// 主界面相机位置
        /// </summary>
        public static float[] GetMainCameraPosition()
        {
            return TableGameConfig.GetData(1).MainCameraPosition;
        }

        /// <summary>
        /// 主界面相机角度
        /// </summary>
        public static float[] GetMainCameraRotation()
        {
            return TableGameConfig.GetData(1).MainCameraRotation;
        }

        /// <summary>
        /// 主界面相机视角
        /// </summary>
        public static float GetMainCameraView()
        {
            return TableGameConfig.GetData(1).MainCameraView;
        }

        /// <summary>
        /// 战斗界面相机角度
        /// </summary>
        public static float[] GetBattleCameraPosition()
        {
            return TableGameConfig.GetData(1).BattleCameraPosition;
        }

        /// <summary>
        /// 战斗界面相机角度
        /// </summary>
        public static float[] GetBattleCameraRotation()
        {
            return TableGameConfig.GetData(1).BattleCameraRotation;
        }

        /// <summary>
        /// 战斗界面相机视角
        /// </summary>
        public static float GetBattleCameraView()
        {
            return TableGameConfig.GetData(1).BattleCameraView;
        }

        /// <summary>
        /// 怪物移动速度比例参数
        /// </summary>
        public static float GetMonsterMoveSpeedParm()
        {
            return TableGameConfig.GetData(1).MonsterMoveSpeedParm;
        }

        /// <summary>
        /// 准心的最远距离
        /// </summary>
        public static float GetAimPositionZ()
        {
            return TableGameConfig.GetData(1).AimPositionZ;
        }

        /// <summary>
        /// 飘字距离随机阈值
        /// </summary>
        public static float[] GetFloatingThreshold()
        {
            return TableGameConfig.GetData(1).FloatingThreshold;
        }

        /// <summary>
        /// 不需要打断音效的类型
        /// </summary>
        public static int[] GetNotStopSoundTypes()
        {
            return TableGameConfig.GetData(1).NotStopSoundTypes;
        }

        /// <summary>
        /// 拍打能量值
        /// </summary>
        public static int GetButtEnergy()
        {
            return TableGameConfig.GetData(1).ButtEnergy;
        }

        /// <summary>
        /// 点击屏幕出现手印的间隔
        /// </summary>
        public static float GetClickScreenInterval()
        {
            return TableGameConfig.GetData(1).ClickScreenInterval;
        }

        /// <summary>
        /// 体力建筑每日刷新时间
        /// </summary>
        public static int[] GetPowerRefreshTime()
        {
            return TableGameConfig.GetData(1).PowerRefreshTime;
        }

        /// <summary>
        /// 体力建筑最大储存上限
        /// </summary>
        public static int GetPowerStorageLimit()
        {
            return TableGameConfig.GetData(1).PowerStorageLimit;
        }

        /// <summary>
        /// 体力过期时间（天）
        /// </summary>
        public static int GetPowerPeriodValidity()
        {
            return TableGameConfig.GetData(1).PowerPeriodValidity;
        }

        /// <summary>
        /// 体力生成数量
        /// </summary>
        public static int GetPowerNum()
        {
            return TableGameConfig.GetData(1).PowerNum;
        }

        /// <summary>
        /// 可选角色
        /// </summary>
        public static int[] GetOptionalPlayer()
        {
            return TableGameConfig.GetData(1).OptionalPlayer;
        }

        /// <summary>
        /// 挂机图纸
        /// </summary>
        public static int GetDrawingDropGroupId()
        {
            return TableGameConfig.GetData(1).DrawingDropGroupId;
        }

        /// <summary>
        /// 挂机倍率
        /// </summary>
        public static int[] GetDrawingTimes()
        {
            return TableGameConfig.GetData(1).DrawingTimes;
        }

        /// <summary>
        /// 最大扫荡次数
        /// </summary>
        public static int GetDrawingMaxTimes()
        {
            return TableGameConfig.GetData(1).DrawingMaxTimes;
        }

        /// <summary>
        /// 精英怪物随机权重
        /// </summary>
        public static int[] GetEliteWeight()
        {
            return TableGameConfig.GetData(1).EliteWeight;
        }

        /// <summary>
        /// BOSS怪物随机权重
        /// </summary>
        public static int[] GetBOSSWeight()
        {
            return TableGameConfig.GetData(1).BOSSWeight;
        }

        /// <summary>
        /// 战斗内BUFF随机广告次数
        /// </summary>
        public static int GetFightRandomAdvertisementNum()
        {
            return TableGameConfig.GetData(1).FightRandomAdvertisementNum;
        }

        /// <summary>
        /// 昵称后缀区间
        /// </summary>
        public static int[] GetNameNumInterval()
        {
            return TableGameConfig.GetData(1).NameNumInterval;
        }

        /// <summary>
        /// 昵称最大字符限制
        /// </summary>
        public static int GetNameMaxCharacter()
        {
            return TableGameConfig.GetData(1).NameMaxCharacter;
        }

        /// <summary>
        /// 个性签名最大字符限制
        /// </summary>
        public static int GetSignatureMaxCharacter()
        {
            return TableGameConfig.GetData(1).SignatureMaxCharacter;
        }

        /// <summary>
        /// 新手第一组id
        /// </summary>
        public static int GetPlayerStartNewGuildGroupId()
        {
            return TableGameConfig.GetData(1).PlayerStartNewGuildGroupId;
        }

        /// <summary>
        /// 全服邮件的最大存储上限
        /// </summary>
        public static int GetGlobalServerMailMaxNum()
        {
            return TableGameConfig.GetData(1).GlobalServerMailMaxNum;
        }

        /// <summary>
        /// 推送：设置下线时挂机已满，铲子已满 间隔多长时间推送，单位：秒,第1个数为挂机间隔，第2个为铲子间隔
        /// </summary>
        public static int[] GetPushFullIntervalTimes()
        {
            return TableGameConfig.GetData(1).PushFullIntervalTimes;
        }

        /// <summary>
        /// 现金券转钻石比例
        /// </summary>
        public static int GetCashCouponRatio()
        {
            return TableGameConfig.GetData(1).CashCouponRatio;
        }

        /// <summary>
        /// 活动列表排序（按照活动类型ActivityType排序）
        /// </summary>
        public static int[] GetActivityListSortConfig()
        {
            return TableGameConfig.GetData(1).ActivityListSortConfig;
        }

        /// <summary>
        /// 副本的广告上限
        /// </summary>
        public static int[] GetStageDailyAdCount()
        {
            return TableGameConfig.GetData(1).StageDailyAdCount;
        }

        /// <summary>
        /// 副本的消耗ID
        /// </summary>
        public static int[] GetStageCostItemId()
        {
            return TableGameConfig.GetData(1).StageCostItemId;
        }

        /// <summary>
        /// 副本的回复数量
        /// </summary>
        public static int[] GetStageDailyGiveCount()
        {
            return TableGameConfig.GetData(1).StageDailyGiveCount;
        }

        /// <summary>
        /// 副本广告单次
        /// </summary>
        public static int GetStageOneADGiveCount()
        {
            return TableGameConfig.GetData(1).StageOneADGiveCount;
        }

        /// <summary>
        /// 世界聊天冷却时间
        /// </summary>
        public static int GetWorldChatInterval()
        {
            return TableGameConfig.GetData(1).WorldChatInterval;
        }

        /// <summary>
        /// 好友邀请奖励（填写邀请码）
        /// </summary>
        public static int GetFriendInvitedReward()
        {
            return TableGameConfig.GetData(1).FriendInvitedReward;
        }

        /// <summary>
        /// 好友礼物每日接收上限
        /// </summary>
        public static int GetGiftRecMax()
        {
            return TableGameConfig.GetData(1).GiftRecMax;
        }

        /// <summary>
        /// 抽卡系统：每天可观看广告次数上限
        /// </summary>
        public static int GetGachaWatchMaxCount()
        {
            return TableGameConfig.GetData(1).GachaWatchMaxCount;
        }

        /// <summary>
        /// 抽卡系统：每天可观看广告间隔时间，单位：秒
        /// </summary>
        public static int GetGachaWatchInterTime()
        {
            return TableGameConfig.GetData(1).GachaWatchInterTime;
        }

        /// <summary>
        /// 抽卡系统：第一个是广告档位次数，第二个是500钻石档位，第三个是1500钻石档位，第四个无效
        /// </summary>
        public static int[] GetGachaDrawRates()
        {
            return TableGameConfig.GetData(1).GachaDrawRates;
        }

        /// <summary>
        /// 抽卡系统：抽卡一次获得的经验
        /// </summary>
        public static int GetGachaDrawExp()
        {
            return TableGameConfig.GetData(1).GachaDrawExp;
        }

        /// <summary>
        /// 抽卡系统：第一个是广告档位次数，第二个是500钻石档位，第三个是1500钻石档位，第四个无效
        /// </summary>
        public static int[] GetGachaDrawCostDiamonds()
        {
            return TableGameConfig.GetData(1).GachaDrawCostDiamonds;
        }

        /// <summary>
        /// 宠物抽卡系统：每天可观看广告间隔时间，单位：秒
        /// </summary>
        public static int GetPetGachaWatchInterTime()
        {
            return TableGameConfig.GetData(1).PetGachaWatchInterTime;
        }

        /// <summary>
        /// 宠物抽卡系统：抽卡一次获得的经验
        /// </summary>
        public static int GetPetGachaDrawExp()
        {
            return TableGameConfig.GetData(1).PetGachaDrawExp;
        }

        /// <summary>
        /// 圣物抽卡系统：观看广告可抽卡次数上限
        /// </summary>
        public static int GetHallowsGachaAdvDrawMaxCount()
        {
            return TableGameConfig.GetData(1).HallowsGachaAdvDrawMaxCount;
        }

        /// <summary>
        /// 圣物抽卡系统：每天可观看广告次数上限
        /// </summary>
        public static int GetHallowsGachaWatchMaxCount()
        {
            return TableGameConfig.GetData(1).HallowsGachaWatchMaxCount;
        }

        /// <summary>
        /// 抽卡系统：观看广告可抽卡次数上限
        /// </summary>
        public static int GetGachaAdvDrawMaxCount()
        {
            return TableGameConfig.GetData(1).GachaAdvDrawMaxCount;
        }

        /// <summary>
        /// 圣物抽卡系统：普通抽卡档位，下标为档位，值为次数，0档为广告初始次数
        /// </summary>
        public static int[] GetHallowsGachaDrawRates()
        {
            return TableGameConfig.GetData(1).HallowsGachaDrawRates;
        }

        /// <summary>
        /// 系统赠送头像框
        /// </summary>
        public static int GetPlayerDefaultHeadFrame()
        {
            return TableGameConfig.GetData(1).PlayerDefaultHeadFrame;
        }

        /// <summary>
        /// 邮件列表显示上限
        /// </summary>
        public static int GetMailMaxNum()
        {
            return TableGameConfig.GetData(1).MailMaxNum;
        }

        /// <summary>
        /// 问卷奖励邮件ID
        /// </summary>
        public static int GetQuestionnaireEmail()
        {
            return TableGameConfig.GetData(1).QuestionnaireEmail;
        }

        /// <summary>
        /// 创建账号奖励ID
        /// </summary>
        public static int GetNewaccountEmail()
        {
            return TableGameConfig.GetData(1).NewaccountEmail;
        }

        /// <summary>
        /// 第二天邮件奖励ID
        /// </summary>
        public static int GetNextDayMailReward()
        {
            return TableGameConfig.GetData(1).NextDayMailReward;
        }

        /// <summary>
        /// 充值重复返还比例
        /// </summary>
        public static int GetRechargeRatio()
        {
            return TableGameConfig.GetData(1).RechargeRatio;
        }

        /// <summary>
        /// 日常任务ID
        /// </summary>
        public static int[] GetDailyTaskArr()
        {
            return TableGameConfig.GetData(1).DailyTaskArr;
        }

        /// <summary>
        /// 周日常任务ID
        /// </summary>
        public static int[] GetWeekTaskArr()
        {
            return TableGameConfig.GetData(1).WeekTaskArr;
        }

        /// <summary>
        /// 好友数量上限
        /// </summary>
        public static int GetFriendMaxNumber()
        {
            return TableGameConfig.GetData(1).FriendMaxNumber;
        }

        /// <summary>
        /// 可拉黑上限
        /// </summary>
        public static int GetBlacklistMax()
        {
            return TableGameConfig.GetData(1).BlacklistMax;
        }

        /// <summary>
        /// 好友送礼物品id|数量
        /// </summary>
        public static int[] GetGiftItemNum()
        {
            return TableGameConfig.GetData(1).GiftItemNum;
        }

        /// <summary>
        /// 玩家名字字符最大长度
        /// </summary>
        public static int GetPlayerNameMaxLength()
        {
            return TableGameConfig.GetData(1).PlayerNameMaxLength;
        }

        /// <summary>
        /// 好友礼物每日赠送上限
        /// </summary>
        public static int GetGiftSendMax()
        {
            return TableGameConfig.GetData(1).GiftSendMax;
        }

        /// <summary>
        /// GM工具发送邮件的过期时间，单位：天
        /// </summary>
        public static int GetGMSendMailMaxDayTime()
        {
            return TableGameConfig.GetData(1).GMSendMailMaxDayTime;
        }

        /// <summary>
        /// 创建角色时默认给玩家的钻石
        /// </summary>
        public static int GetCreatePlayerDefaultDiamond()
        {
            return TableGameConfig.GetData(1).CreatePlayerDefaultDiamond;
        }

        /// <summary>
        /// 创建角色时默认给玩家的金币
        /// </summary>
        public static int GetCreatePlayerDefaultCoin()
        {
            return TableGameConfig.GetData(1).CreatePlayerDefaultCoin;
        }

        /// <summary>
        /// 玩家初始时装（使用称号时装第一个）
        /// </summary>
        public static int GetPlayerInitDress()
        {
            return TableGameConfig.GetData(1).PlayerInitDress;
        }

        /// <summary>
        /// 主线关卡排名低于50%时，根据区间配置固定区间值和下面的增长值要一一对应。
        /// </summary>
        public static int[] GetMainStagePassBounds()
        {
            return TableGameConfig.GetData(1).MainStagePassBounds;
        }

        /// <summary>
        /// 主线关卡排名低于50%时，根据区间配置固定增长值。
        /// </summary>
        public static int[] GetMainStagePassAdds()
        {
            return TableGameConfig.GetData(1).MainStagePassAdds;
        }

        /// <summary>
        /// 邮件每日领取奖励
        /// </summary>
        public static int[] GetDailyCollectEmail()
        {
            return TableGameConfig.GetData(1).DailyCollectEmail;
        }

        /// <summary>
        /// 邮件每周领取奖励
        /// </summary>
        public static int GetWeeklyReceiveEmail()
        {
            return TableGameConfig.GetData(1).WeeklyReceiveEmail;
        }

        /// <summary>
        /// 性别配置（保密|男|女）
        /// </summary>
        public static string[] GetGenderConfig()
        {
            return TableGameConfig.GetData(1).GenderConfig;
        }

        /// <summary>
        /// 修改个人信息钻石配置
        /// </summary>
        public static int GetChangeInforCost()
        {
            return TableGameConfig.GetData(1).ChangeInforCost;
        }

        /// <summary>
        /// 个性签名修改时间限制（秒）
        /// </summary>
        public static int GetSignatureChangeTime()
        {
            return TableGameConfig.GetData(1).SignatureChangeTime;
        }

        /// <summary>
        /// 等级基金
        /// </summary>
        public static int GetGradeFund()
        {
            return TableGameConfig.GetData(1).GradeFund;
        }

        /// <summary>
        /// 公会名上限
        /// </summary>
        public static int GetAllianceNameLen()
        {
            return TableGameConfig.GetData(1).AllianceNameLen;
        }

        /// <summary>
        /// 公会描述上限
        /// </summary>
        public static int GetAllianceNoticeLen()
        {
            return TableGameConfig.GetData(1).AllianceNoticeLen;
        }

        /// <summary>
        /// 创建公会花费
        /// </summary>
        public static int[] GetAllianceCreateExpend()
        {
            return TableGameConfig.GetData(1).AllianceCreateExpend;
        }

        /// <summary>
        /// 周卡对应礼包ID
        /// </summary>
        public static int[] GetWeekCardGiftId()
        {
            return TableGameConfig.GetData(1).WeekCardGiftId;
        }

        /// <summary>
        /// 每日好友申请数量上限
        /// </summary>
        public static int GetFriendApplyMaxCount()
        {
            return TableGameConfig.GetData(1).FriendApplyMaxCount;
        }

        /// <summary>
        /// 好友数量上限
        /// </summary>
        public static int GetFriendMaxCount()
        {
            return TableGameConfig.GetData(1).FriendMaxCount;
        }

        /// <summary>
        /// 好友黑名单数量上限
        /// </summary>
        public static int GetFriendBlackMaxCount()
        {
            return TableGameConfig.GetData(1).FriendBlackMaxCount;
        }

        /// <summary>
        /// 对战匹配范围查找时间
        /// </summary>
        public static int[] GetMainRankMatchTime()
        {
            return TableGameConfig.GetData(1).MainRankMatchTime;
        }

        /// <summary>
        /// 对战每日胜利奖励次数
        /// </summary>
        public static int GetMainBattleDailyWinTimes()
        {
            return TableGameConfig.GetData(1).MainBattleDailyWinTimes;
        }

        /// <summary>
        /// 对战每日失败补给次数
        /// </summary>
        public static int GetMainBattleDailyFailTimes()
        {
            return TableGameConfig.GetData(1).MainBattleDailyFailTimes;
        }

        /// <summary>
        /// 对战初始杯数
        /// </summary>
        public static int GetMainRankScoreInitial()
        {
            return TableGameConfig.GetData(1).MainRankScoreInitial;
        }

        /// <summary>
        /// 宝物抽取每日广告限次
        /// </summary>
        public static int GetTreasureGachaAdTimes()
        {
            return TableGameConfig.GetData(1).TreasureGachaAdTimes;
        }

        /// <summary>
        /// 宝物抽取概率修正系数
        /// </summary>
        public static int[] GetTreasureGachaProModify()
        {
            return TableGameConfig.GetData(1).TreasureGachaProModify;
        }

        /// <summary>
        /// 玩家上阵英雄数量
        /// </summary>
        public static int GetHeroLineUpNum()
        {
            return TableGameConfig.GetData(1).HeroLineUpNum;
        }

        /// <summary>
        /// 初始上阵英雄id
        /// </summary>
        public static int[] GetHeroLineUpId()
        {
            return TableGameConfig.GetData(1).HeroLineUpId;
        }

        /// <summary>
        /// 英雄等级上限
        /// </summary>
        public static int GetHeroLevelMax()
        {
            return TableGameConfig.GetData(1).HeroLevelMax;
        }

        /// <summary>
        /// 英雄觉醒上限
        /// </summary>
        public static int GetHeroEvoMax()
        {
            return TableGameConfig.GetData(1).HeroEvoMax;
        }

        /// <summary>
        /// 初始获得角色
        /// </summary>
        public static int[] GetHeroDefaultObtainId()
        {
            return TableGameConfig.GetData(1).HeroDefaultObtainId;
        }

        /// <summary>
        /// 初始获得角色数量
        /// </summary>
        public static int GetHeroDefaultObtainNum()
        {
            return TableGameConfig.GetData(1).HeroDefaultObtainNum;
        }

    }
}
