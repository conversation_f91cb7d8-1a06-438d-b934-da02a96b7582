package player

import (
	"context"
	"fmt"
	"liteframe/pkg/uuid"
	"strconv"
	"strings"
	"time"

	playerInfov1 "liteframe/api/microservices/playerinfo/v1"
	"liteframe/internal/common/table/table_data"
	"liteframe/internal/common/version"

	chatv1 "liteframe/api/microservices/chat/v1"
	"liteframe/internal/common/error_code"
	"liteframe/internal/common/pb_head"
	cs2 "liteframe/internal/common/protos/cs"
	"liteframe/internal/common/protos/dbstruct"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/common/rpc_def"
	"liteframe/internal/common/table"
	"liteframe/internal/game-logic/gameserver/game_def"
	"liteframe/internal/game-logic/gameserver/iface"
	"liteframe/pkg/connection/packet"

	"liteframe/internal/game-logic/gameserver/gameutil/metrics"
	"liteframe/internal/game-logic/gameserver/global"
	"liteframe/internal/game-logic/gameserver/logic/gameutil"
	"liteframe/pkg/actor"
	"liteframe/pkg/log"
	"liteframe/pkg/pb_pack"
)

const MaxMailSize = 128

// Player 由 playerSystem 创建 销毁，所有消息由 playerSystem 转发，Player actor 负责所有玩家相关的消息处理，包括登录和登出，离线的玩家也有相应的 Actor
// 创建时机：playerSystem 收到 转发给玩家的消息，若 uid 对应的 Actor 不存在，则调用 NewPlayer 创建，并注册到 AstorSystem 里，发送 InitPlayer 消息，PlayerActor 同步加载 DB
// 销毁时间：playerSystem 对超过时长未收到消息的离线玩家进行回收销毁
// Player Actor 有三种状态：
// 无效的 Player：uid 不存在，Actor 创建后 DB 无数据，销毁逻辑同离线 Player
// 未在线的 Player(离线玩家): Actor 创建后，有 DB 成功加载，无 session 和 gateway.Client，可离线操作玩家逻辑，不会发消息
// 在线的 Player：Actor 创建后，有 DB 成功加载，且收到登录消息 OnLogin 成功；下线后 Actor 仍存在，转为离线玩家

type Player struct {
	// initPlayer 前数据
	validPlayer   bool // 玩家是否存在
	uid           uint64
	pid           actor.PID
	actorDispatch *actor.Dispatcher

	// initPlayer 后数据
	db *dbstruct.UserDB

	sid    uint64       // 客户端 session id
	online bool         // 在线 离线
	client iface.Sender // client 使用要判空！！！

	// runtime data
	lastReq      *lastPacket
	crossDayTime int64 // 跨天时间戳 运行时数据

	isForbidSyncItemAndMissionData bool // 是否禁止同步 MissionData 和物品货币信息 慎用！！！

	*moduleGroup
	paymentCallbacks *PaymentCallbacks // 支付回调
	item             *Item             // 道具模块
	bag              *Bag              // 背包模块
	money            *Money            // 货币模块
	playerAttr       *PlayerAttr       // 玩家属性模块
	playerEvent      *EventSystem      // 玩家事件系统
	drop             *Drop             // 掉落
	mail             *Mail             // 邮件模块
	redeemCode       *RedeemCode       // 礼包码
	guild            *Guild            //
	shop             *Shop
	settings         *Settings
	sevenSignIn      *SevenSignin // 七日签到
	firstCharge      *FirstCharge
	topUpRebate      *TopupRebate
	monthlyCard      *MonthlyCard
	gradedFund       *GradedFund
	mission          *Mission
	funcOpen         *FuncOpen
	activity         *Activity
	commonBoxReward  *CommonBoxReward
	monthlyCardNew   *MonthlyCardNew
	timeShop         *TimeShop      // 日周月商城
	gachaBonus       *GachaBonus    // 千抽
	questionnaire    *Questionnaire // 问卷调查
	newGuide         *NewGuide      // 问卷调查
	friend           *Friends       // 好友管理
	tower            *TowerData     // 爬塔

	totalRecharge *TotalRecharge // 日进斗金
	inviteTask    *InviteTask    // 好友邀请
	arena         *Arena         // 竞技场
	heavenlyDao   *HeavenlyDao   // 天道修为
	weekCardData  *WeekCardData  // 周卡
	hook          *Hook          // 挂机奖励
	hero          *Hero          // 英雄
	lineup        *Lineup        // 阵容
	seasonBuff    *SeasonBuff    // 赛季Buff模块
	battle        *Battle        // 战斗相磁
	trophy        *Trophy        // 奖杯模块
	treasure      *Treasure      // 宝物
	redDot        *RedDot        // 红点系统
}

func NewPlayer(uid uint64, isLogin bool) actor.Actor {
	p := &Player{
		uid:         uid,
		moduleGroup: newModuleGroup(),
		actorDispatch: actor.NewDispatcher(1000000, func(uid uint64, id uint32, ms int64) {
			metrics.SetRpcCost(fmt.Sprintf("player_%d", id), ms)
		}),
	}
	p.pid = actor.NewPID(uid, "player")
	p.registerHandler()
	return p
}

func (p *Player) onLogin(nowUnix int64, isReconnect bool) {
	lastLogout := p.db.Base.LastLogout
	if lastLogout < nowUnix {
		if !gameutil.IsSameDay(nowUnix, lastLogout) {
			p.OnCrossDay(false, nowUnix)
		}
		if !gameutil.IsSameWeek(nowUnix, lastLogout) {
			p.OnCrossWeek(false)
		}
		if !gameutil.IsSameMonth(nowUnix, lastLogout) {
			p.OnCrossMonth(false)
		}
	}
	p.db.Base.LastLogin = global.Now().Unix()
	p.online = true
}

func (p *Player) OnDeleteAccount(req *cs2.CLDeleteAccount) {

}

// OnCrossDay 跨天
// @param natural 是否自然跨天（true:自然跨天 false:登陆跨天）
// @param nowUnix - 当前时间戳
func (p *Player) OnCrossDay(natural bool, nowUnix int64) {
	if p.crossDayTime != 0 && gameutil.IsSameDay(p.crossDayTime, nowUnix) {
		return
	}
	// TODO: TO IMPL

	log.Info("OnCrossDay", log.Kv("natural", natural), log.Kv("nowUnix", nowUnix))
	for _, v := range p.modules {
		v.OnCrossDay(natural, nowUnix)
	}

	p.crossDayTime = nowUnix

	p.SendToClient(rpc_def.LCNotifyCrossDay, &cs2.LCNotifyCrossDay{}, false)
}

// OnCrossWeek 跨周
// @param natural 是否自然跨周（true:自然跨周 false:登陆跨周）
func (p *Player) OnCrossWeek(natural bool) {
	// TODO: TO IMPL
	log.Info("OnCrossWeek", log.Kv("natural", natural))
}

// OnCrossMonth 跨月
// @param natural 是否自然跨月（true:自然跨月 false:登陆跨月）
func (p *Player) OnCrossMonth(natural bool) {
	// TODO: TO IMPL
	log.Info("OnCrossMonth", log.Kv("natural", natural))
}

func (p *Player) OnRcvPacket(id uint16) {
	if p.lastReq == nil {
		p.lastReq = &lastPacket{}
	}
	p.lastReq.Id = id
	p.lastReq.Resp = nil
}

func (p *Player) onRespPacket(id uint16, data *packet.Packet) {
	if p.lastReq == nil {
		return
	}

	if id == p.lastReq.Id {
		p.lastReq.Resp = data
	}
}

func (p *Player) LastPacket(id uint16) *lastPacket {
	return nil
	// if p.lastReq == nil || p.lastReq.Id != id {
	//	return nil
	// }
	// return p.lastReq
}

func (p *Player) ResponseCache() {
	if p.client == nil || p.lastReq == nil || p.lastReq.Resp == nil {
		return
	}
	p.client.Send(p.lastReq.Resp)
}

func (p *Player) SendBinaryToClient(id uint16, data []byte, isNotify bool) {
	if p.client == nil {
		return
	}

	if !p.online {
		log.Warn("offline send", log.Kv("uid", p.Uid()), log.Kv("msg_id", id))
		return
	}

	d := pb_pack.Binary2Packet(&pb_head.ClientHead{
		ID: id,
	}, data)
	p.client.Send(d)
	if isNotify { // 推送
		// TODO
	} else {
		p.onRespPacket(id, d)
	}
	if global.MsgTrace == 1 {
		var rpcName string
		if r, ok := rpc_def.RPCs[uint32(id)]; ok {
			rpcName = r.Name
		}

		log.Debug("player out msg",
			log.Kv("uid", p.Uid()),
			log.Kv("id", id),
			log.Kv("name", rpcName),
			log.Kv("isNotify", isNotify),
		)
	}
}

func (p *Player) ToClient() *public.PBPlayerInfo {
	return &public.PBPlayerInfo{
		PlatformID:            int64(p.db.Base.Uid),
		NickName:              p.db.Base.Name,
		Gender:                public.EGenderType(p.db.Base.Gender),
		HeadIcon:              p.settings.GetHeadIconById(p.db.Base.HeadIcon),
		FrameId:               p.settings.GetHeadFrameById(p.db.Base.HeadFrame),
		Sigin:                 p.db.Base.Summary,
		Level:                 int32(p.db.Base.Level),
		MissionData:           nil,
		QuestionnaireProgress: 0,
		Exp:                   p.db.Base.Exp,
		MoneyData: &public.PBPlayerMoneyInfo{
			Gold:      p.money.GetMoneyNumById(game_def.MoneyCoin),
			Diamon:    p.money.GetMoneyNumById(game_def.MoneyDiamond),
			Power:     p.money.GetMoneyNumById(game_def.NickPower),
			Refined:   p.money.GetMoneyNumById(game_def.MoneyRefinement),
			Hallows:   p.money.GetMoneyNumById(game_def.MoneyHallows),
			GuildCoin: p.money.GetMoneyNumById(game_def.MoneyGuildCoin),
		},
		FrameExpireTime:      0,
		WorldChatRoom:        "",
		PChatCleanTime:       0,
		PlayerShareTime:      0,
		AreaID:               0,
		GameCenterLoginType:  0,
		TitleData:            nil,
		ShowSDK:              false,
		Birthday:             601,
		ShowKLCDK:            false,
		ModelId:              0,
		AttributeLevelupList: nil,
		TalentData:           nil,
		EquipSlots:           nil,
		HallowsSlots:         nil,
		PetSlots:             nil,
		SettingData:          &public.PBSettingData{IsShowPush: false},
		BornServerId:         int32(p.db.Base.ServerId),
		LoginServerId:        int32(p.db.Base.ServerId),
		ServerCodeSvnVersion: version.BuildVersion,
		OpenServerTime:       0,
	}
}

func (p *Player) onPlayerDataReq() {

}

// func (p *Player) ChangeDataToClient() *cs.ChangeDataClient {
//	if p.changeData == nil {
//		return nil
//	}
//	changeData := p.changeData
//	p.changeData = nil
//	data := make(map[int32][]byte)
//	//if b, _ := proto.Marshal(changeData.Item); len(b) > 0 {
//	//	data[int32(cs.ChangeDataModule_CDM_Item)] = b
//	//}
//
//	if len(data) == 0 {
//		return nil
//	}
//	//clientChangeData := &cs.ChangeDataClient{Data: data}
//	clientChangeData := &cs.ChangeDataClient{}
//	if global.MsgTrace == 1 {
//		b, _ := json.Marshal(changeData)
//		log.Debug("ChangeDataToClient", log.Kv("uid", p.Uid()), log.Kv("change_data", string(b)))
//	}
//	return clientChangeData
// }

func (p *Player) DB() *dbstruct.UserDB {
	return p.db
}

func IsAvailableMoneyType(moneyType int32) bool {
	return moneyType >= game_def.MoneyNone && moneyType < game_def.MoneySize
}

func (p *Player) onMoneyDec(moneyType int32, oldValue, newValue, decValue int32) {
	// TODO: 当 money 减少时触发的业务逻辑，包括可能会有的安全日志等
	switch moneyType {

	default:
		// No action needed for unknown money types
	}
}

// func (p *Player) SendGrpcRequest(req *actor.Message) interface{} {
//	if req == nil || req.Data == nil {
//		log.Error("Invalid gRPC request - message or data is nil",
//			log.Kv("uid", p.uid))
//		return nil
//	}
//
//	grpcReq, ok := req.Data.(*pbgen.GrpcMessage)
//	if !ok {
//		log.Error("Invalid gRPC request type",
//			log.Kv("uid", p.uid),
//			log.Kv("type", fmt.Sprintf("%T", req.Data)))
//		return nil
//	}
//
//	registry := grpcclient.GetRegistry()
//	if registry == nil {
//		log.Error("gRPC registry not initialized",
//			log.Kv("uid", p.uid))
//		return nil
//	}
//
//	// 使用注册表的请求超时设置
//	ctx, cancel := context.WithTimeout(
//		context.Background(),
//		registry.GetRequestTimeout())
//	defer cancel()
//
//	client := grpcclient.GetConnectByServiceID(grpcReq.ServiceId)
//	if client == nil {
//		log.Error("Failed to get gRPC client",
//			log.Kv("uid", p.uid),
//			log.Kv("service_id", grpcReq.ServiceId),
//			log.Kv("func", grpcReq.FuncName))
//		return nil
//	}
//
//	getter, ok := client.(pbgen.ServiceCallMapGetter)
//	if !ok {
//		log.Error("gRPC client does not implement ServiceCallMapGetter",
//			log.Kv("uid", p.uid),
//			log.Kv("service_id", grpcReq.ServiceId),
//			log.Kv("func", grpcReq.FuncName))
//		return nil
//	}
//
//	serviceCall := getter.GetServiceCallMap(grpcReq.FuncName)
//	if serviceCall == nil {
//		log.Error("gRPC function not found",
//			log.Kv("uid", p.uid),
//			log.Kv("service_id", grpcReq.ServiceId),
//			log.Kv("func", grpcReq.FuncName))
//		return nil
//	}
//
//	log.Debug("Calling gRPC service",
//		log.Kv("uid", p.uid),
//		log.Kv("service_id", grpcReq.ServiceId),
//		log.Kv("func", grpcReq.FuncName))
//
//	reply, err := serviceCall(client, ctx, req)
//	if err != nil {
//		log.Error("gRPC call failed",
//			log.Kv("uid", p.uid),
//			log.Kv("service_id", grpcReq.ServiceId),
//			log.Kv("func", grpcReq.FuncName),
//			log.Err(err))
//		return nil
//	}
//
//	log.Debug("gRPC call succeeded",
//		log.Kv("uid", p.uid),
//		log.Kv("service_id", grpcReq.ServiceId),
//		log.Kv("func", grpcReq.FuncName))
//
//	return reply
// }
//
// func (p *Player) SendGrpcRequestAsync(req *actor.Message) {
//	if req == nil || req.Data == nil {
//		log.Error("Invalid async gRPC request - message or data is nil",
//			log.Kv("uid", p.uid))
//		return
//	}
//
//	if grpcReq, ok := req.Data.(*pbgen.GrpcMessage); ok {
//		log.Debug("Enqueueing async gRPC request",
//			log.Kv("uid", p.uid),
//			log.Kv("service_id", grpcReq.ServiceId),
//			log.Kv("func", grpcReq.FuncName))
//	}
//
//	_, err := p.playerMicroservice.Enqueue(req)
//	if err != nil {
//		log.Error("Failed to enqueue async gRPC request",
//			log.Kv("uid", p.uid),
//			log.Err(err))
//	}
// }

// AddExp 增加经验，自动升级
func (p *Player) AddExp(expValue int64, itemOperateKey int64, causeId int32, subCauseId string) bool {
	if expValue <= 0 {
		return false
	}

	// 计算总经验
	allExp := p.db.Base.Exp + expValue
	beforeLevel := p.db.Base.Level
	levelMax := p.getLevelMax()

	// 处理满级情况
	if beforeLevel >= levelMax {
		needExp := p.getNeedExp(beforeLevel)
		// 满级满经验不处理
		if p.db.Base.Exp >= needExp {
			return false
		}
		// 已经满级，只累加经验
		p.db.Base.Exp = allExp

		resp := &cs2.LC_AddExp_RES{
			IsLevelUp:   false,
			BeforeLevel: int32(beforeLevel),
			AfterLevel:  int32(beforeLevel),
		}
		p.SendToClient(rpc_def.LC_AddExp_RES, resp, false)
		return false
	}

	isLevelUp := false
	var funcList []int32
	var skillList []int32

	// 升级循环
	for {
		needExp := p.getNeedExp(p.db.Base.Level)
		if allExp < needExp || p.db.Base.Level >= levelMax {
			break
		}

		// 升级
		allExp -= needExp
		p.db.Base.Level++

		// 获取解锁内容
		funcId, skillId := p.getLevelUnlocks(p.db.Base.Level)
		if funcId != -1 {
			funcList = append(funcList, funcId)
		}
		if skillId != -1 {
			skillList = append(skillList, skillId)
		}

		isLevelUp = true
		log.Info("AddExp",
			log.Kv("CurLevel", p.db.Base.Level),
			log.Kv("NextLevel", p.db.Base.Level+1),
			log.Kv("CurExp", allExp),
			log.Kv("Cost", needExp))
	}

	// 更新经验值
	p.db.Base.Exp = allExp

	// 升级成功处理
	if isLevelUp {
		// 发送响应包
		resp := &cs2.LC_AddExp_RES{
			IsLevelUp:   true,
			AddExp:      int32(expValue),
			AfterExp:    int32(allExp),
			BeforeLevel: int32(beforeLevel),
			AfterLevel:  int32(p.db.Base.Level),
			FuncList:    funcList,
			SkillList:   skillList,
		}
		p.SendToClient(rpc_def.LC_AddExp_RES, resp, false)
		// 检查功能解锁
		p.checkFunctionUnlock()
		// 触发任务
		p.Mission().OnMonitorMissionParams(MissionConditionTypePlayerLevelUp, nil)

		// 更新排行榜数据
		//p.UpdateRankInfo(rankv1.RankType_Level, int64(p.db.Base.Level))

		// 检查挂机奖励种类是否有变化
		p.Hook().LevelUpChangeReward()
	}

	return isLevelUp
}

// getLevelMax 获取最大等级
func (p *Player) getLevelMax() uint32 {
	return uint32(table.GetTable().TablePlayerLevel.Count())
}

// getNeedExp 获取升级所需经验
func (p *Player) getNeedExp(level uint32) int64 {
	t := table.GetTable().TablePlayerLevel.GetById(int32(level))
	return int64(t.Exp)
}

// getLevelUnlocks 获取等级解锁的功能和技能
func (p *Player) getLevelUnlocks(level uint32) (funcId, skillId int32) {
	t := table.GetTable().TablePlayerLevel.GetById(int32(level))
	return t.FuncId, t.SkillId
}

// checkFunctionUnlock 检查功能解锁
func (p *Player) checkFunctionUnlock() {
	p.FuncOpen().CheckFuncOpen()
}

// addMilitaryLevel 增加军衔等级 todo:暂时没有对应系统
func (p *Player) addMilitaryLevel(level int32) {
	p.db.Base.MilitaryLevel += level
	p.checkFunctionUnlock()
}

func (p *Player) OnGmCommand(command string) {
	log.Info("OnGmCommand]", log.Kv("Uid", p.Uid()), log.Kv("command", command))
	// 分割命令类型和参数
	parts := strings.SplitN(command, " ", 2)
	if len(parts) < 2 {
		log.Error("OnGmCommand invalid params, <type> <params>")
		return
	}

	// 解析命令类型
	cmdType, err := strconv.Atoi(parts[0])
	if err != nil {
		log.Error("OnGmCommand<type> must be number")
		return
	}

	// 获取参数部分
	params := parts[1]

	// 处理不同类型的 GM 命令
	ret := false
	switch cmdType {
	case game_def.GmAddExp:
		ret = p.handleGMAddExp(params)
	case game_def.GmAddBagItem:
		ret = p.handleGMAddBagItem(params)
	case game_def.GmDelBagItem:
		ret = p.handleGMDelBagItem(params)
	case game_def.GmAddMoney:
		ret = p.handleGMAddMoney(params)
	case game_def.GmDelMoney:
		ret = p.handleGMDelMoney(params)
	case game_def.GmItemDrop:
		ret = p.handleGMItemDrop(params)
	case game_def.GmUseItem:
		ret = p.handleGMUseItem(params)
	case game_def.GmMailLogic:
		ret = p.handleGMMailLogic(params)
	case game_def.GmGuild:
		ret = p.handleGMGuild(params)
	case game_def.GmUnlockHeadIcon:
		ret = p.handleGMUnlockHeadIcon(params)
	case game_def.GmAddHeadFrame:
		ret = p.handleGMAddHeadFrame(params)
	case game_def.GmSevenSignIn:
		ret = p.handleGMSevenSignIn(params)
	case game_def.GmTopUpRebate:
		ret = p.handleGMTopUpRebate(params)
	case game_def.GmMonthlyCard:
		ret = p.handleGMMonthlyCard(params)
	case game_def.GmGradedFund:
		ret = p.handleGMGradedFund(params)
	case game_def.GmMission:
		ret = p.handleGMMission(params)
	case game_def.GmAddItem:
		ret = p.handleGMItemOperate(1, params)
	case game_def.GmDelItem:
		ret = p.handleGMItemOperate(2, params)
	case game_def.GmPay:
		ret = p.handlerGMPay(params)
	case game_def.GMPayTest:
		ret = p.handlerGMPayTest(params)
	case game_def.GMADDMilitaryLevel:
		ret = p.handlerGMADDMilitaryLevel(params)
	case game_def.GMFuncOpenTime:
		ret = p.handlerGMTryUnlockTimeFunc(params)
	case game_def.GMTestReq:
		ret = p.handlerGMTestReq()
	case game_def.GMJohnTest:
		ret = p.handlerJohnTest(params)
	case game_def.GMTestSysMsg:
		ret = p.handlerGMTestSysMsg(params)
	case game_def.GMTestPrivate:
		ret = p.handlerGMPrivate(params)
	case game_def.GMJohnTest1:
		ret = p.handlerJohnTest1(params)
	case game_def.GmMonthlyCardNew:
		ret = p.handleGMMonthlyCardNew(params)
	case game_def.GmTimeShop:
		ret = p.handleGMTimeShop(params)
	case game_def.GmGachaBonus:
		ret = p.handleGMGachaBonus(params)
	case game_def.GmQuestionnaire:
		ret = p.handleGMQuestionnaire(params)
	case game_def.GmVipExp:
		ret = p.handleGMVipExp(params)
	case game_def.GmTotalRecharge:
		ret = p.handleAddTotalRecharge(params)
	case game_def.GmHeavenlyDao: // GM 天道修为
		ret = p.handleHeavenlyDao(params)
	case game_def.GmHero: // GM 英雄系统
		ret = p.handleGMHero(params)
	case game_def.GmLineup: // GM 阵容系统
		ret = p.handleGMLineup(params)
	case game_def.GmTreasure: // GM 宝物系统
		ret = p.handleGMTreasure(params)
	default:
		log.Error("OnGmCommand not find type")
	}

	if ret {
		p.SetNoticeTips(error_code.OK_OPT_SUCCESS)
	} else {
		p.SetNoticeTips(error_code.ERROR_OPT_FAILE)
	}
}

// handleGMLineup 处理阵容相关的 GM 命令
func (p *Player) handleGMLineup(params string) bool {
	parts := strings.SplitN(params, " ", 2)
	if len(parts) < 1 {
		log.Error("GmLineup invalid params, format: <subType> <params>")
		return false
	}

	// 解析子命令类型
	subType, err := strconv.Atoi(parts[0])
	if err != nil {
		log.Error("GmLineup subType must be number",
			log.Kv("input", parts[0]))
		return false
	}

	// 获取剩余参数
	var cmdParams string
	if len(parts) > 1 {
		cmdParams = parts[1]
	}

	switch subType {
	case game_def.GmLineupSubUnlockAll: // 解锁所有阵容槽位
		return p.handleLineupUnlockAll()
	case game_def.GmLineupSubSetHeroes: // 设置指定阵容的英雄
		return p.handleLineupSetHeroes(cmdParams)
	case game_def.GmLineupSubSwitch: // 切换当前使用的阵容
		return p.handleLineupSwitch(cmdParams)
	default:
		log.Error("GmLineup unknown subType", log.Kv("subType", subType))
		return false
	}
}

// handleLineupUnlockAll 解锁所有阵容槽位
func (p *Player) handleLineupUnlockAll() bool {
	// 获取所有可能的阵容槽位
	allSlotIds := make([]int32, 0)
	table.GetTable().TableTeamHole.Foreach(func(holeInfo *table_data.TableTeamHole) bool {
		allSlotIds = append(allSlotIds, holeInfo.ID)
		return true
	})

	// 添加这些槽位到已解锁列表中
	unlockedIds := p.Lineup().db.UnlockedIds
	changed := false

	for _, slotId := range allSlotIds {
		alreadyUnlocked := false
		for _, unlocked := range unlockedIds {
			if unlocked == slotId {
				alreadyUnlocked = true
				break
			}
		}

		if !alreadyUnlocked {
			// 创建新阵容并添加到列表
			newLineup := &public.PBLineupInfo{
				Id:      slotId,
				Name:    "自定义" + string(rune(slotId)),
				HeroIds: make([]int32, 0),
			}

			// 如果有其他阵容，复制第一个阵容的英雄列表
			if len(p.Lineup().db.LineupList) > 0 {
				firstLineup := p.Lineup().db.LineupList[0]
				newLineup.HeroIds = append(newLineup.HeroIds, firstLineup.HeroIds...)
			}

			// 添加新阵容到列表和解锁ID列表
			p.Lineup().db.LineupList = append(p.Lineup().db.LineupList, newLineup)
			p.Lineup().db.UnlockedIds = append(p.Lineup().db.UnlockedIds, slotId)
			changed = true
		}
	}

	if changed {
		log.Info("GM: All lineup slots unlocked",
			log.Kv("player_id", p.Uid()),
			log.Kv("unlocked_ids", p.Lineup().db.UnlockedIds))
	}

	return true
}

// handleLineupSetHeroes 设置指定阵容的英雄
// 格式: <阵容ID> <英雄ID1,英雄ID2,...>  例如: "1 1001,1002,1003"
func (p *Player) handleLineupSetHeroes(params string) bool {
	parts := strings.SplitN(params, " ", 2)
	if len(parts) != 2 {
		log.Error("GmLineupSetHeroes invalid params, format: <lineupId> <heroId1,heroId2,...>")
		return false
	}

	// 解析阵容ID
	lineupId, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GmLineupSetHeroes invalid lineupId", log.Kv("input", parts[0]))
		return false
	}

	// 获取指定阵容
	lineup := p.Lineup().getLineupById(int32(lineupId))
	if lineup == nil {
		log.Error("GmLineupSetHeroes lineup not found", log.Kv("lineup_id", lineupId))
		return false
	}

	// 解析英雄ID列表
	heroIdStrs := strings.Split(parts[1], ",")
	heroIds := make([]int32, 0)

	for _, idStr := range heroIdStrs {
		if idStr == "" {
			continue
		}
		heroId, err := strconv.ParseInt(idStr, 10, 32)
		if err != nil {
			log.Error("GmLineupSetHeroes invalid heroId", log.Kv("input", idStr))
			continue
		}
		heroIds = append(heroIds, int32(heroId))
	}

	// 验证英雄是否有效
	validHeroIds := make([]int32, 0)
	for _, heroId := range heroIds {
		if heroId == 0 || p.Hero().GetHeroById(heroId) != nil {
			validHeroIds = append(validHeroIds, heroId)
		} else {
			log.Warn("GmLineupSetHeroes hero not owned",
				log.Kv("player_id", p.Uid()),
				log.Kv("hero_id", heroId))
		}
	}

	// 替换阵容英雄列表
	lineup.HeroIds = validHeroIds

	log.Info("GM: Lineup heroes set",
		log.Kv("player_id", p.Uid()),
		log.Kv("lineup_id", lineupId),
		log.Kv("heroes", validHeroIds))

	return true
}

// handleLineupSwitch 切换当前使用的阵容
// 格式: <阵容ID>  例如: "2"
func (p *Player) handleLineupSwitch(params string) bool {
	// 解析阵容ID
	lineupId, err := strconv.ParseInt(params, 10, 32)
	if err != nil {
		log.Error("GmLineupSwitch invalid lineupId", log.Kv("input", params))
		return false
	}

	// 检查阵容是否存在
	lineup := p.Lineup().getLineupById(int32(lineupId))
	if lineup == nil {
		log.Error("GmLineupSwitch lineup not found", log.Kv("lineup_id", lineupId))
		return false
	}

	// 切换当前阵容
	p.Lineup().db.CurrentId = int32(lineupId)

	log.Info("GM: Current lineup switched",
		log.Kv("player_id", p.Uid()),
		log.Kv("new_current_id", lineupId))

	return true
}

func (p *Player) SendSystemMsg(info string) bool {
	// 使用单例方法发送系统消息
	// return GetInstance().SendSystemMsg(p.uid, info)
	// t_nowtime := int64(time.Now().Unix())
	g2gChatMsg := &chatv1.SendChatMessageRequest{
		ChatMessage: &chatv1.ChatMessage{
			TimeStamp:        1,
			MsgId:            738,
			ZoneWorldId:      1,
			SenderGuid:       0,
			SenderPlayerInfo: &playerInfov1.PlayerBaseInfo{},
			MessageType:      chatv1.MessageChannelType_MessageType_System,
			MessageSubType:   chatv1.MessageSubType_MessageSubType_Broadcast,
			TargetChatServer: chatv1.TargetChatServer_TargerServer_ZoneServer,
			ChannelType:      chatv1.ChatChannelType_ChatType_Message,
			Message:          info,
			HeadFrameId:      1001,
		},
		ReceiverGuids:  []uint64{p.uid},
		NeedTextDetect: false,
	}

	if grpcMgr.GetChatService() == nil {
		return false
	}

	reply, err := grpcMgr.GetChatService().SendChatMessage(context.Background(), g2gChatMsg)
	if err != nil {
		log.Error("SendChatMessage failed", log.Err(err))
		return false
	}

	// bs, _ := proto.Marshal(g2gChatMsg)
	// d := &pbgen.GrpcMessage{
	//	ServiceId: pbgen.ChatServiceClient, //?如何订一
	//	FuncName:  "SendChatMessage",
	//	Data:      bs,
	// }
	// msg := &actor.Message{
	//	Uid:  p.Uid(),
	//	Data: d,
	// }
	// reply := p.SendToGrpcService(msg)
	// if _, ok := reply.(*pbgen.SendChatMessageReply); ok {
	//
	// }
	log.Info("SendChatMessage", log.Kv("body", reply))
	return true
}

// 24
func (p *Player) handlerGMTestSysMsg(params string) bool {
	// p.SendSystemMsg(params)
	return GetInstance().SendSystemMsg(0, params)
}

// 25
func (p *Player) handlerJohnTest(params string) bool {
	// 使用单例方法发送系统消息
	p.SynPlayerInfo2PlayerService()
	return true
}

// 26
func (p *Player) handlerGMPrivate(params string) bool {
	g2gChatMsg := &chatv1.SendChatMessageRequest{
		ChatMessage: &chatv1.ChatMessage{
			TimeStamp:   1,
			MsgId:       738,
			ZoneWorldId: 1,
			SenderGuid:  uint64(p.uid),
			SenderPlayerInfo: &playerInfov1.PlayerBaseInfo{
				Name:  p.settings.db.Base.Name,
				Level: int32(p.settings.db.Base.Level),
			},
			MessageType: chatv1.MessageChannelType_MessageType_PrivateChat,
			Message:     params,
			HeadFrameId: 1001,
		},
		ReceiverGuids:  []uint64{p.uid},
		NeedTextDetect: false,
	}

	reply, err := grpcMgr.GetChatService().SendChatMessage(context.Background(), g2gChatMsg)
	if err != nil {
		log.Error("SendChatMessage failed", log.Err(err))
		return false
	}

	// bs, _ := proto.Marshal(g2gChatMsg)
	// d := &pbgen.GrpcMessage{
	//	ServiceId: pbgen.ChatService, //?如何订一
	//	FuncName:  "SendChatMessage",
	//	Data:      bs,
	// }
	// msg := &actor.Message{
	//	Uid:  p.Uid(),
	//	Data: d,
	// }
	// reply := p.SendToGrpcService(msg)
	// if _, ok := reply.(*pbgen.SendChatMessageReply); ok {
	//
	// }
	log.Info("SendChatMessage", log.Kv("body", reply))
	return true
}

// 27
func (p *Player) handlerJohnTest1(params string) bool {
	// 使用单例方法发送系统消息
	// return GetInstance().SendSystemMsg(p.uid, params)
	// return GetInstance().SendSystemMsg(p.uid, params)
	return GetInstance().SendSystemMsg(p.uid, params)
}

func (p *Player) handleGMAddExp(params string) bool {
	// 解析经验值
	exp, err := strconv.ParseInt(params, 10, 64)
	if err != nil {
		log.Error("GmAddExp ParseInt error")
		return false
	}

	// 添加经验
	p.AddExp(exp, 0, 0, "")
	log.Info("GmAddExp success", log.Kv("exp", exp))
	return true
}

// ChangeName 通用接口：改名
func (p *Player) ChangeName(oldName, name string) bool {
	if p.IfChangeNameSuccess(oldName) {
		p.db.Base.Name = name
	}
	return true
}

// IfChangeNameSuccess 通用接口：改名成功
func (p *Player) IfChangeNameSuccess(name string) bool {
	return p.db.Base.Name == name
}

// handleGMAddBagItem 处理添加背包物品的 GM 命令
// 格式：itemId count
func (p *Player) handleGMAddBagItem(params string) bool {
	// 分割物品 ID 和数量
	parts := strings.Split(params, " ")
	if len(parts) != 2 {
		log.Error("GmAddBagItem invalid params, format: <itemId> <count>")
		return false
	}

	// 解析物品 ID
	itemId, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GmAddBagItem itemId must be number")
		return false
	}

	// 解析数量
	count, err := strconv.ParseInt(parts[1], 10, 32)
	if err != nil {
		log.Error("GmAddBagItem count must be number")
		return false
	}

	// 添加物品
	success := p.bag.AddNormalItem(int32(itemId), int32(count), 1)
	if success {
		log.Info("GmAddBagItem success", log.Kv("itemId", itemId), log.Kv("count", count))
		return true
	} else {
		log.Error("GmAddBagItem failed", log.Kv("itemId", itemId), log.Kv("count", count))
		return false
	}
}

// handleGMDelBagItem 处理删除背包物品的 GM 命令
// 格式：itemId [count]，count 可选，不填则删除全部该类型物品
func (p *Player) handleGMDelBagItem(params string) bool {
	// 分割物品 ID 和数量
	parts := strings.Split(params, " ")
	if len(parts) == 0 || len(parts) > 2 {
		log.Error("GmDelBagItem invalid params, format: <itemId> [count]")
		return false
	}

	// 解析物品 ID
	itemId, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GmDelBagItem itemId must be number", log.Kv("input", parts[0]))
		return false
	}

	if len(parts) == 1 {
		// 删除全部该类型物品
		p.bag.ClearItemsByID(int32(itemId))
		log.Info("GmDelBagItem success clear all", log.Kv("itemId", itemId))
		return false
	}

	// 解析数量
	count, err := strconv.ParseInt(parts[1], 10, 32)
	if err != nil {
		log.Error("GmDelBagItem count must be number", log.Kv("input", parts[1]))
		return false
	}

	// 删除物品
	success := p.bag.DeductNormalItem(int32(itemId), int32(count), 1)
	if success {
		log.Info("GmDelBagItem success ", log.Kv("itemId", itemId), log.Kv("count", count))
		return true
	} else {
		log.Error("GmDelBagItem failed ", log.Kv("itemId", itemId), log.Kv("count", count))
		return false
	}
}

// handleGMAddBagItem 处理添加货币的 GM 命令
// 格式：itemId count
func (p *Player) handleGMAddMoney(params string) bool {
	// 分割 GM 指令 ID 和数量
	parts := strings.Split(params, " ")
	if len(parts) != 2 {
		log.Error("GmAddMoney invalid params, format: <itemId> <count>")
		return false
	}
	// 解析 ID
	moneyId, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GmAddMoney itemId must be number")
		return false
	}
	// 解析数量
	moneyNum, err := strconv.ParseInt(parts[1], 10, 32)
	if err != nil {
		log.Error("GmAddMoney count must be number")
		return false
	}
	// 添加物品
	success := p.money.AddMoney(int32(moneyId), int32(moneyNum), 1)
	if success {
		log.Info("GmAddMoney success", log.Kv("itemId", moneyId), log.Kv("moneyNum", moneyNum))
		return true
	} else {
		log.Error("GmAddMoney failed", log.Kv("itemId", moneyId), log.Kv("moneyNum", moneyNum))
		return false
	}
}

// handleGMDelMoney 处理删除货币的 GM 命令
// 格式：itemId [count]，count 可选，不填则删除全部该类型物品
func (p *Player) handleGMDelMoney(params string) bool {
	// 分割 GM 指令 ID 和数量
	parts := strings.Split(params, " ")
	if len(parts) == 0 || len(parts) > 2 {
		log.Error("GMDelMoney invalid params, format: <itemId> [count]")
		return false
	}
	// 解析 ID
	moneyId, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GMDelMoney itemId must be number", log.Kv("input", parts[0]))
		return false
	}
	// 解析数量
	moneyNum, err := strconv.ParseInt(parts[1], 10, 32)
	if err != nil {
		log.Error("GMDelMoney count must be number", log.Kv("input", parts[1]))
		return false
	}
	// 删除物品
	success := p.money.DelMoney(int32(moneyId), int32(moneyNum), 1)
	if success {
		log.Info("GMDelMoney success ", log.Kv("moneyId", moneyId), log.Kv("moneyNum", moneyNum))
		return true
	} else {
		log.Error("GMDelMoney failed ", log.Kv("moneyId", moneyId), log.Kv("moneyNum", moneyNum))
		return false
	}
}

// handleGMItemDrop 处理道具掉落 GM 命令
func (p *Player) handleGMItemDrop(params string) bool {
	// 分割 GM 指令 ID 和数量
	parts := strings.Split(params, " ")
	if len(parts) == 0 || len(parts) > 2 {
		log.Error("GMItemDrop invalid params, format: <itemId> [count]")
		return false
	}
	// 解析 ID
	dropId, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GMItemDrop itemId must be number", log.Kv("input", parts[0]))
		return false
	}
	// 执行掉落逻辑
	p.item.GetDropItemByDropID(int32(dropId), 1)
	return true
}

// handleGMUseItem 处理使用道具 GM 命令
func (p *Player) handleGMUseItem(params string) bool {
	// 分割 GM 指令 ID 和数量
	parts := strings.Split(params, " ")
	if len(parts) == 0 || len(parts) > 2 {
		log.Error("GMUseItem invalid params, format: <itemId> [count]")
		return false
	}
	// 解析 ID
	itemId, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GMUseItem itemId must be number", log.Kv("input", parts[0]))
		return false
	}
	// 添加道具
	p.bag.AddNormalItem(int32(itemId), 1, 1)
	// 执行道具使用逻辑
	msg := &public.UseItemParam{
		ItemId: int32(itemId),
		UseNum: 1,
	}
	p.item.UseItem(msg)
	return true
}

// handleGMMailLogic 处理邮件逻辑 GM 命令
func (p *Player) handleGMMailLogic(params string) bool {
	// 分割 GM 指令 ID 和数量
	parts := strings.Split(params, " ")
	if len(parts) < 1 || len(parts) > 2 {
		log.Error("GMMailLogic invalid params")
		return false
	}
	// 解析 ID
	mailParam1, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GMMailLogic param1 must be number", log.Kv("input", parts[0]))
		return false
	}
	// 根据指令执行逻辑
	if 1 == mailParam1 {
		mailParam2, err := strconv.ParseInt(parts[1], 10, 32)
		if err != nil {
			log.Error("GMMailLogic param1 and param2 must be number", log.Kv("input", parts[1]))
			return false
		}
		mailId := mailParam2
		// 添加一封邮件
		mailTableInfo := table.GetTable().TableMailTemplateConfig.GetById(int32(mailId))
		if mailTableInfo != nil {
			mailType := public.MailType(mailTableInfo.MailType)
			title := "hello~ Mail is come!"
			content := "hello~ Welcome to the game!"
			createTime := time.Now().Unix()
			expireTime := int64(mailTableInfo.MailValidityDays)
			senderGid := int64(p.pid.Id())
			senderName := "Gamer"
			receiverGid := int64(1001)
			receiver := ""
			itemList := make(map[int32]int32)
			itemList[1] = 2000
			itemList[1000002] = 20
			mailReason := public.MailReasonType(1)
			syncClient := false
			p.mail.AddMail(mailType, uuid.GenMailID(), 0, title, content, createTime, expireTime, senderGid, senderName, receiverGid, receiver, itemList, mailReason, syncClient)
		}
	} else if 2 == mailParam1 {
		p.mail.SyncMailAllList()
	} else if 3 == mailParam1 {
		//mailInfo := p.mail.GmGetFirstMail()
		//p.mail.ReadMail(mailInfo.Id)
	} else if 4 == mailParam1 {
		//mailInfo := p.mail.GmGetFirstMail()
		//p.mail.ReceiveMail(mailInfo.Id)
	} else if 5 == mailParam1 {
		p.mail.ReceiveAllMail()
	} else if 6 == mailParam1 {
		//mailInfo := p.mail.GmGetFirstMail()
		//p.mail.DelMail(mailInfo.Id)
	} else if 7 == mailParam1 {
		p.mail.DelAllReadMail()
	}
	return true
}

func (p *Player) handleGMUnlockHeadIcon(params string) bool {
	// 分割 GM 指令 ID
	parts := strings.Split(params, " ")
	if len(parts) == 0 || len(parts) > 2 {
		log.Error("GMAddHeadIcon invalid params, format: <headIconId>")
		return false
	}
	// 解析 ID
	headIconId, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GMAddHeadIcon headIconId must be number", log.Kv("input", parts[0]))
		return false
	}
	// 执行解锁头像逻辑
	out := p.settings.AddHeadIcon(int32(headIconId))
	p.SendToClient(rpc_def.LCSyncHeadIconList, out, false)

	return true
}

func (p *Player) handleGMAddHeadFrame(params string) bool {
	// 分割 GM 指令 ID
	parts := strings.Split(params, " ")
	if len(parts) == 0 || len(parts) > 2 {
		log.Error("GMAddHeadFrame invalid params, format: <headFrameId>")
		return false
	}
	// 解析 ID
	headFrameId, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GMAddHeadFrame headFrameId must be number", log.Kv("input", parts[0]))
		return false
	}
	// 执行解锁头像逻辑
	out := p.settings.AddHeadFrame(int32(headFrameId))
	p.SendToClient(rpc_def.LCSyncHeadFrameList, out, false)

	return true
}

// handleGMSevenSignIn 处理七日签到 GM 命令
func (p *Player) handleGMSevenSignIn(params string) bool {
	// 分割 GM 指令 ID 和数量
	parts := strings.Split(params, " ")
	if len(parts) < 1 || len(parts) > 2 {
		log.Error("GmSevenSignIn invalid params")
		return false
	}
	// 解析 ID
	comParam1, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GmSevenSignIn param1 must be number", log.Kv("input", parts[0]))
		return false
	}
	// 根据指令执行逻辑
	if 1 == comParam1 {
		p.sevenSignIn.SignInData = make(map[int32]int32)
		p.sevenSignIn.SaveDB()
	} else if 2 == comParam1 {
		p.sevenSignIn.OnCrossDay(true, 1)
	} else if 13 == comParam1 {
		p.sevenSignIn.HandleGetSevenSignInData()
	} else if 14 == comParam1 {
		p.sevenSignIn.HandleGetSevenSignInAward(1)
	}
	return true
}

// handleGMTopUpRebate 处理充值返利 GM 命令
func (p *Player) handleGMTopUpRebate(params string) bool {
	// 分割 GM 指令 ID 和数量
	parts := strings.Split(params, " ")
	if len(parts) < 1 || len(parts) > 2 {
		log.Error("GMTopUpRebate invalid params")
		return false
	}
	// 解析 ID
	comParam1, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GMTopUpRebate param1 must be number", log.Kv("input", parts[0]))
		return false
	}
	// 根据指令执行逻辑
	if 1 == comParam1 {
		p.topUpRebate.TopUpRebateData = make(map[int32]dbstruct.DBTopupRebate)
		p.topUpRebate.TopUpRebateData[1] = dbstruct.DBTopupRebate{
			TaskId:    1,
			TaskState: 0,
			TaskParam: 0,
		}
		p.topUpRebate.SaveDB()
	} else if 2 == comParam1 {
		p.topUpRebate.OnCrossDay(true, 1)
	} else if 11 == comParam1 {
		p.topUpRebate.OnMonitorTopUpOperate(nil)
	} else if 12 == comParam1 {
		p.topUpRebate.RequestGetTopUpRebateData()
	} else if 13 == comParam1 {
		p.topUpRebate.RequestGetTopUpRebateAward(1)
	}
	return true
}

// handleGMMonthlyCard 处理月卡 GM 命令
func (p *Player) handleGMMonthlyCard(params string) bool {
	// 分割 GM 指令 ID 和数量
	parts := strings.Split(params, " ")
	if len(parts) < 1 || len(parts) > 2 {
		log.Error("GMMonthlyCard invalid params")
		return false
	}
	// 解析 ID
	comParam1, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GMMonthlyCard param1 must be number", log.Kv("input", parts[0]))
		return false
	}
	// 根据指令执行逻辑
	if 1 == comParam1 {
		p.monthlyCard.ExpirationTime = 0
		p.monthlyCard.SaveDB()
	} else if 2 == comParam1 {
		p.monthlyCard.OnCrossDay(true, 1)
	} else if 11 == comParam1 {
		p.monthlyCard.OnMonthlyCardBuyOperate(1)
	} else if 12 == comParam1 {
		p.monthlyCard.RequestGetMonthlyCardData()
	}
	return true
}

func (p *Player) handleGMMonthlyCardNew(params string) bool {
	// 分割 GM 指令 ID 和数量
	parts := strings.Split(params, " ")
	if len(parts) < 1 || len(parts) > 2 {
		log.Error("GMMonthlyCard invalid params")
		return false
	}
	// 解析 ID
	comParam1, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GMMonthlyCard param1 must be number", log.Kv("input", parts[0]))
		return false
	}
	if 1 == comParam1 {
		p.monthlyCardNew.ResetMonthlyCardNewData()
		p.monthlyCardNew.SaveDB()
	} else if 2 == comParam1 {
		p.monthlyCardNew.OnCrossDay(true, 1)
	} else if 11 == comParam1 {
		p.monthlyCardNew.RequestGetMonthlyCardNewData()
	} else if 12 == comParam1 {
		p.monthlyCardNew.MonthlyCardBuyNewOperate(1) // 超值月卡
	} else if 13 == comParam1 {
		p.monthlyCardNew.MonthlyCardBuyNewOperate(2) // 至尊月卡
	} else if 14 == comParam1 {
		p.monthlyCardNew.RequestGetMonthlyCardNewExtraReward()
	}
	return true
}

// handleGMGradedFund 处理等级基金 GM 命令
func (p *Player) handleGMGradedFund(params string) bool {
	// 分割 GM 指令 ID 和数量
	parts := strings.Split(params, " ")
	if len(parts) < 1 || len(parts) > 2 {
		log.Error("GMGradedFund invalid params")
		return false
	}
	// 解析 ID
	comParam1, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GMGradedFund param1 must be number", log.Kv("input", parts[0]))
		return false
	}
	// 根据指令执行逻辑
	if 1 == comParam1 {
		p.gradedFund.GradedFundData = make(map[int32]dbstruct.DBGradedFund)
		p.gradedFund.SaveDB()
	} else if 2 == comParam1 {
		p.gradedFund.OnCrossDay(true, 1)
	} else if 11 == comParam1 {
		p.gradedFund.RequestGetGradedFundData()
	} else if 12 == comParam1 {
		p.gradedFund.RequestBuyGradedFund(1)
	} else if 13 == comParam1 {
		p.gradedFund.RequestGetGradedFundComWeal(1)
	} else if 14 == comParam1 {
		p.gradedFund.RequestGetGradedFundSuperWeal(1)
	}
	return true
}

// handleGMMission 处理任务 GM 命令
func (p *Player) handleGMMission(params string) bool {

	parts := strings.Split(params, " ")
	if len(parts) < 1 || len(parts) > 2 {
		log.Error("GMMission invalid params")
		return false
	}

	userType, err := strconv.ParseInt(parts[0], 10, 64)

	if err != nil {
		log.Error("GMMission ParseInt 0 error")
		return false
	}

	if 0 == userType {
		p.Mission().OnCrossDay(true, 1)
	} else if 1 == userType {

		missionId := int64(0)

		if len(parts) != 2 {
			log.Error("GMMission invalid params")
			return false
		}

		missionId, err = strconv.ParseInt(parts[1], 10, 64)
		if err != nil {
			log.Error("GmAddExp ParseInt 1 error")
			return false
		}

		var curMissionInfo *MissionInfo

		if curMissionInfo, err = p.Mission().UpdMissionState(int32(missionId), public.MissionState_MissionState_Finished); err != nil {
			log.Error("handlerGMMissionComplete UpdMissionState error", log.Kv("missionId", missionId))
			return false
		}

		_, err = p.Mission().RequestSubmitMission(func(mission *table_data.TableMission) bool {

			if curMissionInfo.missionCfg.ID != curMissionInfo.missionCfg.ID {
				return false
			}

			return true
		}, true)

		if err != nil {
			log.Error("handlerGMMissionComplete RequestSubmitMission error", log.Kv("missionId", missionId))
			return false
		}
	}

	return true
}

// handleGMItemOperate 处理道具操作 GM 命令
func (p *Player) handleGMItemOperate(param int32, params string) bool {
	// 分割 GM 指令 ID 和数量
	parts := strings.Split(params, " ")
	if len(parts) < 1 || len(parts) > 2 {
		log.Error("GMMission invalid params")
		return false
	}
	// 解析 ID
	comParam1, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GMMission param1 must be number", log.Kv("input", parts[0]))
		return false
	}
	// 解析数量
	comParam2, err := strconv.ParseInt(parts[1], 10, 32)
	if err != nil {
		log.Error("GmAddBagItem count must be number")
		return false
	}
	// 根据指令执行逻辑
	if 1 == param {
		p.item.AddItem(int32(comParam1), int32(comParam2), 1)
	} else if 2 == param {
		p.item.DelItem(int32(comParam1), int32(comParam2), 1)
	}
	return true
}

func (p *Player) handlerGMPay(params string) bool {
	req := &cs2.CLPaymentPreRequestReq{
		ModuleType:      1,
		GoodsPrice:      100,
		GoodsRegisterId: "grid",
		GameGoodsId:     "10001",
		ChannelId:       "4001",
	}
	p.OnPaymentPreRequest(req)
	return true
}

func (p *Player) handlerGMPayTest(params string) bool {
	split := strings.SplitN(params, " ", 2)
	if len(split) < 2 {
		log.Error("handlerGMPayTest invalid params, <type> <params>")
		return false
	}
	nType, err := strconv.Atoi(split[0])
	if err != nil {
		return false
	}
	req := &cs2.CLPaymentPreRequestReq{
		ModuleType:      int32(nType),
		GoodsPrice:      100,
		GoodsRegisterId: "grid",
		GameGoodsId:     split[1],
		ChannelId:       "4001",
	}
	p.OnPaymentRequestTest(req)
	return true
}

func (p *Player) handlerGMADDMilitaryLevel(params string) bool {
	addLevel, err := strconv.ParseInt(params, 10, 64)
	if err != nil {
		log.Error("GmAddExp ParseInt error")
		return false
	}

	p.addMilitaryLevel(int32(addLevel))
	return true
}

func (p *Player) handlerGMTryUnlockTimeFunc(params string) bool {
	timeD, err := strconv.ParseInt(params, 10, 64)
	if err != nil {
		log.Error("GmAddExp ParseInt error")
		return false
	}

	p.FuncOpen().tryUnlockTimeFunc(timeD)
	return true
}

func (p *Player) handlerGMTestReq() bool {

	if _, err := p.CommonBoxReward().GetReward(1, []int32{0}, true); err != nil {
		log.Error("handlerGMTestReq GetReward error")
		return false
	}

	return true
}

func (p *Player) handleGMTimeShop(params string) bool {
	// 分割 GM 指令 ID 和数量
	parts := strings.Split(params, " ")
	if len(parts) < 1 || len(parts) > 2 {
		log.Error("GMMonthlyCard invalid params")
		return false
	}
	// 解析 ID
	comParam1, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GMMonthlyCard param1 must be number", log.Kv("input", parts[0]))
		return false
	}

	switch comParam1 {
	case 1: // 清空当前购买记录和数据，重新初始化
		p.TimeShop().timeGoods = make(map[int32]*dbstruct.TimeGoodsData)
		p.TimeShop().InitTemplate()
		p.TimeShop().SaveDB()
		break
	case 2:
		p.TimeShop().OnCrossDay(true, 1)
		break
	case 3:
		p.TimeShop().onCrossWeek(true, 1)
		break
	case 4:
		p.TimeShop().onCrossMonth(true, 1)
		break
	case 11:
		break
	default:
		log.Error("OnGmCommand not find type")
	}

	return true
}

// HandleGlobalMail 处理全局邮件
func (p *Player) HandleGlobalMail(mailData *public.PBMail) {
	if p.mail == nil {
		log.Error("mail module not initialized", log.Kv("uid", p.uid))
		return
	}

	//// 复制邮件数据，避免共享同一个对象
	//newMailData := &dbstruct.MailData{
	//	Id:              mailData.Id,
	//	Type:            mailData.Type,
	//	SubType:         mailData.SubType,
	//	TitleStr:        mailData.TitleStr,
	//	ContentStr:      mailData.ContentStr,
	//	Status:          int32(public.MailStateType_MailStateType_UnRead), // 设置为未读状态
	//	CreateTime:      mailData.CreateTime,
	//	ExpireTime:      mailData.ExpireTime,
	//	OfflineFlag:     mailData.OfflineFlag,
	//	SenderId:        mailData.SenderId,
	//	SenderName:      mailData.SenderName,
	//	ReceiverId:      int64(p.uid), // 设置接收者为当前玩家，转换为int64
	//	Reason:          mailData.Reason,
	//	Level:           mailData.Level,
	//	PlatId:          mailData.PlatId,
	//	SenderHeadIcon:  mailData.SenderHeadIcon,
	//	GoodsItems:      make([]*dbstruct.CommonKeyValue, 0, len(mailData.GoodsItems)),
	//	IntParamList:    make([]int32, len(mailData.IntParamList)),
	//	StringParamList: make([]string, len(mailData.StringParamList)),
	//}
	//
	//// 复制物品列表
	//for _, item := range mailData.GoodsItems {
	//	newMailData.GoodsItems = append(newMailData.GoodsItems, &dbstruct.CommonKeyValue{
	//		Key:   item.Key,
	//		Value: item.Value,
	//	})
	//}
	//
	//// 复制参数列表
	//copy(newMailData.IntParamList, mailData.IntParamList)
	//copy(newMailData.StringParamList, mailData.StringParamList)
	//
	//// 添加到玩家邮箱
	//p.mail.MailDataList[newMailData.Id] = newMailData
	p.mail.UpdateExcessMail()

	// 如果玩家在线，同步邮件列表
	if p.IsOnline() {
		p.mail.SyncMailAllList()
	}

	log.Info("added global mail to player", log.Kv("uid", p.uid), log.Kv("mailId", mailData.Id))
}

// handleGMGachaBonus 处理千抽GM命令
func (p *Player) handleGMGachaBonus(params string) bool {
	// 分割 GM 指令 ID 和数量
	parts := strings.Split(params, " ")
	if len(parts) < 1 || len(parts) > 2 {
		log.Error("GmGachaBonus invalid params")
		return false
	}
	// 解析 ID
	comParam1, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GmGachaBonus param1 must be number", log.Kv("input", parts[0]))
		return false
	}
	// 根据指令执行逻辑
	if 1 == comParam1 {
		p.gachaBonus.PullData = make(map[int32]int32)
		p.gachaBonus.SaveDB()
	} else if 2 == comParam1 {
		p.gachaBonus.HandleGetGachaBonusData()
	} else if 3 == comParam1 {
		p.gachaBonus.HandleGetGachaBonusAward(1)
	}
	return true
}

// handleGMQuestionnaire 处理问卷GM命令
func (p *Player) handleGMQuestionnaire(params string) bool {
	parts := strings.Split(params, " ")
	if len(parts) < 1 || len(parts) > 2 {
		log.Error("GMQuestionnaire invalid params")
		return false
	}
	qid, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GMQuestionnaire param1 must be number", log.Kv("input", parts[0]))
		return false
	}
	p.questionnaire.GetQuestionnaireReward(int32(qid))
	return true
}
func (p *Player) handleGMVipExp(params string) bool {
	parts := strings.Split(params, " ")
	if len(parts) > 1 {
		log.Error("GMVipExp invalid params")
		return false
	}
	// exp, err := strconv.ParseInt(parts[0], 10, 32)
	// if err != nil {
	//	log.Error("GMQuestionnaire param1 must be number", log.Kv("input", parts[0]))
	//	return false
	// }
	// p.vip.AddVipBoxExp(int32(exp))
	return true
}

func (p *Player) handleAddTotalRecharge(params string) bool {
	parts := strings.Split(params, " ")
	if len(parts) > 1 {
		log.Error("GMVipExp invalid params")
		return false
	}
	p.totalRecharge.GmAddTotalRecharge()
	return true
}

func (p *Player) handleHeavenlyDao(params string) bool {
	parts := strings.Split(params, " ")
	if len(parts) > 1 {
		log.Error("GMVipExp invalid params")
		return false
	}
	// 解析 ID
	comParam1, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GmHeavenlyDao param1 must be number", log.Kv("input", parts[0]))
		return false
	}
	// 根据指令执行逻辑
	if 1 == comParam1 {
		p.heavenlyDao.GmHeavenlyDaoPromote() // 直接晋升
	}
	return true
}

func (p *Player) OnPlayerOtherInfo(in *cs2.CLPlayerOtherInfo) {

	resp := &cs2.LCPlayerOtherInfo{
		OtherPlayer: p.PlayerOtherInfo(),
	}
	p.SendToClient(rpc_def.LCPlayerOtherInfo, resp, false)
}

func (p *Player) PlayerOtherInfo() *public.PBPlayerOtherInfo {
	//todo 后续查redis
	return &public.PBPlayerOtherInfo{
		PlatformID: int64(p.db.Base.Uid),
		NickName:   p.db.Base.Name,
		HeadIcon:   p.settings.GetHeadIconById(p.db.Base.HeadIcon),
		Level:      int32(p.db.Base.Level),
		Exp:        p.db.Base.Exp,
	}
}

// handleGMHeroAdd GM命令：添加英雄
// 格式：heroId [经验值]（可选参数，默认为0）
func (p *Player) handleGMHeroAdd(params string) bool {
	parts := strings.Split(params, " ")
	if len(parts) < 1 || len(parts) > 2 {
		log.Error("GM HeroAdd invalid params format: heroId [expAmount]")
		return false
	}

	// 解析英雄ID
	heroId, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GM HeroAdd heroId must be number", log.Kv("input", parts[0]))
		return false
	}

	// 解析经验值（可选参数）
	expAmount := int32(0)
	if len(parts) >= 2 {
		expValue, err := strconv.ParseInt(parts[1], 10, 32)
		if err != nil {
			log.Error("GM HeroAdd expAmount must be number", log.Kv("input", parts[1]))
			return false
		}
		expAmount = int32(expValue)
	}

	// 添加英雄
	result := p.hero.AddHeroExp(int32(heroId), expAmount)
	if result != error_code.ERROR_OK {
		log.Error("GM HeroAdd failed", log.Kv("heroId", heroId), log.Kv("expAmount", expAmount), log.Kv("error", result))
		return false
	}

	log.Info("GM HeroAdd success", log.Kv("player_id", p.Uid()), log.Kv("heroId", heroId), log.Kv("expAmount", expAmount))
	return true
}

// handleGMHeroRemove GM命令：移除英雄
// 格式：heroId
func (p *Player) handleGMHeroRemove(params string) bool {
	parts := strings.Split(params, " ")
	if len(parts) != 1 {
		log.Error("GM HeroRemove invalid params format: heroId")
		return false
	}

	// 解析英雄ID
	heroId, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GM HeroRemove heroId must be number", log.Kv("input", parts[0]))
		return false
	}

	// 使用Hero模块的RemoveHero方法，会同步更新阵容
	code := p.hero.RemoveHero(int32(heroId))
	if code != error_code.ERROR_OK {
		log.Error("GM HeroRemove failed", log.Kv("heroId", heroId), log.Kv("error_code", code))
		return false
	}

	log.Info("GM HeroRemove success", log.Kv("player_id", p.Uid()), log.Kv("heroId", heroId))
	return true
}

// handleGMHeroLevel GM命令：设置英雄等级
// 格式：heroId level
func (p *Player) handleGMHeroLevel(params string) bool {
	parts := strings.Split(params, " ")
	if len(parts) != 2 {
		log.Error("GM HeroLevel invalid params format: heroId level")
		return false
	}

	// 解析英雄ID
	heroId, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GM HeroLevel heroId must be number", log.Kv("input", parts[0]))
		return false
	}

	// 解析等级值
	level, err := strconv.ParseInt(parts[1], 10, 32)
	if err != nil {
		log.Error("GM HeroLevel level must be number", log.Kv("input", parts[1]))
		return false
	}

	// 确保等级不小于1
	if level < 1 {
		log.Error("GM HeroLevel level must be >= 1", log.Kv("input", level))
		return false
	}

	// 查找英雄
	hero := p.hero.GetHeroById(int32(heroId))
	if hero == nil {
		// 英雄不存在，创建新英雄并设置等级
		newHero := &public.PBHeroInfo{
			HeroId:    int32(heroId),
			HeroLevel: int32(level),
			Exp:       0,
		}
		p.hero.db.HeroList = append(p.hero.db.HeroList, newHero)
		log.Info("GM HeroLevel created new hero with level",
			log.Kv("player_id", p.Uid()),
			log.Kv("heroId", heroId),
			log.Kv("level", level))
	} else {
		// 英雄存在，设置等级
		hero.HeroLevel = int32(level)
		log.Info("GM HeroLevel set hero level",
			log.Kv("player_id", p.Uid()),
			log.Kv("heroId", heroId),
			log.Kv("level", level))
	}

	return true
}

// handleGMHeroAddAll GM命令：获得所有英雄
// 格式：[level]（可选参数，默认为1级）
func (p *Player) handleGMHeroAddAll(params string) bool {
	// 解析等级参数（可选）
	level := int32(1) // 默认等级1
	if len(params) > 0 {
		levelValue, err := strconv.ParseInt(params, 10, 32)
		if err != nil {
			log.Error("GM HeroAddAll level must be number", log.Kv("input", params))
			return false
		}
		if levelValue < 1 {
			log.Error("GM HeroAddAll level must be >= 1", log.Kv("input", levelValue))
			return false
		}
		level = int32(levelValue)
	}

	// 创建英雄ID集合，用于快速检查是否已拥有英雄
	existingHeroes := make(map[int32]bool)
	for _, hero := range p.hero.db.HeroList {
		existingHeroes[hero.HeroId] = true
	}

	// 遍历TableHero表，添加所有英雄
	addedCount := 0
	table.GetTable().TableHero.Foreach(func(heroData *table_data.TableHero) bool {
		// 只添加未拥有的英雄
		if !existingHeroes[heroData.ID] {
			newHero := &public.PBHeroInfo{
				HeroId:    heroData.ID,
				HeroLevel: level,
				Exp:       0,
			}
			p.hero.db.HeroList = append(p.hero.db.HeroList, newHero)
			addedCount++
		}
		return true // 继续遍历
	})

	log.Info("GM HeroAddAll success",
		log.Kv("player_id", p.Uid()),
		log.Kv("added_heroes", addedCount),
		log.Kv("total_heroes", len(p.hero.db.HeroList)),
		log.Kv("level", level))
	return true
}

// handleGMHero 处理英雄相关的 GM 命令
// 格式：<subType> <params>
// 1=添加英雄 format: 1 heroId [expAmount]
// 2=移除英雄 format: 2 heroId
// 3=设置英雄等级 format: 3 heroId level
// 4=获得所有英雄 format: 4 [level]
// 5=设置所有英雄等级 format: 5 level
// 6=给指定英雄增加经验值 format: 6 heroId expAmount
// 7=设置英雄觉醒等级 format: 7 heroId awakeLevel
func (p *Player) handleGMHero(params string) bool {
	parts := strings.SplitN(params, " ", 2)
	if len(parts) < 1 {
		log.Error("GmHero invalid params, format: <subType> <params>")
		return false
	}

	// 解析子命令类型
	subType, err := strconv.Atoi(parts[0])
	if err != nil {
		log.Error("GmHero subType must be number",
			log.Kv("input", parts[0]))
		return false
	}

	// 获取剩余参数
	var cmdParams string
	if len(parts) > 1 {
		cmdParams = parts[1]
	}

	switch subType {
	case game_def.GmHeroSubAdd: // 添加英雄
		return p.handleHeroAdd(cmdParams)
	case game_def.GmHeroSubRemove: // 移除英雄
		return p.handleHeroRemove(cmdParams)
	case game_def.GmHeroSubSetLevel: // 设置英雄等级
		return p.handleHeroSetLevel(cmdParams)
	case game_def.GmHeroSubAddAll: // 获得所有英雄
		return p.handleHeroAddAll(cmdParams)
	case game_def.GmHeroSubSetAllLvl: // 设置所有英雄等级
		return p.handleHeroSetAllLvl(cmdParams)
	case game_def.GmHeroSubAddExp: // 给指定英雄增加经验值
		return p.handleHeroAddExp(cmdParams)
	case game_def.GmHeroSubSetAwakeLevel: // 设置英雄觉醒等级
		return p.handleHeroSetAwakeLevel(cmdParams)
	default:
		log.Error("GmHero unknown subType", log.Kv("subType", subType))
		return false
	}
}

// handleHeroAdd 添加英雄
// 格式：heroId [expAmount]
// 如果英雄不存在，创建英雄；如果英雄已存在，添加碎片（经验值）
func (p *Player) handleHeroAdd(params string) bool {
	parts := strings.Split(params, " ")
	if len(parts) < 1 || len(parts) > 2 {
		log.Error("GM HeroAdd invalid params format: heroId [expAmount]")
		return false
	}

	// 解析英雄ID
	heroId, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GM HeroAdd heroId must be number", log.Kv("input", parts[0]))
		return false
	}

	// 解析经验值（可选参数）
	expAmount := int32(1) // 默认添加1个碎片
	if len(parts) >= 2 {
		expValue, err := strconv.ParseInt(parts[1], 10, 32)
		if err != nil {
			log.Error("GM HeroAdd expAmount must be number", log.Kv("input", parts[1]))
			return false
		}
		expAmount = int32(expValue)
	}

	// 检查英雄是否已存在
	existingHero := p.hero.GetHeroById(int32(heroId))
	isNewHero := existingHero == nil

	// 添加英雄或碎片
	result := p.hero.AddHeroExp(int32(heroId), expAmount)
	if result != error_code.ERROR_OK {
		log.Error("GM HeroAdd failed", log.Kv("heroId", heroId), log.Kv("expAmount", expAmount), log.Kv("error", result))
		return false
	}

	if isNewHero {
		log.Info("GM HeroAdd success - new hero created",
			log.Kv("player_id", p.Uid()),
			log.Kv("heroId", heroId),
			log.Kv("expAmount", expAmount))
	} else {
		log.Info("GM HeroAdd success - fragments added",
			log.Kv("player_id", p.Uid()),
			log.Kv("heroId", heroId),
			log.Kv("fragmentsAdded", expAmount))
	}
	return true
}

// handleHeroRemove 移除英雄
// 格式：heroId
func (p *Player) handleHeroRemove(params string) bool {
	parts := strings.Split(params, " ")
	if len(parts) != 1 {
		log.Error("GM HeroRemove invalid params format: heroId")
		return false
	}

	// 解析英雄ID
	heroId, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GM HeroRemove heroId must be number", log.Kv("input", parts[0]))
		return false
	}

	// 使用Hero模块的RemoveHero方法，会同步更新阵容
	code := p.hero.RemoveHero(int32(heroId))
	if code != error_code.ERROR_OK {
		log.Error("GM HeroRemove failed", log.Kv("heroId", heroId), log.Kv("error_code", code))
		return false
	}

	log.Info("GM HeroRemove success", log.Kv("player_id", p.Uid()), log.Kv("heroId", heroId))
	return true
}

// handleHeroSetLevel 设置英雄等级
// 格式：heroId level
func (p *Player) handleHeroSetLevel(params string) bool {
	parts := strings.Split(params, " ")
	if len(parts) != 2 {
		log.Error("GM HeroLevel invalid params format: heroId level")
		return false
	}

	// 解析英雄ID
	heroId, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GM HeroLevel heroId must be number", log.Kv("input", parts[0]))
		return false
	}

	// 解析等级值
	level, err := strconv.ParseInt(parts[1], 10, 32)
	if err != nil {
		log.Error("GM HeroLevel level must be number", log.Kv("input", parts[1]))
		return false
	}

	// 确保等级不小于1
	if level < 1 {
		log.Error("GM HeroLevel level must be >= 1", log.Kv("input", level))
		return false
	}

	// 查找英雄
	hero := p.hero.GetHeroById(int32(heroId))
	if hero == nil {
		// 英雄不存在，创建新英雄并设置等级
		newHero := &public.PBHeroInfo{
			HeroId:    int32(heroId),
			HeroLevel: int32(level),
			Exp:       0,
		}
		p.hero.db.HeroList = append(p.hero.db.HeroList, newHero)
		log.Info("GM HeroLevel created new hero with level",
			log.Kv("player_id", p.Uid()),
			log.Kv("heroId", heroId),
			log.Kv("level", level))
	} else {
		// 英雄存在，设置等级
		hero.HeroLevel = int32(level)
		log.Info("GM HeroLevel set hero level",
			log.Kv("player_id", p.Uid()),
			log.Kv("heroId", heroId),
			log.Kv("level", level))
	}

	return true
}

// handleHeroAddAll 获得所有英雄
// 格式：[level]（可选参数，默认为1级）
func (p *Player) handleHeroAddAll(params string) bool {
	// 解析等级参数（可选）
	level := int32(1) // 默认等级1
	if len(params) > 0 {
		levelValue, err := strconv.ParseInt(params, 10, 32)
		if err != nil {
			log.Error("GM HeroAddAll level must be number", log.Kv("input", params))
			return false
		}
		if levelValue < 1 {
			log.Error("GM HeroAddAll level must be >= 1", log.Kv("input", levelValue))
			return false
		}
		level = int32(levelValue)
	}

	// 创建英雄ID集合，用于快速检查是否已拥有英雄
	existingHeroes := make(map[int32]bool)
	for _, hero := range p.hero.db.HeroList {
		existingHeroes[hero.HeroId] = true
	}

	// 遍历TableHero表，添加所有英雄
	addedCount := 0
	table.GetTable().TableHero.Foreach(func(heroData *table_data.TableHero) bool {
		// 只添加未拥有的英雄
		if !existingHeroes[heroData.ID] {
			newHero := &public.PBHeroInfo{
				HeroId:    heroData.ID,
				HeroLevel: level,
				Exp:       0,
			}
			p.hero.db.HeroList = append(p.hero.db.HeroList, newHero)
			addedCount++
		}
		return true // 继续遍历
	})

	log.Info("GM HeroAddAll success",
		log.Kv("player_id", p.Uid()),
		log.Kv("added_heroes", addedCount),
		log.Kv("total_heroes", len(p.hero.db.HeroList)),
		log.Kv("level", level))
	return true
}

// handleHeroSetAllLvl 设置所有英雄等级
// 格式：level
func (p *Player) handleHeroSetAllLvl(params string) bool {
	// 检查参数
	if len(params) == 0 {
		log.Error("GM HeroSetAllLvl invalid params format: level")
		return false
	}

	// 解析等级参数
	level, err := strconv.ParseInt(params, 10, 32)
	if err != nil {
		log.Error("GM HeroSetAllLvl level must be number", log.Kv("input", params))
		return false
	}

	if level < 1 {
		log.Error("GM HeroSetAllLvl level must be >= 1", log.Kv("input", level))
		return false
	}

	// 设置所有英雄等级
	for _, hero := range p.hero.db.HeroList {
		hero.HeroLevel = int32(level)
	}

	log.Info("GM HeroSetAllLvl success",
		log.Kv("player_id", p.Uid()),
		log.Kv("hero_count", len(p.hero.db.HeroList)),
		log.Kv("level", level))
	return true
}

// handleHeroAddExp 给指定英雄增加经验值
// 格式：heroId expAmount
func (p *Player) handleHeroAddExp(params string) bool {
	// 检查参数
	parts := strings.Split(params, " ")
	if len(parts) != 2 {
		log.Error("GM HeroAddExp invalid params format: heroId expAmount")
		return false
	}

	// 解析英雄ID
	heroId, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GM HeroAddExp heroId must be number", log.Kv("input", parts[0]))
		return false
	}

	// 解析经验值
	expAmount, err := strconv.ParseInt(parts[1], 10, 32)
	if err != nil {
		log.Error("GM HeroAddExp expAmount must be number", log.Kv("input", parts[1]))
		return false
	}

	if expAmount < 0 {
		log.Error("GM HeroAddExp expAmount must be >= 0", log.Kv("input", expAmount))
		return false
	}

	// 查找英雄
	hero := p.hero.GetHeroById(int32(heroId))
	if hero == nil {
		log.Error("GM HeroAddExp hero not found", log.Kv("heroId", heroId))
		return false
	}

	// 增加英雄经验值
	result := p.hero.AddHeroExp(int32(heroId), int32(expAmount))
	if result != error_code.ERROR_OK {
		log.Error("GM HeroAddExp failed to add exp",
			log.Kv("heroId", heroId),
			log.Kv("expAmount", expAmount),
			log.Kv("error", result))
		return false
	}

	// 查询英雄当前状态(可能是新创建的)
	hero = p.hero.GetHeroById(int32(heroId))
	if hero == nil {
		log.Error("GM HeroAddExp hero not found after adding exp", log.Kv("heroId", heroId))
		return false
	}

	log.Info("GM HeroAddExp success",
		log.Kv("player_id", p.Uid()),
		log.Kv("heroId", heroId),
		log.Kv("expAmount", expAmount),
		log.Kv("current_exp", hero.Exp),
		log.Kv("current_level", hero.HeroLevel))
	return true
}

// handleHeroSetAwakeLevel 设置英雄觉醒等级
// 格式：heroId awakeLevel
func (p *Player) handleHeroSetAwakeLevel(params string) bool {
	parts := strings.Split(params, " ")
	if len(parts) != 2 {
		log.Error("GM HeroSetAwakeLevel invalid params format: heroId awakeLevel")
		return false
	}

	// 解析英雄ID
	heroId, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GM HeroSetAwakeLevel heroId must be number", log.Kv("input", parts[0]))
		return false
	}

	// 解析觉醒等级
	awakeLevel, err := strconv.ParseInt(parts[1], 10, 32)
	if err != nil {
		log.Error("GM HeroSetAwakeLevel awakeLevel must be number", log.Kv("input", parts[1]))
		return false
	}

	// 确保觉醒等级在有效范围内（0-5）
	if awakeLevel < 0 || awakeLevel > 5 {
		log.Error("GM HeroSetAwakeLevel awakeLevel must be 0-5", log.Kv("input", awakeLevel))
		return false
	}

	// 查找英雄
	hero := p.hero.GetHeroById(int32(heroId))
	if hero == nil {
		// 获取英雄配置信息
		heroData := table.GetTable().TableHero.GetById(int32(heroId))
		if heroData == nil {
			log.Error("GM HeroSetAwakeLevel hero config not found",
				log.Kv("player_id", p.Uid()),
				log.Kv("heroId", heroId))
			return false
		}

		// 英雄不存在，创建新英雄并设置觉醒等级
		newHero := &public.PBHeroInfo{
			HeroId:     int32(heroId),
			HeroLevel:  heroData.BaseLvl, // 使用配置表中的初始等级
			Exp:        0,
			AwakeLevel: int32(awakeLevel),
		}
		p.hero.db.HeroList = append(p.hero.db.HeroList, newHero)
		log.Info("GM HeroSetAwakeLevel created new hero with awake level",
			log.Kv("player_id", p.Uid()),
			log.Kv("heroId", heroId),
			log.Kv("base_level", heroData.BaseLvl),
			log.Kv("awakeLevel", awakeLevel))
	} else {
		// 英雄存在，设置觉醒等级
		hero.AwakeLevel = int32(awakeLevel)
		log.Info("GM HeroSetAwakeLevel set hero awake level",
			log.Kv("player_id", p.Uid()),
			log.Kv("heroId", heroId),
			log.Kv("awakeLevel", awakeLevel))
	}

	return true
}

// handleGMTreasure 处理宝物相关的 GM 命令
// 格式：<subType> <params>
// 1=删除宝物 format: 1 treasureId [count]
// 2=设置宝物等级 format: 2 treasureId level
// 3=设置宝物星级 format: 3 treasureId star
// 4=重置广告次数 format: 4
// 5=模拟抽取 format: 5 gachaId times
func (p *Player) handleGMTreasure(params string) bool {
	parts := strings.SplitN(params, " ", 2)
	if len(parts) < 1 {
		log.Error("GmTreasure invalid params, format: <subType> <params>")
		return false
	}

	// 解析子命令类型
	subType, err := strconv.Atoi(parts[0])
	if err != nil {
		log.Error("GmTreasure subType must be number",
			log.Kv("input", parts[0]))
		return false
	}

	// 获取剩余参数
	var cmdParams string
	if len(parts) > 1 {
		cmdParams = parts[1]
	}

	switch subType {
	case game_def.GmTreasureSubDelTreasure: // 删除宝物
		return p.handleTreasureDelete(cmdParams)
	case game_def.GmTreasureSubSetLevel: // 设置宝物等级
		return p.handleTreasureSetLevel(cmdParams)
	case game_def.GmTreasureSubSetStar: // 设置宝物星级
		return p.handleTreasureSetStar(cmdParams)
	case game_def.GmTreasureSubResetAdTimes: // 重置广告次数
		return p.handleTreasureResetAdTimes(cmdParams)
	case game_def.GmTreasureSubSimulateGacha: // 模拟抽取
		return p.handleTreasureSimulateGacha(cmdParams)
	default:
		log.Error("GmTreasure unknown subType", log.Kv("subType", subType))
		return false
	}
}

// handleTreasureDelete 删除宝物
// 格式：treasureId [count]（count可选，默认为1）
func (p *Player) handleTreasureDelete(params string) bool {
	parts := strings.Split(params, " ")
	if len(parts) < 1 || len(parts) > 2 {
		log.Error("GM TreasureDelete invalid params format: treasureId [count]")
		return false
	}

	// 解析宝物ID
	treasureId, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GM TreasureDelete treasureId must be number", log.Kv("input", parts[0]))
		return false
	}

	// 解析数量（可选参数）
	count := int32(1)
	if len(parts) >= 2 {
		countValue, err := strconv.ParseInt(parts[1], 10, 32)
		if err != nil {
			log.Error("GM TreasureDelete count must be number", log.Kv("input", parts[1]))
			return false
		}
		if countValue <= 0 {
			log.Error("GM TreasureDelete count must be > 0", log.Kv("input", countValue))
			return false
		}
		count = int32(countValue)
	}

	// 查找宝物
	treasure := p.treasure.findTreasure(int32(treasureId))
	if treasure == nil {
		log.Error("GM TreasureDelete treasure not found", log.Kv("treasureId", treasureId))
		return false
	}

	// 检查数量是否足够
	if treasure.Count < count {
		log.Error("GM TreasureDelete not enough treasure count",
			log.Kv("treasureId", treasureId),
			log.Kv("hasCount", treasure.Count),
			log.Kv("deleteCount", count))
		return false
	}

	// 删除宝物数量
	treasure.Count -= count

	log.Info("GM TreasureDelete success",
		log.Kv("player_id", p.Uid()),
		log.Kv("treasureId", treasureId),
		log.Kv("deletedCount", count),
		log.Kv("remainCount", treasure.Count))
	return true
}

// handleTreasureSetLevel 设置宝物等级
// 格式：treasureId level
func (p *Player) handleTreasureSetLevel(params string) bool {
	parts := strings.Split(params, " ")
	if len(parts) != 2 {
		log.Error("GM TreasureSetLevel invalid params format: treasureId level")
		return false
	}

	// 解析宝物ID
	treasureId, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GM TreasureSetLevel treasureId must be number", log.Kv("input", parts[0]))
		return false
	}

	// 解析等级值
	level, err := strconv.ParseInt(parts[1], 10, 32)
	if err != nil {
		log.Error("GM TreasureSetLevel level must be number", log.Kv("input", parts[1]))
		return false
	}

	// 确保等级不小于1
	if level < 1 {
		log.Error("GM TreasureSetLevel level must be >= 1", log.Kv("input", level))
		return false
	}

	// 查找宝物
	treasure := p.treasure.findTreasure(int32(treasureId))
	if treasure == nil {
		// 宝物不存在，激活宝物并设置等级
		treasure = p.treasure.activateTreasure(int32(treasureId))
		if treasure == nil {
			log.Error("GM TreasureSetLevel failed to activate treasure", log.Kv("treasureId", treasureId))
			return false
		}
	}

	// 设置等级
	treasure.Level = int32(level)

	log.Info("GM TreasureSetLevel success",
		log.Kv("player_id", p.Uid()),
		log.Kv("treasureId", treasureId),
		log.Kv("level", level))
	return true
}

// handleTreasureSetStar 设置宝物星级
// 格式：treasureId star
func (p *Player) handleTreasureSetStar(params string) bool {
	parts := strings.Split(params, " ")
	if len(parts) != 2 {
		log.Error("GM TreasureSetStar invalid params format: treasureId star")
		return false
	}

	// 解析宝物ID
	treasureId, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GM TreasureSetStar treasureId must be number", log.Kv("input", parts[0]))
		return false
	}

	// 解析星级值
	star, err := strconv.ParseInt(parts[1], 10, 32)
	if err != nil {
		log.Error("GM TreasureSetStar star must be number", log.Kv("input", parts[1]))
		return false
	}

	// 确保星级不小于1
	if star < 1 {
		log.Error("GM TreasureSetStar star must be >= 1", log.Kv("input", star))
		return false
	}

	// 查找宝物
	treasure := p.treasure.findTreasure(int32(treasureId))
	if treasure == nil {
		// 宝物不存在，激活宝物并设置星级
		treasure = p.treasure.activateTreasure(int32(treasureId))
		if treasure == nil {
			log.Error("GM TreasureSetStar failed to activate treasure", log.Kv("treasureId", treasureId))
			return false
		}
	}

	// 设置星级
	treasure.Star = int32(star)

	log.Info("GM TreasureSetStar success",
		log.Kv("player_id", p.Uid()),
		log.Kv("treasureId", treasureId),
		log.Kv("star", star))
	return true
}

// handleTreasureResetAdTimes 重置广告次数
// 格式：无参数
func (p *Player) handleTreasureResetAdTimes(params string) bool {
	// 重置广告次数
	p.treasure.db.DailyAdGachaCounts = 0
	p.treasure.db.LastAdGachaResetTime = 0

	log.Info("GM TreasureResetAdTimes success",
		log.Kv("player_id", p.Uid()))
	return true
}

// handleTreasureSimulateGacha 模拟抽取
// 格式：gachaId times
func (p *Player) handleTreasureSimulateGacha(params string) bool {
	parts := strings.Split(params, " ")
	if len(parts) != 2 {
		log.Error("GM TreasureSimulateGacha invalid params format: gachaId times")
		return false
	}

	// 解析卡池ID
	gachaId, err := strconv.ParseInt(parts[0], 10, 32)
	if err != nil {
		log.Error("GM TreasureSimulateGacha gachaId must be number", log.Kv("input", parts[0]))
		return false
	}

	// 解析抽取次数
	times, err := strconv.ParseInt(parts[1], 10, 32)
	if err != nil {
		log.Error("GM TreasureSimulateGacha times must be number", log.Kv("input", parts[1]))
		return false
	}

	if times <= 0 {
		log.Error("GM TreasureSimulateGacha times must be > 0", log.Kv("input", times))
		return false
	}

	// 统计结果
	resultMap := make(map[int32]int32) // itemId -> count

	// 执行模拟抽取
	for i := int32(0); i < int32(times); i++ {
		// 判断是否触发保底
		currentPityCount := p.treasure.db.GachaPityCounts[int32(gachaId)]
		gachaConfig := table.GetTable().TableTreasureGacha.GetById(int32(gachaId))
		if gachaConfig == nil {
			log.Error("GM TreasureSimulateGacha gacha config not found", log.Kv("gachaId", gachaId))
			return false
		}

		isSpecialBag := (currentPityCount + 1) >= gachaConfig.Count

		// 选择卡包
		var selectedBagId int32
		if isSpecialBag && gachaConfig.BagSpecial != nil {
			selectedBagId = p.treasure.selectBagByWeight(gachaConfig.BagSpecial)
			p.treasure.db.GachaPityCounts[int32(gachaId)] = 0
		} else {
			selectedBagId = p.treasure.selectBagByWeight(gachaConfig.Bag)
			p.treasure.db.GachaPityCounts[int32(gachaId)] = currentPityCount + 1
		}

		// 从卡包中抽取道具
		itemId, itemCount := p.treasure.selectItemFromBag(selectedBagId)
		if itemId > 0 && itemCount > 0 {
			resultMap[itemId] += itemCount
		}
	}

	// 输出统计结果
	log.Info("GM TreasureSimulateGacha results",
		log.Kv("player_id", p.Uid()),
		log.Kv("gachaId", gachaId),
		log.Kv("times", times),
		log.Kv("results", resultMap),
		log.Kv("finalPityCount", p.treasure.db.GachaPityCounts[int32(gachaId)]))

	return true
}
