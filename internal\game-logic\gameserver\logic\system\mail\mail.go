package mail

import (
	"context"
	"fmt"
	"liteframe/pkg/util"
	"slices"

	"liteframe/internal/common/constant/def"
	"liteframe/internal/common/protos/dbstruct"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/game-logic/gameserver/game_def/actor_def"
	"liteframe/internal/game-logic/gameserver/gameutil/metrics"
	"liteframe/internal/game-logic/gameserver/global"
	"liteframe/internal/game-logic/gameserver/logic/system/dao/dbhelper"
	"liteframe/pkg/actor"
	"liteframe/pkg/log"
)

type MailMgr struct {
	Pid              actor.PID
	rpcDispatch      *actor.Dispatcher
	globalMailDb     *dbstruct.MailList
	userOfflineDb    map[uint64]*dbstruct.MailList
	dirtyUserOffline map[uint64]struct{}
}

func NewMailMgr() *MailMgr {
	m := &MailMgr{
		Pid: actor_def.MailMgrSystemPID,
		rpcDispatch: actor.NewDispatcher(20, func(uid uint64, id uint32, ms int64) {
			metrics.SetRpcCost(fmt.Sprintf("mail_%d", id), ms)
			if ms > 20 {
				log.Debug("mailWorld", log.Kv("id", id), log.Kv("cost", ms))
			}
		}),
		globalMailDb:     &dbstruct.MailList{},
		userOfflineDb:    make(map[uint64]*dbstruct.MailList),
		dirtyUserOffline: make(map[uint64]struct{}),
	}
	m.register()
	return m
}

func (m *MailMgr) register() {
	m.rpcDispatch.Register(def.MsgId_Actor_Mail_AddGlobalMail, m.addGlobalMailHandle)
	m.rpcDispatch.Register(def.MsgId_Actor_Mail_Global_AddOffLineMail, m.addOfflineMailHandle)
	//m.rpcDispatch.Register(def.MsgId_Actor_Mail_GetUserOfflineMail, m.getOfflineMailHandle)
}

func (m *MailMgr) InitDb() {
	// 载入全局邮件数据库
	data, err := dbhelper.LoadGlobalMailDataSync(m.Pid)
	if err != nil {
		log.Error(err.Error())
		panic(err)
	}
	m.globalMailDb = data
	if err != nil {
		log.Error(err.Error())
		panic(err)
	}
	//// 载入用户离线邮件数据库
	//data2, err := dbhelper.SyncLoadAllHashData(m.Pid, dao.GetMailOfflineKey())
	//if err != nil {
	//	log.Error(err.Error())
	//	panic(err)
	//}
	//for idStr, v := range data2 {
	//	rid, errx := util.String2Int(idStr)
	//	if errx != nil {
	//		log.Error("load offline mail string2int err ", log.Kv("id", idStr), log.Kv("err", errx))
	//		continue
	//	}
	//	userDb := &dbstruct.MailList{}
	//	errx = proto.Unmarshal([]byte(v), userDb)
	//	if errx != nil {
	//		log.Error("load offline mail Unmarshal err ", log.Kv("id", idStr), log.Kv("err", errx))
	//		continue
	//	}
	//	m.userOfflineDb[uint64(rid)] = userDb
	//}
}

func (m *MailMgr) Tick() {
	m.Save()
}

func (m *MailMgr) Save() {
	if len(m.dirtyUserOffline) == 0 {
		return
	}
	//saveData := map[string][]byte{}
	//delKeys := make([]string, 0)
	//for id, _ := range m.dirtyUserOffline {
	//	v, ok := m.userOfflineDb[id]
	//	if !ok {
	//		delKeys = append(delKeys, util.Int642String(int64(id)))
	//		continue
	//	}
	//	data, err := proto.Marshal(v)
	//	if err != nil {
	//		log.Error("save offline mail Unmarshal err ", log.Kv("info", v), log.Kv("err", err))
	//		continue
	//	}
	//	saveData[util.Int642String(int64(id))] = data
	//}
	//if len(saveData) > 0 {
	//	dbhelper.SaveHashData(m.Pid, dao.GetMailOfflineKey(), saveData, func(err error) {
	//		if err != nil {
	//			log.Error("save offline mail dirty data err", log.Err(err))
	//		}
	//	})
	//}
	//if len(delKeys) > 0 {
	//	dbhelper.DeleteHashData(m.Pid, dao.GetMailOfflineKey(), delKeys, func(err error) {
	//		if err != nil {
	//			log.Error("delete offline mail dirty data err", log.Err(err))
	//		}
	//	})
	//}
	//m.dirtyUserOffline = make(map[uint64]struct{})
}

func (m *MailMgr) PID() actor.PID {
	return m.Pid
}
func (m *MailMgr) Process(msg *actor.Message) {
	if msg.Id == def.MsgId_Actor_Second {
		m.Tick()
		return
	}
	err := m.rpcDispatch.Dispatch(context.TODO(), msg)
	if err != nil {
		log.Fatal("[mailMgr] dispatch actor msg err", log.Kv("msg", msg))
	}
}

func (m *MailMgr) OnStop() {

}

func (m *MailMgr) addGlobalMailHandle(ctx context.Context, msg *actor.Message) error {
	mail := msg.Data.(*public.PBMail)
	m.addGlobalMail(mail)
	return nil
}

func (m *MailMgr) addOfflineMailHandle(ctx context.Context, msg *actor.Message) error {
	mailMap := msg.Data.(map[uint64]*public.PBMail)
	for id, v := range mailMap {
		m.addOfflineMail(id, v)
	}
	return nil
}

//func (m *MailMgr) getOfflineMailHandle(ctx context.Context, msg *actor.Message) error {
//	req := msg.Data.(*game_def.GetOfflineMailReq)
//	list := m.getUserOffLineMail(req.Rid, req.LastId)
//	msg.Response(actor.RespMessage{
//		Err:  nil,
//		Data: list,
//	})
//	return nil
//}

func (m *MailMgr) addOfflineMail(id uint64, mail *public.PBMail) {
	data, ok := m.userOfflineDb[id]
	if !ok {
		data = &dbstruct.MailList{List: make([]*public.PBMail, 0)}
	}
	data.List = append(data.List, mail)
	m.userOfflineDb[id] = data
	if m.dirtyUserOffline == nil {
		m.dirtyUserOffline = make(map[uint64]struct{})
	}
	m.dirtyUserOffline[id] = struct{}{}
}

func (m *MailMgr) addGlobalMail(mail *public.PBMail) {
	if m.globalMailDb == nil {
		m.globalMailDb = &dbstruct.MailList{List: make([]*public.PBMail, 0)}
	}
	m.globalMailDb.List = append(m.globalMailDb.List, mail)
	m.SaveGlobalMail()
}

// 保存全局邮件 全局邮件变化立即回存
func (m *MailMgr) SaveGlobalMail() {
	if len(m.globalMailDb.List) != 0 {
		m.globalMailDb.List = slices.DeleteFunc(m.globalMailDb.List, func(v *public.PBMail) bool { return v.ExpireTime < global.Now().Unix() })
	}

	dbhelper.SaveGlobalMail(m.Pid, m.globalMailDb, func(err error) {
		if err != nil {
			log.Error("save global mail error", log.Err(err))
		}
	})
}

func (m *MailMgr) getUserOffLineMail(rid uint64, lastId uint64) []*public.PBMail {
	mailList := make([]*public.PBMail, 0)
	for _, v := range m.globalMailDb.List {
		if v.Id > int64(lastId) {
			mail := &public.PBMail{}
			util.DeepCopy(&mail, v)
			mailList = append(mailList, mail)
		}
	}

	if u, ok := m.userOfflineDb[rid]; ok {
		mailList = append(mailList, u.List...)
		delete(m.userOfflineDb, rid)
		m.dirtyUserOffline[rid] = struct{}{}
	}
	return mailList
}

//// GlobalMailSystem 全局邮件系统
//type GlobalMailSystem struct {
//	dispatch *actor.Dispatcher
//	mailDB   *dbstruct.GameData // 存储全局邮件数据
//
//	// 全局邮件列表，按照发送时间排序
//	globalMails []*dbstruct.GlobalActivityMailData
//
//	// 定时器，用于每天5点触发邮件发送
//	timerMgr *timer.TimerExManager
//
//	// 邮件ID生成器
//	nextMailId int64
//}
//
//// NewGlobalMailSystem 创建全局邮件系统
//func NewGlobalMailSystem() *GlobalMailSystem {
//	gms := &GlobalMailSystem{
//		dispatch: actor.NewDispatcher(100, func(uid uint64, id uint32, ms int64) {
//			metrics.SetRpcCost(fmt.Sprintf("globalmail_%d", id), ms)
//			if ms > 100 {
//				log.Debug("global mail system", log.Kv("id", id), log.Kv("cost", ms))
//			}
//		}),
//		mailDB: &dbstruct.GameData{
//			GlobalMails: make([]*dbstruct.GlobalActivityMailData, 0),
//		},
//		globalMails: make([]*dbstruct.GlobalActivityMailData, 0),
//		timerMgr:    timer.NewTimerExManager(timer.Size(100)),
//		nextMailId:  time.Now().UnixNano(),
//	}
//	gms.registerHandler()
//	return gms
//}
//
//// LoadGlobalMailSync 从DB同步加载全局邮件数据
//func (gms *GlobalMailSystem) LoadGlobalMailSync() error {
//	// 使用serverId作为key加载数据
//	data, err := dbhelper.LoadDataSync(gms.PID(), strconv.FormatUint(uint64(global.ServerId), 10))
//	if err != nil {
//		log.Error("load global mails failed", log.Err(err))
//		return err
//	}
//
//	if data != nil && len(data) > 0 {
//		if err := proto.Unmarshal(data, gms.mailDB); err != nil {
//			log.Error("unmarshal global mail data failed", log.Err(err))
//			return err
//		}
//
//		// 将数据转移到内存中
//		gms.globalMails = gms.mailDB.GlobalMails
//	}
//
//	// 设置定时器，每天5点触发邮件发送
//	gms.timerMgr.AddTimerFunc("0 5 * * *", gms.dailyMailCheck)
//
//	log.Info("load global mails success", log.Kv("count", len(gms.globalMails)))
//	return nil
//}
//
//func (gms *GlobalMailSystem) registerHandler() {
//	// 注册系统消息处理
//	gms.dispatch.Register(uint32(def.MsgId_Actor_Second), gms.secondTickHandler)
//	gms.dispatch.Register(uint32(def.MsgId_Actor_CrossDay), gms.crossDayHandler)
//
//	// 注册玩家系统消息
//	//gms.dispatch.Register(uint32(def.MsgId_Actor_PlayerSystem2GlobalMail), gms.playerSystemMessageHandler)
//
//	// 注册GM消息
//	gms.dispatch.Register(uint32(def.MsgId_Actor_GM), gms.gmCommandHandler)
//}
//
//// PID 返回Actor的PID
//func (gms *GlobalMailSystem) PID() actor.PID {
//	return actor_def.GlobalMailPID
//}
//
//// Process 处理消息
//func (gms *GlobalMailSystem) Process(msg *actor.Message) {
//	if err := gms.dispatch.Dispatch(context.Background(), msg); err != nil {
//		log.Error("global mail system dispatch failed", log.Err(err), log.Kv("msgId", msg.Id))
//	}
//}
//
//// OnStop 停止时的处理
//func (gms *GlobalMailSystem) OnStop() {
//	log.Info("global mail system stop")
//	// 保存数据
//	gms.saveGlobalMails()
//}
//
//// secondTickHandler 处理每秒Tick
//func (gms *GlobalMailSystem) secondTickHandler(ctx context.Context, msg *actor.Message) error {
//	// 运行定时器
//	gms.timerMgr.Run(global.Now(), 100)
//	return nil
//}
//
//// crossDayHandler 处理跨天消息
//func (gms *GlobalMailSystem) crossDayHandler(ctx context.Context, msg *actor.Message) error {
//	crossData, ok := msg.Data.(actor_def.CrossData)
//	if !ok {
//		return errors.New("invalid cross day data")
//	}
//
//	// 检查是否是自然跨天
//	if crossData.Natural {
//		// 清理过期邮件
//		gms.cleanExpiredMails()
//	}
//
//	return nil
//}
//
//// playerSystemMessageHandler 处理来自玩家系统的消息
//func (gms *GlobalMailSystem) playerSystemMessageHandler(ctx context.Context, msg *actor.Message) error {
//	// 处理玩家系统发来的消息，如玩家上线通知等
//	// 这里可以根据实际需求扩展
//	return nil
//}
//
//// gmCommandHandler 处理GM命令
//func (gms *GlobalMailSystem) gmCommandHandler(ctx context.Context, msg *actor.Message) error {
//	if msg.Data == nil {
//		return errors.New("data nil")
//	}
//
//	// 这里简化处理，实际可能需要更复杂的逻辑
//	// 假设GM命令是一个字符串，格式为"cmd:param1:param2:..."
//	cmdStr, ok := msg.Data.(string)
//	if !ok {
//		return fmt.Errorf("invalid GM command format: %T", msg.Data)
//	}
//
//	parts := strings.Split(cmdStr, ":")
//	if len(parts) < 1 {
//		return errors.New("invalid GM command format")
//	}
//
//	cmd := parts[0]
//	switch cmd {
//	case "add_global_mail":
//		// 格式: add_global_mail:title:content:expireTime
//		if len(parts) < 4 {
//			return errors.New("invalid add_global_mail command format")
//		}
//		title := parts[1]
//		content := parts[2]
//		expireTime, err := strconv.ParseInt(parts[3], 10, 64)
//		if err != nil {
//			return fmt.Errorf("invalid expire time: %s", parts[3])
//		}
//
//		// 创建邮件数据
//		mailData := gms.CreateMailData(
//			public.MailType_MailType_Reward,
//			title,
//			content,
//			expireTime,
//			"GM",
//			make(map[int32]int32), // 空物品列表
//			public.MailReasonType_MailReasonType_GMRemote,
//		)
//
//		// 添加全局邮件
//		mailId := gms.AddGlobalMail(mailData)
//		log.Info("added global mail via GM command", log.Kv("mailId", mailId))
//
//	case "send_mail_now":
//		// 格式: send_mail_now:mailId
//		if len(parts) < 2 {
//			return errors.New("invalid send_mail_now command format")
//		}
//		mailId, err := strconv.ParseInt(parts[1], 10, 64)
//		if err != nil {
//			return fmt.Errorf("invalid mail id: %s", parts[1])
//		}
//
//		// 立即发送指定邮件
//		success := gms.SendMailNow(mailId)
//		log.Info("send mail now via GM command", log.Kv("mailId", mailId), log.Kv("success", success))
//
//	case "send_all_mails_now":
//		// 立即发送所有符合条件的邮件
//		count := gms.SendAllMailsNow()
//		log.Info("send all mails now via GM command", log.Kv("count", count))
//
//	default:
//		return fmt.Errorf("unknown GM command: %s", cmd)
//	}
//
//	return nil
//}
//
//// dailyMailCheck 每天5点检查并发送邮件
//func (gms *GlobalMailSystem) dailyMailCheck(nowUnix int64) {
//	log.Info("daily mail check triggered", log.Kv("time", nowUnix))
//
//	// 获取当前时间
//	now := time.Unix(nowUnix, 0)
//
//	// 检查每个邮件是否需要发送
//	mailsToSend := make([]*dbstruct.GlobalActivityMailData, 0)
//	for _, mail := range gms.globalMails {
//		// 检查邮件发送条件
//		if gms.shouldSendMail(mail, now) {
//			mailsToSend = append(mailsToSend, mail)
//		}
//	}
//
//	// 发送邮件
//	if len(mailsToSend) > 0 {
//		gms.sendMailsToAllPlayers(mailsToSend)
//	}
//}
//
//// shouldSendMail 检查邮件是否应该发送
//func (gms *GlobalMailSystem) shouldSendMail(mail *dbstruct.GlobalActivityMailData, now time.Time) bool {
//	// 检查邮件发送条件，如时间、目标玩家等
//	// 这里简化处理，实际可能需要更复杂的逻辑
//	return mail.MailData.CreateTime <= now.Unix() &&
//		mail.MailData.Status == int32(public.MailStateType_MailStateType_UnRead)
//}
//
//// sendMailsToAllPlayers 向所有玩家发送邮件
//func (gms *GlobalMailSystem) sendMailsToAllPlayers(mails []*dbstruct.GlobalActivityMailData) {
//	// 构建发送请求
//	req := &cs.GM2PS_SendGlobalMail{
//		Mails: make([]*dbstruct.MailData, 0, len(mails)),
//	}
//
//	for _, mail := range mails {
//		req.Mails = append(req.Mails, mail.MailData)
//		// 更新邮件状态
//		mail.MailData.Status = int32(public.MailStateType_MailStateType_Read)
//	}
//
//	// 发送给玩家系统
//	err := actor.Send(gms.PID(), actor_def.PlayerSystemPID, &actor.Message{
//		Id:   uint32(def.MsgId_Actor_Mail_AddGlobalMail),
//		Data: req,
//	})
//
//	if err != nil {
//		log.Error("send global mails to player system failed", log.Err(err))
//		return
//	}
//
//	log.Info("sent global mails to all players", log.Kv("count", len(mails)))
//
//	// 保存邮件状态
//	gms.saveGlobalMails()
//}
//
//// AddGlobalMail 添加全局邮件
//func (gms *GlobalMailSystem) AddGlobalMail(mailData *dbstruct.MailData) int64 {
//	// 生成邮件ID
//	mailId := gms.generateMailId()
//	mailData.Id = mailId
//
//	// 创建全局邮件
//	globalMail := &dbstruct.GlobalActivityMailData{
//		Id:       mailId,
//		PlayerId: 0, // 0表示发给所有玩家
//		MailData: mailData,
//	}
//
//	// 添加到列表
//	gms.globalMails = append(gms.globalMails, globalMail)
//	gms.mailDB.GlobalMails = gms.globalMails
//
//	// 保存到数据库
//	gms.saveGlobalMails()
//
//	log.Info("added global mail", log.Kv("mailId", mailId))
//
//	return mailId
//}
//
//// RemoveGlobalMail 删除全局邮件
//func (gms *GlobalMailSystem) RemoveGlobalMail(mailId int64) bool {
//	for i, mail := range gms.globalMails {
//		if mail.Id == mailId {
//			// 删除邮件
//			gms.globalMails = append(gms.globalMails[:i], gms.globalMails[i+1:]...)
//			gms.mailDB.GlobalMails = gms.globalMails
//
//			// 保存到数据库
//			gms.saveGlobalMails()
//
//			log.Info("removed global mail", log.Kv("mailId", mailId))
//			return true
//		}
//	}
//
//	log.Warn("global mail not found", log.Kv("mailId", mailId))
//	return false
//}
//
//// SendMailNow 立即发送指定邮件
//func (gms *GlobalMailSystem) SendMailNow(mailId int64) bool {
//	for _, mail := range gms.globalMails {
//		if mail.Id == mailId {
//			// 发送邮件
//			gms.sendMailsToAllPlayers([]*dbstruct.GlobalActivityMailData{mail})
//			return true
//		}
//	}
//
//	log.Warn("global mail not found for immediate sending", log.Kv("mailId", mailId))
//	return false
//}
//
//// SendAllMailsNow 立即发送所有符合条件的邮件
//func (gms *GlobalMailSystem) SendAllMailsNow() int {
//	now := global.Now()
//	mailsToSend := make([]*dbstruct.GlobalActivityMailData, 0)
//
//	for _, mail := range gms.globalMails {
//		if gms.shouldSendMail(mail, now) {
//			mailsToSend = append(mailsToSend, mail)
//		}
//	}
//
//	if len(mailsToSend) > 0 {
//		gms.sendMailsToAllPlayers(mailsToSend)
//	}
//
//	return len(mailsToSend)
//}
//
//// generateMailId 生成邮件ID
//func (gms *GlobalMailSystem) generateMailId() int64 {
//	gms.nextMailId++
//	return gms.nextMailId
//}
//
//// cleanExpiredMails 清理过期邮件
//func (gms *GlobalMailSystem) cleanExpiredMails() {
//	now := global.Now().Unix()
//	newMails := make([]*dbstruct.GlobalActivityMailData, 0, len(gms.globalMails))
//
//	for _, mail := range gms.globalMails {
//		// 检查邮件是否过期
//		if mail.MailData.CreateTime+mail.MailData.ExpireTime > now {
//			newMails = append(newMails, mail)
//		}
//	}
//
//	// 如果有邮件被清理，更新列表并保存
//	if len(newMails) != len(gms.globalMails) {
//		gms.globalMails = newMails
//		gms.mailDB.GlobalMails = newMails
//		gms.saveGlobalMails()
//
//		log.Info("cleaned expired mails",
//			log.Kv("before", len(gms.globalMails)),
//			log.Kv("after", len(newMails)))
//	}
//}
//
//// saveGlobalMails 保存全局邮件数据
//func (gms *GlobalMailSystem) saveGlobalMails() {
//	// 更新数据库对象
//	gms.mailDB.GlobalMails = gms.globalMails
//
//	// 异步保存到数据库
//	dbhelper.SaveData(gms.PID(), strconv.FormatUint(uint64(global.ServerId), 10), gms.mailDB, func(err error) {
//		if err != nil {
//			log.Error("save global mails failed", log.Err(err))
//		} else {
//			log.Info("save global mails success", log.Kv("count", len(gms.globalMails)))
//		}
//	})
//}
//
//// CreateMailData 创建邮件数据
//func (gms *GlobalMailSystem) CreateMailData(
//	mailType public.MailType,
//	title string,
//	content string,
//	expireTime int64,
//	senderName string,
//	items map[int32]int32,
//	reason public.MailReasonType) *dbstruct.MailData {
//
//	mailData := &dbstruct.MailData{
//		Type:       int32(mailType),
//		TitleStr:   title,
//		ContentStr: content,
//		Status:     int32(public.MailStateType_MailStateType_UnRead),
//		CreateTime: global.Now().Unix(),
//		ExpireTime: expireTime,
//		SenderName: senderName,
//		Reason:     int32(reason),
//		GoodsItems: make([]*dbstruct.CommonKeyValue, 0, len(items)),
//	}
//
//	// 添加物品
//	for itemId, count := range items {
//		mailData.GoodsItems = append(mailData.GoodsItems, &dbstruct.CommonKeyValue{
//			Key:   itemId,
//			Value: count,
//		})
//	}
//
//	return mailData
//}
