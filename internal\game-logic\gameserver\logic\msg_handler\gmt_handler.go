package msg_handler

import (
	"encoding/json"
	"fmt"
	"io"
	"liteframe/internal/common/constant/def"
	"liteframe/internal/game-logic/gameserver/game_def"
	"liteframe/internal/game-logic/gameserver/game_def/actor_def"
	"liteframe/pkg/actor"
	"net/http"
	"strconv"
)

// 定义一个处理函数类型
type ActionHandler func(req game_def.GMTRequest) (interface{}, error)

// 处理函数映射表
var handlers = map[int32]ActionHandler{
	game_def.GmtGetPlayerInfo:  handleSendToPlayer, // 查询玩家信息
	game_def.GmtSendPlayerMail: handleSendMail,     // 发送个人邮件
	game_def.GmtSendGlobalMail: handleSendMail,     // 发送全局邮件
	game_def.GmtSendMarquee:    handleSendMarquee,  // 发送跑马灯
	game_def.GmtDelPlayerItem:  handleSendToPlayer, // 删除玩家道具
}

// HandleGMTQuery 处理 GMT 的 HTTP 请求
func HandleGMTQuery(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Only POST method is allowed", http.StatusMethodNotAllowed)
		return
	}
	// 使用 io.ReadAll 读取请求体
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}
	print(string(body))
	defer r.Body.Close()

	// 解析 JSON
	var req game_def.GMTRequest
	if err := json.Unmarshal(body, &req); err != nil {
		http.Error(w, "Invalid JSON format", http.StatusBadRequest)
		return
	}

	// 查找对应的处理函数
	if handler, exists := handlers[req.Action]; exists {
		response, err := handler(req)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		// 返回 JSON 响应
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	} else {
		response := map[string]string{
			"error": "Unsupported action",
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}
}

func handleSendToPlayer(req game_def.GMTRequest) (interface{}, error) {
	roleID, err := strconv.Atoi(req.Req["roleID"].(string))
	if err != nil {
		fmt.Println("handleGetUserInfo: roleID not found or incorrect type")
		return nil, fmt.Errorf("roleID not found or incorrect type")
	}
	fmt.Printf("Handling GetUserInfo, roleID: %d\n", roleID)
	msg := &actor.Message{
		Id:   uint32(def.MsgId_Actor_GMT),
		Uid:  uint64(roleID),
		Data: &req,
	}

	resp := actor.SyncRequest(actor_def.SystemPID, actor_def.PlayerSystemPID, msg)
	if resp.Err != nil {
		response := map[string]string{
			"error": resp.Err.Error(),
		}
		return response, nil
	}
	// 模拟查询角色信息
	/*response := map[string]interface{}{
		"roleInfo": map[string]interface{}{
			"account":       "test_account",
			"roleName":      "TestRole",
			"roleID":        roleID,
			"level":         35,
			"createTime":    **********,
			"lastLoginTime": **********,
			"guildID":       12345,
			"chongzhi":      500,
		},
		"itemList": []map[string]interface{}{
			{"itemId": 101, "itemName": "Healing Potion", "itemCount": 10},
			{"itemId": 102, "itemName": "Mana Potion", "itemCount": 5},
		},
		"moneyList": []map[string]interface{}{
			{"moneyId": 1, "moneyName": "Gold", "moneyCount": 5000},
			{"moneyId": 2, "moneyName": "Gems", "moneyCount": 50},
		},
		"mailList": []map[string]interface{}{
			{
				"mailTitle":   "Welcome!",
				"mailContent": "Welcome to the game!",
				"sendTime":    **********,
				"mailItem":    "Starter Pack",
			},
		},
	}*/
	return resp.Data, nil
}

func handleSendMail(req game_def.GMTRequest) (interface{}, error) {
	msg := &actor.Message{
		Id:   uint32(def.MsgId_Actor_GMT),
		Data: req,
	}

	resp := actor.SyncRequest(actor_def.SystemPID, actor_def.MailMgrSystemPID, msg)
	if resp.Err != nil {
		response := map[string]string{
			"error": resp.Err.Error(),
		}
		return response, nil
	}
	return resp.Data, nil
}

func handleSendMarquee(req game_def.GMTRequest) (interface{}, error) {
	msg := &actor.Message{
		Id:   uint32(def.MsgId_Actor_GMT),
		Data: req,
	}
	//GetInstance().SendSystemMsgToAll(params)

	resp := actor.SyncRequest(actor_def.SystemPID, actor_def.MailMgrSystemPID, msg)
	if resp.Err != nil {
		response := map[string]string{
			"error": resp.Err.Error(),
		}
		return response, nil
	}
	return resp.Data, nil
}
