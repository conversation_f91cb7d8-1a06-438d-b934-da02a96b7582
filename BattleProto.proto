syntax = "proto3";
package battle;
option go_package = "liteframe/internal/common/protos/cs";
option csharp_namespace = "XGamePlay.Utility.Proto";

//战斗数据
message PbBattle {
    int64 uid = 1; //战斗唯一ID
    int32 type = 2; //战斗类型,可改统一枚举
    int32 seed = 3; //随机种子
    int32 frame = 4; //当前逻辑帧
    int32 accUid = 5; //实体递增唯一ID
    string map = 6; //地图
    int32 width = 7; //地图宽
    int32 height = 8; //地图高
    float gridSize = 9; //格子尺寸
    int32 teamId = 10; //0下方 1 上
    repeated PbEntity entities = 11; //实体数组
}

//实体
message PbEntity {
    int32 uid = 1; //实体唯一ID
    bool dirty = 2; //状态
    PbEntityBase baseInfo = 3; //基础信息
    PbTransform transform = 4; //位置信息
    PbAttribute attribute = 5; //属性信息
    PbState state = 6; //状态信息
    PbBehavior behavior = 7; //行为信息
    PbWayPoint way = 8; //寻路点信息
}

//基础信息
message PbEntityBase {
    int32 teamId = 1; //队伍ID
    int32 heroId = 2; //英雄ID
    int32 level = 3; //等级
    int32 star = 4; //星级
    int32 evo = 5; //觉醒等级
}

//位置信息
message PbTransform {
    float x = 1;
    float y = 2;
    // int32 dir = 3; //方向
    // int32 gx = 4; //格子X
    // int32 gy = 5; //格子Y
    // int32 grid = 6; //格子索引
    // int32 camp = 7; //1先手方 2后手
}

//属性信息
message PbAttribute {
    repeated int32 basic = 1; //一级属性
    repeated int32 value = 2; //二级属性
}

//状态信息
message PbState {
    int32 status = 1; //基础状态信息 idle,move,attak,die
    int32 action = 2; //基础行动状态
    int32 logic = 3; //逻辑状态
    int32 effect = 4; //控制效果状态
}

//技能
message PbSkill {
    int32 id = 1; //技能ID
    int64 elapsed = 2; //起始时间
}

//Buff
message PbBuff {
    int32 id = 1; //ID
    int32 uid = 2; //UID
    int32 origin = 3; //来源Entity
    int32 skill = 4; //来源技能
    int64 elapsed = 5; //起始时间
    int64 pulsed = 6; //脉冲时间
    int32 stack = 7; //层数
    map<int32,int32> attrs = 8; //二级属性
}

//行为
message PbBehavior {
    int32 target = 1; //当前的索敌目标
    repeated PbSkill skills = 2; //技能数组
    repeated PbBuff buffs = 3; //Buff数组
}

//寻路点
message PbWayPoint {
    repeated int32 grids = 1; //路径中格子的索引
    int32 currentGrid = 2; //当前格子索引
}
