package player

import (
	"liteframe/internal/common/error_code"
	"liteframe/internal/common/protos/dbstruct"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/common/table"
	"liteframe/internal/game-logic/gameserver/game_def"
	"liteframe/pkg/log"
)

// Hero 英雄系统
type Hero struct {
	player *Player
	db     *dbstruct.Hero

	totalLevel     int32 // 英雄总等级
	resonanceLevel int32 // 共鸣等级（每10级对应1阶）
}

func NewHero(p *Player) *Hero {
	h := &Hero{
		player: p,
		db: &dbstruct.Hero{
			HeroList: make([]*public.PBHeroInfo, 0),
		},
	}

	return h
}

func (t *Hero) InitDB(db *dbstruct.UserDB) {
	if db.Game == nil {
		db.Game = &dbstruct.GameDB{}
	}
	if db.Game.Hero == nil {
		db.Game.Hero = &dbstruct.Hero{
			HeroList: make([]*public.PBHeroInfo, 0),
		}
	}
	t.db = db.Game.Hero

	// 如果英雄列表为空，添加配置中的初始英雄
	if len(t.db.HeroList) == 0 {
		t.addInitialHeroes()
	}

	t.calculateTotalLevelAndResonance()

	log.Info("Hero InitDB")
}

func (t *Hero) OnCrossDay(natural bool, nowUnix int64) {}

// addInitialHeroes 添加配置中的初始发放英雄
func (t *Hero) addInitialHeroes() {
	// 从配置中获取初始发放英雄ID列表
	initialHeroIds := table.GetHeroDefaultObtainId()
	initialHeroNum := table.GetHeroDefaultObtainNum()

	if len(initialHeroIds) == 0 {
		log.Info("No initial heroes configured", log.Kv("player_id", t.player.Uid()))
		return
	}

	// 为每个初始英雄ID创建英雄
	for i, heroId := range initialHeroIds {
		// 检查英雄ID是否在hero表中存在
		heroData := table.GetTable().TableHero.GetById(heroId)
		if heroData == nil {
			log.Error("Invalid hero ID in initial hero config",
				log.Kv("player_id", t.player.Uid()),
				log.Kv("hero_id", heroId))
			continue
		}

		// 计算给予的数量和经验
		var giveNum int32
		var heroExp int32

		if initialHeroNum > 0 {
			// 如果配置了数量，每个英雄都给这个数量
			giveNum = initialHeroNum
			if i == 0 {
				// 第一个是本体，经验为0
				heroExp = 0
			} else {
				// 后续都是碎片，转换为经验
				heroExp = initialHeroNum
			}
		} else {
			// 如果没有配置数量，第一个是本体，后续都是1个碎片
			if i == 0 {
				// 第一个是本体
				giveNum = 1
				heroExp = 0
			} else {
				// 后续都是1个碎片
				giveNum = 1
				heroExp = 1
			}
		}

		// 创建英雄
		newHero := &public.PBHeroInfo{
			HeroId:    heroId,
			HeroLevel: heroData.BaseLvl, // 使用配置表中的初始等级
			Exp:       heroExp,          // 设置初始经验
		}
		t.db.HeroList = append(t.db.HeroList, newHero)

		log.Info("Initial hero added",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("hero_id", heroId),
			log.Kv("base_level", heroData.BaseLvl),
			log.Kv("give_num", giveNum),
			log.Kv("hero_exp", heroExp),
			log.Kv("is_first", i == 0))
	}

	log.Info("Initial heroes added",
		log.Kv("player_id", t.player.Uid()),
		log.Kv("hero_count", len(initialHeroIds)),
		log.Kv("hero_ids", initialHeroIds),
		log.Kv("configured_num", initialHeroNum))
}

// HeroList 获取英雄列表并返回给客户端
func (t *Hero) HeroList() ([]*public.PBHeroInfo, error_code.Code) {
	log.Info("Get hero list", log.Kv("player_id", t.player.Uid()))

	// 返回英雄列表和成功码，不需要重新计算共鸣等级
	return t.db.HeroList, error_code.ERROR_OK
}

// calculateTotalLevelAndResonance 计算英雄总等级和共鸣等级
// 共鸣系统实现：所有英雄等级总和，每10级为一个共鸣阶级
func (t *Hero) calculateTotalLevelAndResonance() {
	totalLevel := int32(0)

	// 累加所有英雄等级
	for _, hero := range t.db.HeroList {
		totalLevel += hero.HeroLevel
	}

	// 更新总等级
	t.totalLevel = totalLevel

	// 计算共鸣等级，每10级为一阶
	t.resonanceLevel = totalLevel / 10

	log.Info("Hero resonance calculated",
		log.Kv("player_id", t.player.Uid()),
		log.Kv("total_level", t.totalLevel),
		log.Kv("resonance_level", t.resonanceLevel))
}

// GetTotalLevel 获取英雄总等级
func (t *Hero) GetTotalLevel() int32 {
	return t.totalLevel
}

// GetResonanceLevel 获取共鸣等级
func (t *Hero) GetResonanceLevel() int32 {
	return t.resonanceLevel
}

// getHeroMaxLevelByAwakeLevel 根据觉醒等级获取英雄最大等级
// 如果英雄没有觉醒配置或觉醒等级超出配置范围，返回-1表示无限制
func (t *Hero) getHeroMaxLevelByAwakeLevel(heroId int32, awakeLevel int32) int32 {
	heroInfo := table.GetTable().TableHero.GetById(heroId)
	if heroInfo == nil {
		return -1
	}

	// 如果没有HeroLv配置或为空，表示没有觉醒限制
	if len(heroInfo.HeroLv) == 0 {
		return -1
	}

	// 觉醒等级从0开始，对应数组下标
	if awakeLevel < 0 || int(awakeLevel) >= len(heroInfo.HeroLv) {
		return -1
	}

	return heroInfo.HeroLv[awakeLevel]
}

// isHeroSupportAwake 判断英雄是否支持觉醒
func (t *Hero) isHeroSupportAwake(heroId int32) bool {
	heroInfo := table.GetTable().TableHero.GetById(heroId)
	if heroInfo == nil {
		return false
	}

	// 如果ItemCost配置为空，表示此英雄没有觉醒
	// ItemCost才是控制觉醒消耗的关键，即使HeroLv有限制也没意义
	return len(heroInfo.ItemCost) > 0
}

// getHeroAwakeMaxLevel 获取英雄觉醒的最大等级
func (t *Hero) getHeroAwakeMaxLevel(heroId int32) int32 {
	heroInfo := table.GetTable().TableHero.GetById(heroId)
	if heroInfo == nil || len(heroInfo.ItemCost) == 0 {
		return 0
	}
	return int32(len(heroInfo.ItemCost))
}

// getHeroAwakeCost 获取英雄觉醒升级所需的物品和数量
// 返回值：物品ID列表，物品数量，是否有效
func (t *Hero) getHeroAwakeCost(heroId int32, targetAwakeLevel int32) ([]int32, int32, bool) {
	heroInfo := table.GetTable().TableHero.GetById(heroId)
	if heroInfo == nil {
		return nil, 0, false
	}

	// 检查ItemCost配置
	if len(heroInfo.ItemCost) == 0 {
		return nil, 0, false
	}

	// 检查觉醒等级是否在有效范围内（1-based）
	if targetAwakeLevel < 1 || targetAwakeLevel > int32(len(heroInfo.ItemCost)) {
		return nil, 0, false
	}

	// 获取物品ID列表（支持多个物品，为后期扩容做准备）
	var itemIds []int32
	if len(heroInfo.ItemId) > 0 {
		itemIds = heroInfo.ItemId
	} else {
		// 如果没有配置ItemId，返回无效
		return nil, 0, false
	}

	// 获取对应觉醒等级的消耗数量（数组索引从0开始）
	itemCost := heroInfo.ItemCost[targetAwakeLevel-1]

	return itemIds, itemCost, true
}

// getHeroAwakeRequiredLevel 获取英雄觉醒所需的英雄等级
func (t *Hero) getHeroAwakeRequiredLevel(heroId int32, targetAwakeLevel int32) (int32, bool) {
	heroInfo := table.GetTable().TableHero.GetById(heroId)
	if heroInfo == nil || len(heroInfo.HeroLv) == 0 {
		return 0, false
	}

	// 检查觉醒等级是否在有效范围内（1-based）
	if targetAwakeLevel < 1 || targetAwakeLevel > int32(len(heroInfo.HeroLv)) {
		return 0, false
	}

	// 获取对应觉醒等级所需的英雄等级（数组索引从0开始）
	requiredLevel := heroInfo.HeroLv[targetAwakeLevel-1]
	return requiredLevel, true
}

// CanHeroLevelUp 检查英雄是否可以升级
// 返回值：是否可以升级，错误码（如果不能升级）
func (t *Hero) CanHeroLevelUp(heroId int32) (bool, error_code.Code) {
	// 1. 查找英雄
	hero := t.GetHeroById(heroId)
	if hero == nil {
		return false, error_code.ERROR_HERO_NOT_FOUND
	}

	// 2. 从TableHero获取英雄品质
	heroInfo := table.GetTable().TableHero.GetById(heroId)
	if heroInfo == nil {
		return false, error_code.ERROR_HERO_NOT_FOUND
	}

	// 3. 构建TableHeroLevel的ID
	heroLevelId := heroInfo.QualityId*100 + hero.HeroLevel
	heroLevelInfo := table.GetTable().TableHeroLevel.GetById(heroLevelId)
	if heroLevelInfo == nil {
		return false, error_code.ERROR_PARAMS
	}

	// 4. 检查觉醒等级限制
	if t.isHeroSupportAwake(heroId) {
		maxLevel := t.getHeroMaxLevelByAwakeLevel(heroId, hero.AwakeLevel)
		if maxLevel > 0 && hero.HeroLevel >= maxLevel {
			return false, error_code.ERROR_HERO_AWAKE_LEVEL_NOT_ENOUGH
		}
	}

	// 5. 检查经验是否足够
	if hero.Exp < heroLevelInfo.Cost {
		return false, error_code.ERROR_HERO_EXP_NOT_ENOUGH
	}

	// 6. 检查金币是否足够
	if !t.player.Money().IsCanDelMoney(1, heroLevelInfo.GoldCost) {
		return false, error_code.ERROR_NO_MONEY
	}

	// 7. 检查CostItem是否足够
	if len(heroLevelInfo.CostItem) >= 2 {
		costItemId := heroLevelInfo.CostItem[0]
		costItemCount := heroLevelInfo.CostItem[1]
		if !t.player.Item().IsCanDelItem(costItemId, costItemCount) {
			return false, error_code.ERROR_NO_ITEM
		}
	}

	return true, error_code.ERROR_OK
}

// CanHeroAwakeLevelUp 检查英雄是否可以觉醒升级
// 返回值：是否可以觉醒，错误码（如果不能觉醒）
func (t *Hero) CanHeroAwakeLevelUp(heroId int32) (bool, error_code.Code) {
	// 1. 查找英雄
	hero := t.GetHeroById(heroId)
	if hero == nil {
		return false, error_code.ERROR_HERO_NOT_FOUND
	}

	// 2. 检查英雄是否支持觉醒
	if !t.isHeroSupportAwake(heroId) {
		return false, error_code.ERROR_PARAMS
	}

	// 3. 检查觉醒等级是否已达到上限
	maxAwakeLevel := t.getHeroAwakeMaxLevel(heroId)
	if hero.AwakeLevel >= maxAwakeLevel {
		return false, error_code.ERROR_PARAMS
	}

	// 4. 计算下一个觉醒等级
	nextAwakeLevel := hero.AwakeLevel + 1

	// 5. 检查英雄等级是否满足觉醒要求
	requiredHeroLevel, hasLevelRequirement := t.getHeroAwakeRequiredLevel(heroId, nextAwakeLevel)
	if hasLevelRequirement && hero.HeroLevel < requiredHeroLevel {
		return false, error_code.ERROR_HERO_LEVEL_NOT_ENOUGH
	}

	// 6. 检查觉醒消耗
	itemIds, itemCost, hasCost := t.getHeroAwakeCost(heroId, nextAwakeLevel)
	if !hasCost {
		return false, error_code.ERROR_PARAMS
	}

	// 7. 检查所有物品数量是否足够
	for _, itemId := range itemIds {
		if !t.player.Item().IsCanDelItem(itemId, itemCost) {
			return false, error_code.ERROR_NO_ITEM
		}
	}

	return true, error_code.ERROR_OK
}

// GetHeroById 根据ID获取英雄
func (t *Hero) GetHeroById(heroId int32) *public.PBHeroInfo {
	for _, hero := range t.db.HeroList {
		if hero.HeroId == heroId {
			return hero
		}
	}
	return nil
}

// AddHeroExp 增加英雄碎片/经验
// 如果没有英雄，获得一个碎片即直接拥有英雄，经验为0
// 如果有英雄，增加对应数量的碎片作为经验值
func (t *Hero) AddHeroExp(heroId int32, expAmount int32) error_code.Code {
	hero := t.GetHeroById(heroId)

	if hero == nil {
		// 检查英雄ID是否在hero表中存在
		heroData := table.GetTable().TableHero.GetById(heroId)
		if heroData == nil {
			log.Error("Invalid hero ID, hero not found in table",
				log.Kv("player_id", t.player.Uid()),
				log.Kv("hero_id", heroId))
			return error_code.ERROR_HERO_NOT_FOUND
		}

		// 英雄不存在，创建新英雄
		newHero := &public.PBHeroInfo{
			HeroId:    heroId,
			HeroLevel: heroData.BaseLvl, // 使用配置表中的初始等级
			Exp:       0,                // 初始经验为0
		}
		t.db.HeroList = append(t.db.HeroList, newHero)
		log.Info("New hero created",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("hero_id", heroId),
			log.Kv("base_level", heroData.BaseLvl))
	} else {
		// 英雄已存在，增加经验值
		hero.Exp += expAmount
		log.Info("Hero exp added",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("hero_id", heroId),
			log.Kv("exp_added", expAmount),
			log.Kv("current_exp", hero.Exp))
	}

	return error_code.ERROR_OK
}

// HeroLevelUp 英雄升级
// 规则：使用exp字段和表中对应英雄等级的cost比对，经验>=cost则升一级并扣除对应经验
func (t *Hero) HeroLevelUp(heroId int32) (int32, int32, error_code.Code) {
	// 1. 查找英雄
	hero := t.GetHeroById(heroId)
	if hero == nil {
		log.Error("Hero not found",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("hero_id", heroId))
		return 0, 0, error_code.ERROR_HERO_NOT_FOUND
	}

	// 2. 从TableHero获取英雄品质
	heroInfo := table.GetTable().TableHero.GetById(heroId)
	if heroInfo == nil {
		log.Error("Hero config not found in TableHero",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("hero_id", heroId))
		return 0, 0, error_code.ERROR_HERO_NOT_FOUND
	}

	// 3. 构建TableHeroLevel的ID：品质*100 + 当前等级
	// 例如：品质2的英雄从9级升10级，ID = 2*100 + 9 = 209
	heroLevelId := heroInfo.QualityId*100 + hero.HeroLevel
	heroLevelInfo := table.GetTable().TableHeroLevel.GetById(heroLevelId)
	if heroLevelInfo == nil {
		log.Error("Hero level config not found in TableHeroLevel",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("hero_id", heroId),
			log.Kv("quality_id", heroInfo.QualityId),
			log.Kv("current_level", hero.HeroLevel),
			log.Kv("hero_level_id", heroLevelId))
		return 0, 0, error_code.ERROR_PARAMS
	}

	// 4. 检查觉醒等级是否满足升级要求
	// 如果英雄支持觉醒，需要检查当前觉醒等级允许的最大等级
	if t.isHeroSupportAwake(heroId) {
		maxLevel := t.getHeroMaxLevelByAwakeLevel(heroId, hero.AwakeLevel)
		if maxLevel > 0 && hero.HeroLevel >= maxLevel {
			log.Error("Hero level reached awake level limit",
				log.Kv("player_id", t.player.Uid()),
				log.Kv("hero_id", heroId),
				log.Kv("current_level", hero.HeroLevel),
				log.Kv("current_awake_level", hero.AwakeLevel),
				log.Kv("max_level_for_awake", maxLevel))
			return 0, 0, error_code.ERROR_HERO_AWAKE_LEVEL_NOT_ENOUGH
		}
	}

	// 5. 检查经验是否足够
	if hero.Exp < heroLevelInfo.Cost {
		log.Error("Hero exp not enough",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("hero_id", heroId),
			log.Kv("current_exp", hero.Exp),
			log.Kv("required_exp", heroLevelInfo.Cost))
		return 0, 0, error_code.ERROR_HERO_EXP_NOT_ENOUGH
	}

	// 6. 检查金币是否足够
	if !t.player.Money().IsCanDelMoney(1, heroLevelInfo.GoldCost) { // 1表示金币ID
		log.Error("Gold not enough",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("required_gold", heroLevelInfo.GoldCost))
		return 0, 0, error_code.ERROR_NO_MONEY
	}

	// 7. 检查CostItem是否足够（如果有配置的话）
	if len(heroLevelInfo.CostItem) >= 2 {
		costItemId := heroLevelInfo.CostItem[0]
		costItemCount := heroLevelInfo.CostItem[1]
		if !t.player.Item().IsCanDelItem(costItemId, costItemCount) {
			log.Error("Cost item not enough",
				log.Kv("player_id", t.player.Uid()),
				log.Kv("required_item_id", costItemId),
				log.Kv("required_count", costItemCount),
				log.Kv("current_count", t.player.Item().GetItemNumById(costItemId)))
			return 0, 0, error_code.ERROR_NO_ITEM
		}
	}

	// 8. 扣除经验、金币和CostItem，增加等级
	hero.Exp -= heroLevelInfo.Cost

	// 扣除金币
	delResult := t.player.Money().DelMoney(1, heroLevelInfo.GoldCost, game_def.DEL_ITEM_HERO_LEVELUP)
	if !delResult {
		log.Error("Fail to deduct gold",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("gold", heroLevelInfo.GoldCost))
		return 0, 0, error_code.ERROR_OPT_FAILE
	}

	// 扣除CostItem（如果有配置的话）
	if len(heroLevelInfo.CostItem) >= 2 {
		costItemId := heroLevelInfo.CostItem[0]
		costItemCount := heroLevelInfo.CostItem[1]
		delItemResult := t.player.Item().DelItem(costItemId, costItemCount, game_def.DEL_ITEM_HERO_LEVELUP)
		if !delItemResult {
			log.Error("Fail to deduct cost item",
				log.Kv("player_id", t.player.Uid()),
				log.Kv("item_id", costItemId),
				log.Kv("item_count", costItemCount))
			return 0, 0, error_code.ERROR_OPT_FAILE
		}
	}

	// 9. 升级
	hero.HeroLevel++

	// 记录升级日志
	if len(heroLevelInfo.CostItem) >= 2 {
		log.Info("Hero level up successful",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("hero_id", heroId),
			log.Kv("new_level", hero.HeroLevel),
			log.Kv("remaining_exp", hero.Exp),
			log.Kv("consumed_gold", heroLevelInfo.GoldCost),
			log.Kv("consumed_item_id", heroLevelInfo.CostItem[0]),
			log.Kv("consumed_item_count", heroLevelInfo.CostItem[1]))
	} else {
		log.Info("Hero level up successful",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("hero_id", heroId),
			log.Kv("new_level", hero.HeroLevel),
			log.Kv("remaining_exp", hero.Exp),
			log.Kv("consumed_gold", heroLevelInfo.GoldCost))
	}

	// 更新共鸣等级
	t.calculateTotalLevelAndResonance()

	return hero.HeroLevel, hero.Exp, error_code.ERROR_OK
}

// HeroAwakeLevelUp 英雄觉醒等级升级
// 规则：使用 TableHero 配置表的ItemId和ItemCost字段，根据英雄ID和觉醒等级获取所需物品和数量
func (t *Hero) HeroAwakeLevelUp(heroId int32) (int32, error_code.Code) {
	// 1. 查找英雄
	hero := t.GetHeroById(heroId)
	if hero == nil {
		log.Error("Hero not found for awake level up",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("hero_id", heroId))
		return 0, error_code.ERROR_HERO_NOT_FOUND
	}

	// 2. 检查英雄是否支持觉醒
	if !t.isHeroSupportAwake(heroId) {
		log.Error("Hero does not support awake",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("hero_id", heroId))
		return 0, error_code.ERROR_PARAMS
	}

	// 3. 检查觉醒等级是否已达到上限
	maxAwakeLevel := t.getHeroAwakeMaxLevel(heroId)
	if hero.AwakeLevel >= maxAwakeLevel {
		log.Error("Hero awake level already at max",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("hero_id", heroId),
			log.Kv("current_awake_level", hero.AwakeLevel),
			log.Kv("max_awake_level", maxAwakeLevel))
		return 0, error_code.ERROR_PARAMS
	}

	// 4. 计算下一个觉醒等级
	nextAwakeLevel := hero.AwakeLevel + 1

	// 5. 检查英雄等级是否满足觉醒要求
	requiredHeroLevel, hasLevelRequirement := t.getHeroAwakeRequiredLevel(heroId, nextAwakeLevel)
	if hasLevelRequirement && hero.HeroLevel < requiredHeroLevel {
		log.Error("Hero level not enough for awake level up",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("hero_id", heroId),
			log.Kv("current_hero_level", hero.HeroLevel),
			log.Kv("required_hero_level", requiredHeroLevel),
			log.Kv("next_awake_level", nextAwakeLevel))
		return 0, error_code.ERROR_HERO_LEVEL_NOT_ENOUGH
	}

	// 6. 获取觉醒消耗
	itemIds, itemCost, hasCost := t.getHeroAwakeCost(heroId, nextAwakeLevel)
	if !hasCost {
		log.Error("Hero awake cost config not found",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("hero_id", heroId),
			log.Kv("next_awake_level", nextAwakeLevel))
		return 0, error_code.ERROR_PARAMS
	}

	// 7. 检查所有物品数量是否足够
	for _, itemId := range itemIds {
		if !t.player.Item().IsCanDelItem(itemId, itemCost) {
			log.Error("Item not enough for awake level up",
				log.Kv("player_id", t.player.Uid()),
				log.Kv("hero_id", heroId),
				log.Kv("required_item_id", itemId),
				log.Kv("required_count", itemCost),
				log.Kv("current_count", t.player.Item().GetItemNumById(itemId)))
			return 0, error_code.ERROR_NO_ITEM
		}
	}

	// 8. 扣除所有物品
	for _, itemId := range itemIds {
		delResult := t.player.Item().DelItem(itemId, itemCost, game_def.DEL_ITEM_HERO_AWAKE_LEVELUP) // 英雄觉醒等级升级操作
		if !delResult {
			log.Error("Fail to deduct item for awake level up",
				log.Kv("player_id", t.player.Uid()),
				log.Kv("hero_id", heroId),
				log.Kv("item_id", itemId),
				log.Kv("item_count", itemCost))
			return 0, error_code.ERROR_OPT_FAILE
		}
	}

	// 9. 觉醒等级升级
	hero.AwakeLevel++
	log.Info("Hero awake level up successful",
		log.Kv("player_id", t.player.Uid()),
		log.Kv("hero_id", heroId),
		log.Kv("new_awake_level", hero.AwakeLevel),
		log.Kv("consumed_item_ids", itemIds),
		log.Kv("consumed_item_count", itemCost))

	// 觉醒等级升级后需要重新计算共鸣等级
	t.calculateTotalLevelAndResonance()

	return hero.AwakeLevel, error_code.ERROR_OK
}

// RemoveHero 移除英雄并同步更新阵容
func (t *Hero) RemoveHero(heroId int32) error_code.Code {
	// 查找要移除的英雄索引
	foundIndex := -1
	for i, hero := range t.db.HeroList {
		if hero.HeroId == heroId {
			foundIndex = i
			break
		}
	}

	// 如果未找到英雄，返回失败
	if foundIndex == -1 {
		log.Error("Hero not found for removal",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("hero_id", heroId))
		return error_code.ERROR_HERO_NOT_FOUND
	}

	// 移除英雄
	t.db.HeroList = append(t.db.HeroList[:foundIndex], t.db.HeroList[foundIndex+1:]...)

	// 同步更新阵容中的英雄
	t.removeHeroFromAllLineups(heroId)

	// 重新计算共鸣等级
	t.calculateTotalLevelAndResonance()

	log.Info("Hero removed successfully",
		log.Kv("player_id", t.player.Uid()),
		log.Kv("hero_id", heroId))

	return error_code.ERROR_OK
}

// removeHeroFromAllLineups 从所有阵容中移除指定英雄
func (t *Hero) removeHeroFromAllLineups(heroId int32) {
	lineup := t.player.Lineup()
	if lineup == nil || lineup.db == nil {
		return
	}

	removedCount := 0
	// 遍历所有阵容，移除指定英雄
	for _, lineupInfo := range lineup.db.LineupList {
		for i := len(lineupInfo.HeroIds) - 1; i >= 0; i-- {
			if lineupInfo.HeroIds[i] == heroId {
				// 移除英雄
				lineupInfo.HeroIds = append(lineupInfo.HeroIds[:i], lineupInfo.HeroIds[i+1:]...)
				removedCount++
				log.Info("Hero removed from lineup",
					log.Kv("player_id", t.player.Uid()),
					log.Kv("lineup_id", lineupInfo.Id),
					log.Kv("hero_id", heroId))
			}
		}
	}

	if removedCount > 0 {
		log.Info("Hero removed from lineups",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("hero_id", heroId),
			log.Kv("removed_count", removedCount))
	}
}
