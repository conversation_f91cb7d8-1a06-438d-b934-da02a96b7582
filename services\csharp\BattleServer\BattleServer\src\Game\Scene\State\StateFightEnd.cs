﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Aurora.Framework;

namespace BattleServer.Game
{
    public class StateFightEnd : State
    {
        public StateFightEnd(StateComponent stateComponent) : base(stateComponent)
        {
        }

        public override void OnEnter()
        {
            Log.Debug("[StateEnd] OnEnter");
            Log.Info("[StateEnd] OnEnter");

            OnSecenBattleEnd();

            if (_stateComponent.GetScene().GetPlayers().Count() < 2)
            {
                _stateComponent.ChangeState(StateType.Settlement);
            }
            else
            {
                _stateComponent.ChangeState(StateType.Wait);
            }
        }

        public override void OnUpdate(float deltaTime)
        {
            
        }

        private void OnSecenBattleEnd()
        {
            foreach (var battle in _stateComponent.GetScene().GetBattles())
            {
                battle.OnBattleEnd();
            }
        }
    }
}
