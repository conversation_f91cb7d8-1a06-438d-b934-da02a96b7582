package dbhelper

import (
	"encoding/json"
	"fmt"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/game-logic/gameserver/global"
	"reflect"
	"strconv"

	"liteframe/internal/common/constant/def"
	"liteframe/internal/common/protos/dbstruct"
	"liteframe/internal/common/protos/g2m"

	"liteframe/internal/game-logic/gameserver/game_def/actor_def"
	"liteframe/internal/game-logic/gameserver/gameutil/dbutil"
	"liteframe/pkg/actor"
	"liteframe/pkg/log"

	"google.golang.org/protobuf/proto"
)

// db 操作的异步接口  使用该文件里的接口进行数据的存取

// SaveUser 保存玩家数据
func SaveUser(pid actor.PID, uid uint64, user *dbstruct.UserDB, cb func(err error)) {
	base, game, _ := dbutil.Marshal(user)
	tmp, _ := dbutil.UnmarshalMerge(base, game)
	d := &g2m.G2M_SaveUser{
		Data: [][]byte{base, game},
		User: tmp,
	}
	msg := &actor.Message{
		Id:   uint32(def.MsgId_Mongo_SaveUser),
		Uid:  uid,
		Data: d}

	actor.AsyncRequest(pid, actor_def.DBSystemPID, msg, func(msg actor.RespMessage) { // 通知 player system 存储完成
		if cb != nil {
			cb(msg.Err)
		}
	})
}

func AccountLogin(pid actor.PID, account string, serverId uint64, cb func(user *g2m.M2G_LoadCreateUid, err error)) {
	d := &g2m.G2M_LoadCreateUid{
		Account:  account,
		ServerId: serverId,
	}

	msg := &actor.Message{
		Id:   uint32(def.MsgId_Mongo_LoadOrCreateUid),
		Data: d,
	}
	actor.AsyncRequest(pid, actor_def.DBSystemPID, msg, func(msg actor.RespMessage) {
		d := msg.Data.(*g2m.M2G_LoadCreateUid)
		if msg.Err != nil {
			cb(d, msg.Err)
			return
		}

		cb(d, nil)
	})
}

// LoadUidByAccount 加载玩家数据
func LoadUidByAccount(pid actor.PID, account string, serverId uint64, cb func(uid uint64, err error)) {
	d := &g2m.G2M_LoadCreateUid{
		Account:  account,
		ServerId: serverId,
	}

	msg := &actor.Message{
		Id:   uint32(def.MsgId_Mongo_LoadUidByAccount),
		Data: d,
	}
	actor.AsyncRequest(pid, actor_def.DBSystemPID, msg, func(msg actor.RespMessage) {
		if msg.Err != nil {
			cb(0, msg.Err)
			return
		}

		d := msg.Data.(uint64)
		cb(d, nil)
	})
}

// LoadData 异步加载数据  cb 的参数 out 必须是引用类型!!! 否则 panic
func LoadData[V any](pid actor.PID, key string, cb func(out V, err error)) {
	sid, _ := strconv.ParseUint(key, 10, 64)
	d := &g2m.G2M_LoadData{ServerId: sid}
	msg := &actor.Message{
		Id:   uint32(def.MsgId_Mongo_LoadData),
		Data: d,
	}

	actor.AsyncRequest(pid, actor_def.DBSystemPID, msg, func(msg actor.RespMessage) {
		var ev V

		if msg.Err != nil {
			cb(ev, msg.Err)
			return
		}

		if msg.Data == nil {
			cb(ev, fmt.Errorf("data nil"))
			return
		}

		d := msg.Data.(*g2m.M2G_LoadData)
		if len(d.Data) == 0 {
			cb(ev, fmt.Errorf("pid %v load key %s data empty", pid, key))
			return
		}

		rt := reflect.ValueOf(ev).Type()
		rv := reflect.New(rt.Elem()).Interface()

		if pb, ok := rv.(proto.Message); ok {
			err := proto.Unmarshal(d.Data, pb)
			cb(rv.(V), err)
			return
		}

		err := json.Unmarshal(d.Data, rv)
		cb(rv.(V), err)
	})
}

// SaveData 异步存储数据接口
func SaveData(pid actor.PID, key string, data interface{}, cb func(err error)) {
	sid, _ := strconv.ParseUint(key, 10, 64)
	d := &g2m.G2M_SaveData{
		ServerId: sid,
	}
	msg := &actor.Message{
		Id:   uint32(def.MsgId_Mongo_SaveData),
		Uid:  0,
		Data: d,
	}

	switch v := data.(type) {
	case []byte:
		d.Data = v
	case proto.Message:
		var err error
		d.Data, err = proto.Marshal(v)
		if err != nil {
			log.Error("proto marshal error", log.Err(err))
			if cb != nil {
				cb(err)
			}
			return
		}
	default:
		var err error
		d.Data, err = json.Marshal(v)
		if err != nil {
			cb(fmt.Errorf("json marshal error %v", err.Error()))
			return
		}
	}
	actor.AsyncRequest(pid, actor_def.DBSystemPID, msg, func(msg actor.RespMessage) {
		if cb != nil {
			cb(msg.Err)
		}
	})
}

// DeleteData 异步删除接口
func DeleteData(pid actor.PID, key string, cb func(err error)) {
	sid, _ := strconv.ParseUint(key, 10, 64)
	d := &g2m.G2M_DeleteData{
		ServerId: sid,
	}
	msg := &actor.Message{
		Id:   uint32(def.MsgId_Mongo_DeleteData),
		Data: d,
	}
	actor.AsyncRequest(pid, actor_def.DBSystemPID, msg, func(msg actor.RespMessage) {
		if cb != nil {
			cb(msg.Err)
		}
	})
}

func FindDuplicateNames(pid actor.PID, name string, cb func(user *g2m.M2G_FindDuplicateNamesData, err error)) {
	d := &g2m.G2M_FindDuplicateNamesData{
		Name: name,
	}

	msg := &actor.Message{
		Id:   uint32(def.MsgId_Actor_FindDuplicateNames),
		Data: d,
	}
	actor.AsyncRequest(pid, actor_def.DBSystemPID, msg, func(msg actor.RespMessage) {
		d := msg.Data.(*g2m.M2G_FindDuplicateNamesData)
		if msg.Err != nil {
			cb(d, msg.Err)
			return
		}

		cb(d, nil)
	})
}

func ChangeName(pid actor.PID, uid uint64, oldName, name string, cb func(err error)) {
	d := &g2m.G2M_ChangeNameData{
		Uid:     uid,
		Name:    name,
		OldName: oldName,
	}

	msg := &actor.Message{
		Id:   uint32(def.MsgId_Actor_ChangeNames),
		Data: d,
	}
	actor.AsyncRequest(pid, actor_def.DBSystemPID, msg, func(msg actor.RespMessage) {
		if cb != nil {
			cb(msg.Err)
		}
	})
}

// LoadGlobalMail 加载全局邮件
func LoadGlobalMail(pid actor.PID, cb func(mails []*public.PBMail, err error)) {
	msg := &actor.Message{
		Id: uint32(def.MsgId_Mongo_LoadGlobalMail),
	}

	actor.AsyncRequest(pid, actor_def.DBSystemPID, msg, func(resp actor.RespMessage) {
		if resp.Err != nil {
			cb(nil, resp.Err)
			return
		}
		mails := resp.Data.([]*public.PBMail)
		cb(mails, nil)
	})
}

// SaveGlobalMail 保存全局邮件
func SaveGlobalMail(pid actor.PID, mailData *dbstruct.MailList, cb func(error)) {
	msg := &actor.Message{
		Id: uint32(def.MsgId_Mongo_SaveGlobalMail),
		Data: g2m.G2M_SaveGlobalMail{
			List: mailData,
		},
	}

	actor.AsyncRequest(pid, actor_def.DBSystemPID, msg, func(resp actor.RespMessage) {
		if cb != nil {
			cb(resp.Err)
		}
	})
}

// LoadArenaSystem 加载竞技场系统
func LoadArenaSystem(pid actor.PID, cb func(loadData []byte, err error)) {
	msg := &actor.Message{
		Id: uint32(def.MsgId_Mongo_LoadArenaSystem),
		Data: &g2m.G2M_LoadData{
			ServerId: uint64(global.ServerId),
		},
	}

	actor.AsyncRequest(pid, actor_def.DBSystemPID, msg, func(resp actor.RespMessage) {
		if resp.Err != nil {
			cb(nil, resp.Err)
			return
		}
		loadData := resp.Data.(*g2m.M2G_LoadData)
		cb(loadData.Data, nil)
	})
}

// SaveArenaSystem 保存竞技场数据
func SaveArenaSystem(pid actor.PID, arenaData *dbstruct.ArenaSystemDB, cb func(error)) {
	var err error
	d := &g2m.G2M_SaveData{
		ServerId: uint64(global.ServerId),
	}
	d.Data, err = proto.Marshal(arenaData)
	if err != nil {
		log.Error("proto marshal error", log.Err(err))
		if cb != nil {
			cb(err)
		}
		return
	}
	msg := &actor.Message{
		Id:   uint32(def.MsgId_Mongo_SaveArenaSystem),
		Data: d,
	}

	actor.AsyncRequest(pid, actor_def.DBSystemPID, msg, func(resp actor.RespMessage) {
		if cb != nil {
			cb(resp.Err)
		}
	})
}

// SaveGuildData 异步存储单个公会数据
func SaveGuildData(pid actor.PID, guildId int64, data interface{}, cb func(err error)) {
	d := &g2m.G2M_SaveGuildData{
		GuildId: guildId,
	}
	msg := &actor.Message{
		Id:   uint32(def.MsgId_Mongo_SaveGuildData),
		Uid:  0,
		Data: d,
	}

	switch v := data.(type) {
	case []byte:
		d.GuildData = v
	case proto.Message:
		var err error
		d.GuildData, err = proto.Marshal(v)
		if err != nil {
			log.Error("proto marshal error", log.Err(err))
			if cb != nil {
				cb(err)
			}
			return
		}
	default:
		var err error
		d.GuildData, err = json.Marshal(v)
		if err != nil {
			cb(fmt.Errorf("json marshal error %v", err.Error()))
			return
		}
	}
	actor.AsyncRequest(pid, actor_def.DBSystemPID, msg, func(msg actor.RespMessage) {
		if cb != nil {
			cb(msg.Err)
		}
	})
}

// DeleteGuildData 异步删除公会数据
func DeleteGuildData(pid actor.PID, guildId int64, cb func(err error)) {
	d := &g2m.G2M_DeleteGuildData{
		GuildId: guildId,
	}
	msg := &actor.Message{
		Id:   uint32(def.MsgId_Mongo_DeleteGuildData),
		Data: d,
	}
	actor.AsyncRequest(pid, actor_def.DBSystemPID, msg, func(msg actor.RespMessage) {
		if cb != nil {
			cb(msg.Err)
		}
	})
}

// SavePaymentData 异步存储支付系统数据
func SavePaymentData(pid actor.PID, orderKey string, data interface{}, cb func(err error)) {
	d := &g2m.G2M_SavePaymentData{
		OrderKey: orderKey,
	}
	msg := &actor.Message{
		Id:   uint32(def.MsgId_Mongo_SavePaymentData),
		Uid:  0,
		Data: d,
	}

	switch v := data.(type) {
	case []byte:
		d.PaymentData = v
	case proto.Message:
		var err error
		d.PaymentData, err = proto.Marshal(v)
		if err != nil {
			log.Error("proto marshal error", log.Err(err))
			if cb != nil {
				cb(err)
			}
			return
		}
	default:
		var err error
		d.PaymentData, err = json.Marshal(v)
		if err != nil {
			cb(fmt.Errorf("json marshal error %v", err.Error()))
			return
		}
	}
	actor.AsyncRequest(pid, actor_def.DBSystemPID, msg, func(msg actor.RespMessage) {
		if cb != nil {
			cb(msg.Err)
		}
	})
}

// SaveTipOffData 异步举报系统数据
func SaveTipOffData(pid actor.PID, data interface{}, cb func(err error)) {
	d := &g2m.G2M_SaveTipOffData{}
	msg := &actor.Message{
		Id:   uint32(def.MsgId_Mongo_SaveTipOffData),
		Uid:  0,
		Data: d,
	}

	switch v := data.(type) {
	case []byte:
		d.TipOffData = v
	case proto.Message:
		var err error
		d.TipOffData, err = proto.Marshal(v)
		if err != nil {
			log.Error("proto marshal error", log.Err(err))
			if cb != nil {
				cb(err)
			}
			return
		}
	default:
		var err error
		d.TipOffData, err = json.Marshal(v)
		if err != nil {
			cb(fmt.Errorf("json marshal error %v", err.Error()))
			return
		}
	}
	actor.AsyncRequest(pid, actor_def.DBSystemPID, msg, func(msg actor.RespMessage) {
		if cb != nil {
			cb(msg.Err)
		}
	})
}

// SaveSeasonBuffDB 异步保存赛季buff系统数据
func SaveSeasonBuffDB(pid actor.PID, data interface{}, cb func(err error)) {
	d := &g2m.G2M_SaveSeasonBuffData{}
	msg := &actor.Message{
		Id:   uint32(def.MsgId_Mongo_SaveSeasonBuffData),
		Uid:  0,
		Data: d,
	}

	switch v := data.(type) {
	case []byte:
		d.SeasonBuffData = v
	case proto.Message:
		var err error
		d.SeasonBuffData, err = proto.Marshal(v)
		if err != nil {
			log.Error("proto marshal error", log.Err(err))
			if cb != nil {
				cb(err)
			}
			return
		}
	default:
		var err error
		d.SeasonBuffData, err = json.Marshal(v)
		if err != nil {
			cb(fmt.Errorf("json marshal error %v", err.Error()))
			return
		}
	}
	actor.AsyncRequest(pid, actor_def.DBSystemPID, msg, func(msg actor.RespMessage) {
		if cb != nil {
			cb(msg.Err)
		}
	})
}
