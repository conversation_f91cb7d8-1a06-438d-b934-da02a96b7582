// 协议ID类型为short，-32767 到 32767
//StartMessageID = 3000; // 必须以;分号结束
//MaxMessageID = 3999; // 必须以;分号结束

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.23.2
// source: GLProtocol.proto

package cs

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "liteframe/internal/common/protos/dbstruct"
	public "liteframe/internal/common/protos/public"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

//Test
type GL_Test_Res struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"` //1: 成功, 其他错误
}

func (x *GL_Test_Res) Reset() {
	*x = GL_Test_Res{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GLProtocol_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GL_Test_Res) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GL_Test_Res) ProtoMessage() {}

func (x *GL_Test_Res) ProtoReflect() protoreflect.Message {
	mi := &file_GLProtocol_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GL_Test_Res.ProtoReflect.Descriptor instead.
func (*GL_Test_Res) Descriptor() ([]byte, []int) {
	return file_GLProtocol_proto_rawDescGZIP(), []int{0}
}

func (x *GL_Test_Res) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

//通知 lobyy玩家是否需要重新laod 全局邮件
type Gl_GlobalMailNeedReload_Res struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerUidList []int64 `protobuf:"varint,1,rep,packed,name=playerUidList,proto3" json:"playerUidList,omitempty"` //玩家的list 信息
	Param1        int32   `protobuf:"varint,2,opt,name=param1,proto3" json:"param1,omitempty"`                      //额外参数1，INT型，一般用做唯一处理类型标识
	Param2        int64   `protobuf:"varint,3,opt,name=param2,proto3" json:"param2,omitempty"`                      //额外参数2，LONG型
	Param3        int64   `protobuf:"varint,4,opt,name=param3,proto3" json:"param3,omitempty"`                      //额外参数3，LONG型
}

func (x *Gl_GlobalMailNeedReload_Res) Reset() {
	*x = Gl_GlobalMailNeedReload_Res{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GLProtocol_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Gl_GlobalMailNeedReload_Res) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Gl_GlobalMailNeedReload_Res) ProtoMessage() {}

func (x *Gl_GlobalMailNeedReload_Res) ProtoReflect() protoreflect.Message {
	mi := &file_GLProtocol_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Gl_GlobalMailNeedReload_Res.ProtoReflect.Descriptor instead.
func (*Gl_GlobalMailNeedReload_Res) Descriptor() ([]byte, []int) {
	return file_GLProtocol_proto_rawDescGZIP(), []int{1}
}

func (x *Gl_GlobalMailNeedReload_Res) GetPlayerUidList() []int64 {
	if x != nil {
		return x.PlayerUidList
	}
	return nil
}

func (x *Gl_GlobalMailNeedReload_Res) GetParam1() int32 {
	if x != nil {
		return x.Param1
	}
	return 0
}

func (x *Gl_GlobalMailNeedReload_Res) GetParam2() int64 {
	if x != nil {
		return x.Param2
	}
	return 0
}

func (x *Gl_GlobalMailNeedReload_Res) GetParam3() int64 {
	if x != nil {
		return x.Param3
	}
	return 0
}

// 通知去请求好友信息
type Gl_NoticeSyncFriend_Res struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerUidInfo *public.PBCommonLongKeyValue `protobuf:"bytes,1,opt,name=playerUidInfo,proto3" json:"playerUidInfo,omitempty"` //玩家的id 和附加数据
}

func (x *Gl_NoticeSyncFriend_Res) Reset() {
	*x = Gl_NoticeSyncFriend_Res{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GLProtocol_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Gl_NoticeSyncFriend_Res) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Gl_NoticeSyncFriend_Res) ProtoMessage() {}

func (x *Gl_NoticeSyncFriend_Res) ProtoReflect() protoreflect.Message {
	mi := &file_GLProtocol_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Gl_NoticeSyncFriend_Res.ProtoReflect.Descriptor instead.
func (*Gl_NoticeSyncFriend_Res) Descriptor() ([]byte, []int) {
	return file_GLProtocol_proto_rawDescGZIP(), []int{2}
}

func (x *Gl_NoticeSyncFriend_Res) GetPlayerUidInfo() *public.PBCommonLongKeyValue {
	if x != nil {
		return x.PlayerUidInfo
	}
	return nil
}

// 踢人信息
type Gl_GlobalKickPlayer_Res struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type        int32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`                  // 0提所有人  1 按照玩家id踢
	PlayerUid   []int64 `protobuf:"varint,2,rep,packed,name=playerUid,proto3" json:"playerUid,omitempty"` //玩家的id
	SpecialArgs int32   `protobuf:"varint,3,opt,name=specialArgs,proto3" json:"specialArgs,omitempty"`    //特殊字段的信息
	BanTime     int64   `protobuf:"varint,4,opt,name=banTime,proto3" json:"banTime,omitempty"`
	Reason      string  `protobuf:"bytes,5,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *Gl_GlobalKickPlayer_Res) Reset() {
	*x = Gl_GlobalKickPlayer_Res{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GLProtocol_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Gl_GlobalKickPlayer_Res) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Gl_GlobalKickPlayer_Res) ProtoMessage() {}

func (x *Gl_GlobalKickPlayer_Res) ProtoReflect() protoreflect.Message {
	mi := &file_GLProtocol_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Gl_GlobalKickPlayer_Res.ProtoReflect.Descriptor instead.
func (*Gl_GlobalKickPlayer_Res) Descriptor() ([]byte, []int) {
	return file_GLProtocol_proto_rawDescGZIP(), []int{3}
}

func (x *Gl_GlobalKickPlayer_Res) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *Gl_GlobalKickPlayer_Res) GetPlayerUid() []int64 {
	if x != nil {
		return x.PlayerUid
	}
	return nil
}

func (x *Gl_GlobalKickPlayer_Res) GetSpecialArgs() int32 {
	if x != nil {
		return x.SpecialArgs
	}
	return 0
}

func (x *Gl_GlobalKickPlayer_Res) GetBanTime() int64 {
	if x != nil {
		return x.BanTime
	}
	return 0
}

func (x *Gl_GlobalKickPlayer_Res) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// 通知Lobby执行JS脚本信息
type GL_GlobalToLobbyRunJS_Res struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JsStr           string `protobuf:"bytes,1,opt,name=jsStr,proto3" json:"jsStr,omitempty"`                     //要运行的脚本信息
	IsClassCode     int32  `protobuf:"varint,2,opt,name=isClassCode,proto3" json:"isClassCode,omitempty"`        //是否为Class文件
	ScriptExecuteID string `protobuf:"bytes,3,opt,name=scriptExecuteID,proto3" json:"scriptExecuteID,omitempty"` //脚本唯一ID，CMS用于提取结果
}

func (x *GL_GlobalToLobbyRunJS_Res) Reset() {
	*x = GL_GlobalToLobbyRunJS_Res{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GLProtocol_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GL_GlobalToLobbyRunJS_Res) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GL_GlobalToLobbyRunJS_Res) ProtoMessage() {}

func (x *GL_GlobalToLobbyRunJS_Res) ProtoReflect() protoreflect.Message {
	mi := &file_GLProtocol_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GL_GlobalToLobbyRunJS_Res.ProtoReflect.Descriptor instead.
func (*GL_GlobalToLobbyRunJS_Res) Descriptor() ([]byte, []int) {
	return file_GLProtocol_proto_rawDescGZIP(), []int{4}
}

func (x *GL_GlobalToLobbyRunJS_Res) GetJsStr() string {
	if x != nil {
		return x.JsStr
	}
	return ""
}

func (x *GL_GlobalToLobbyRunJS_Res) GetIsClassCode() int32 {
	if x != nil {
		return x.IsClassCode
	}
	return 0
}

func (x *GL_GlobalToLobbyRunJS_Res) GetScriptExecuteID() string {
	if x != nil {
		return x.ScriptExecuteID
	}
	return ""
}

//通知 lobby 服务器状态信息
type Gl_GlobalGetAllActivityInfo_Res struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityInfoList []*public.PBActivityInfo `protobuf:"bytes,1,rep,name=activityInfoList,proto3" json:"activityInfoList,omitempty"` // 所有活动的信息
}

func (x *Gl_GlobalGetAllActivityInfo_Res) Reset() {
	*x = Gl_GlobalGetAllActivityInfo_Res{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GLProtocol_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Gl_GlobalGetAllActivityInfo_Res) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Gl_GlobalGetAllActivityInfo_Res) ProtoMessage() {}

func (x *Gl_GlobalGetAllActivityInfo_Res) ProtoReflect() protoreflect.Message {
	mi := &file_GLProtocol_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Gl_GlobalGetAllActivityInfo_Res.ProtoReflect.Descriptor instead.
func (*Gl_GlobalGetAllActivityInfo_Res) Descriptor() ([]byte, []int) {
	return file_GLProtocol_proto_rawDescGZIP(), []int{5}
}

func (x *Gl_GlobalGetAllActivityInfo_Res) GetActivityInfoList() []*public.PBActivityInfo {
	if x != nil {
		return x.ActivityInfoList
	}
	return nil
}

//通知 lobby 服务器状态信息
type Gl_GlobalGetActivityInfo_Res struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityInfo *public.PBActivityInfo `protobuf:"bytes,1,opt,name=activityInfo,proto3" json:"activityInfo,omitempty"` // 所有活动的信息
}

func (x *Gl_GlobalGetActivityInfo_Res) Reset() {
	*x = Gl_GlobalGetActivityInfo_Res{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GLProtocol_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Gl_GlobalGetActivityInfo_Res) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Gl_GlobalGetActivityInfo_Res) ProtoMessage() {}

func (x *Gl_GlobalGetActivityInfo_Res) ProtoReflect() protoreflect.Message {
	mi := &file_GLProtocol_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Gl_GlobalGetActivityInfo_Res.ProtoReflect.Descriptor instead.
func (*Gl_GlobalGetActivityInfo_Res) Descriptor() ([]byte, []int) {
	return file_GLProtocol_proto_rawDescGZIP(), []int{6}
}

func (x *Gl_GlobalGetActivityInfo_Res) GetActivityInfo() *public.PBActivityInfo {
	if x != nil {
		return x.ActivityInfo
	}
	return nil
}

//请求兑换码兑换
type GL_CDKeyUse_Res struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result        int32                      `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`               //处理结果：0.成功可以发奖 1.校验失败 2.单码已达兑换上限 3.此批已达兑换上限 4.不符合条件
	PlatformId    int64                      `protobuf:"varint,2,opt,name=platformId,proto3" json:"platformId,omitempty"`       //当前玩家ID
	Code          string                     `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`                    //兑换码
	ItemList      []*public.PBCommonKeyValue `protobuf:"bytes,4,rep,name=itemList,proto3" json:"itemList,omitempty"`            //奖励列表
	GroupId       int64                      `protobuf:"varint,5,opt,name=groupId,proto3" json:"groupId,omitempty"`             //当前所属批次ID
	GroupUseLimit int32                      `protobuf:"varint,6,opt,name=groupUseLimit,proto3" json:"groupUseLimit,omitempty"` //当前批次使用上限
	GroupName     string                     `protobuf:"bytes,7,opt,name=groupName,proto3" json:"groupName,omitempty"`          //礼包类型：BI埋点用
}

func (x *GL_CDKeyUse_Res) Reset() {
	*x = GL_CDKeyUse_Res{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GLProtocol_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GL_CDKeyUse_Res) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GL_CDKeyUse_Res) ProtoMessage() {}

func (x *GL_CDKeyUse_Res) ProtoReflect() protoreflect.Message {
	mi := &file_GLProtocol_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GL_CDKeyUse_Res.ProtoReflect.Descriptor instead.
func (*GL_CDKeyUse_Res) Descriptor() ([]byte, []int) {
	return file_GLProtocol_proto_rawDescGZIP(), []int{7}
}

func (x *GL_CDKeyUse_Res) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *GL_CDKeyUse_Res) GetPlatformId() int64 {
	if x != nil {
		return x.PlatformId
	}
	return 0
}

func (x *GL_CDKeyUse_Res) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *GL_CDKeyUse_Res) GetItemList() []*public.PBCommonKeyValue {
	if x != nil {
		return x.ItemList
	}
	return nil
}

func (x *GL_CDKeyUse_Res) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *GL_CDKeyUse_Res) GetGroupUseLimit() int32 {
	if x != nil {
		return x.GroupUseLimit
	}
	return 0
}

func (x *GL_CDKeyUse_Res) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

// 通知禁言信息
type Gl_GlobalChatBanPlayer_Res struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerUid   int64  `protobuf:"varint,1,opt,name=playerUid,proto3" json:"playerUid,omitempty"` //玩家的id
	ChatBanTime int64  `protobuf:"varint,2,opt,name=chatBanTime,proto3" json:"chatBanTime,omitempty"`
	Reason      string `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *Gl_GlobalChatBanPlayer_Res) Reset() {
	*x = Gl_GlobalChatBanPlayer_Res{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GLProtocol_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Gl_GlobalChatBanPlayer_Res) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Gl_GlobalChatBanPlayer_Res) ProtoMessage() {}

func (x *Gl_GlobalChatBanPlayer_Res) ProtoReflect() protoreflect.Message {
	mi := &file_GLProtocol_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Gl_GlobalChatBanPlayer_Res.ProtoReflect.Descriptor instead.
func (*Gl_GlobalChatBanPlayer_Res) Descriptor() ([]byte, []int) {
	return file_GLProtocol_proto_rawDescGZIP(), []int{8}
}

func (x *Gl_GlobalChatBanPlayer_Res) GetPlayerUid() int64 {
	if x != nil {
		return x.PlayerUid
	}
	return 0
}

func (x *Gl_GlobalChatBanPlayer_Res) GetChatBanTime() int64 {
	if x != nil {
		return x.ChatBanTime
	}
	return 0
}

func (x *Gl_GlobalChatBanPlayer_Res) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// 通知天气信息
type Gl_GlobalWeather_Res struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurWeatherId  int32 `protobuf:"varint,1,opt,name=curWeatherId,proto3" json:"curWeatherId,omitempty"`   //当前天气的id
	NextWeatherId int32 `protobuf:"varint,2,opt,name=nextWeatherId,proto3" json:"nextWeatherId,omitempty"` //下一次的天气id
}

func (x *Gl_GlobalWeather_Res) Reset() {
	*x = Gl_GlobalWeather_Res{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GLProtocol_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Gl_GlobalWeather_Res) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Gl_GlobalWeather_Res) ProtoMessage() {}

func (x *Gl_GlobalWeather_Res) ProtoReflect() protoreflect.Message {
	mi := &file_GLProtocol_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Gl_GlobalWeather_Res.ProtoReflect.Descriptor instead.
func (*Gl_GlobalWeather_Res) Descriptor() ([]byte, []int) {
	return file_GLProtocol_proto_rawDescGZIP(), []int{9}
}

func (x *Gl_GlobalWeather_Res) GetCurWeatherId() int32 {
	if x != nil {
		return x.CurWeatherId
	}
	return 0
}

func (x *Gl_GlobalWeather_Res) GetNextWeatherId() int32 {
	if x != nil {
		return x.NextWeatherId
	}
	return 0
}

//全局广播事件信息GlobalToAllLobby
type GL_GlobalBroadcastEvent_Res struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type       public.GlobalBroadcastEventType `protobuf:"varint,1,opt,name=type,proto3,enum=GlobalBroadcastEventType" json:"type,omitempty"` //事件类型
	Id         int64                           `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`                                   //操作ID
	IntParam1  int32                           `protobuf:"varint,3,opt,name=intParam1,proto3" json:"intParam1,omitempty"`                     //INT参数1
	IntParam2  int32                           `protobuf:"varint,4,opt,name=intParam2,proto3" json:"intParam2,omitempty"`                     //INT参数2
	IntParam3  int32                           `protobuf:"varint,5,opt,name=intParam3,proto3" json:"intParam3,omitempty"`                     //INT参数3
	LongParam1 int64                           `protobuf:"varint,6,opt,name=longParam1,proto3" json:"longParam1,omitempty"`                   //LONG参数1
	LongParam2 int64                           `protobuf:"varint,7,opt,name=longParam2,proto3" json:"longParam2,omitempty"`                   //LONG参数2
	LongParam3 int64                           `protobuf:"varint,8,opt,name=longParam3,proto3" json:"longParam3,omitempty"`                   //LONG参数3
	Param1     string                          `protobuf:"bytes,9,opt,name=param1,proto3" json:"param1,omitempty"`                            //String参数1
	Param2     string                          `protobuf:"bytes,10,opt,name=param2,proto3" json:"param2,omitempty"`                           //String参数2
	Param3     string                          `protobuf:"bytes,11,opt,name=param3,proto3" json:"param3,omitempty"`                           //String参数3
}

func (x *GL_GlobalBroadcastEvent_Res) Reset() {
	*x = GL_GlobalBroadcastEvent_Res{}
	if protoimpl.UnsafeEnabled {
		mi := &file_GLProtocol_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GL_GlobalBroadcastEvent_Res) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GL_GlobalBroadcastEvent_Res) ProtoMessage() {}

func (x *GL_GlobalBroadcastEvent_Res) ProtoReflect() protoreflect.Message {
	mi := &file_GLProtocol_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GL_GlobalBroadcastEvent_Res.ProtoReflect.Descriptor instead.
func (*GL_GlobalBroadcastEvent_Res) Descriptor() ([]byte, []int) {
	return file_GLProtocol_proto_rawDescGZIP(), []int{10}
}

func (x *GL_GlobalBroadcastEvent_Res) GetType() public.GlobalBroadcastEventType {
	if x != nil {
		return x.Type
	}
	return public.GlobalBroadcastEventType_GlobalBroadcastEventType_None
}

func (x *GL_GlobalBroadcastEvent_Res) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GL_GlobalBroadcastEvent_Res) GetIntParam1() int32 {
	if x != nil {
		return x.IntParam1
	}
	return 0
}

func (x *GL_GlobalBroadcastEvent_Res) GetIntParam2() int32 {
	if x != nil {
		return x.IntParam2
	}
	return 0
}

func (x *GL_GlobalBroadcastEvent_Res) GetIntParam3() int32 {
	if x != nil {
		return x.IntParam3
	}
	return 0
}

func (x *GL_GlobalBroadcastEvent_Res) GetLongParam1() int64 {
	if x != nil {
		return x.LongParam1
	}
	return 0
}

func (x *GL_GlobalBroadcastEvent_Res) GetLongParam2() int64 {
	if x != nil {
		return x.LongParam2
	}
	return 0
}

func (x *GL_GlobalBroadcastEvent_Res) GetLongParam3() int64 {
	if x != nil {
		return x.LongParam3
	}
	return 0
}

func (x *GL_GlobalBroadcastEvent_Res) GetParam1() string {
	if x != nil {
		return x.Param1
	}
	return ""
}

func (x *GL_GlobalBroadcastEvent_Res) GetParam2() string {
	if x != nil {
		return x.Param2
	}
	return ""
}

func (x *GL_GlobalBroadcastEvent_Res) GetParam3() string {
	if x != nil {
		return x.Param3
	}
	return ""
}

var File_GLProtocol_proto protoreflect.FileDescriptor

var file_GLProtocol_proto_rawDesc = []byte{
	0x0a, 0x10, 0x47, 0x4c, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0b, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x1a,
	0x13, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x10, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x45, 0x6e, 0x75, 0x6d,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x10, 0x44, 0x42, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x25, 0x0a, 0x0b, 0x47, 0x4c, 0x5f, 0x54,
	0x65, 0x73, 0x74, 0x5f, 0x52, 0x65, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x8b, 0x01, 0x0a, 0x1b, 0x47, 0x6c, 0x5f, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x4d, 0x61, 0x69,
	0x6c, 0x4e, 0x65, 0x65, 0x64, 0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x52, 0x65, 0x73, 0x12,
	0x24, 0x0a, 0x0d, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x55, 0x69, 0x64, 0x4c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x55, 0x69,
	0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x31, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x31, 0x12, 0x16, 0x0a,
	0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x32, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x32, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x33, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x33, 0x22, 0x56, 0x0a,
	0x17, 0x47, 0x6c, 0x5f, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x46, 0x72,
	0x69, 0x65, 0x6e, 0x64, 0x5f, 0x52, 0x65, 0x73, 0x12, 0x3b, 0x0a, 0x0d, 0x70, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x55, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x50, 0x42, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4c, 0x6f, 0x6e, 0x67, 0x4b, 0x65,
	0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x55, 0x69,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x9f, 0x01, 0x0a, 0x17, 0x47, 0x6c, 0x5f, 0x47, 0x6c, 0x6f,
	0x62, 0x61, 0x6c, 0x4b, 0x69, 0x63, 0x6b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x52, 0x65,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x55,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x55, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x41, 0x72,
	0x67, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61,
	0x6c, 0x41, 0x72, 0x67, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x6e, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x62, 0x61, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x7d, 0x0a, 0x19, 0x47, 0x4c, 0x5f, 0x47, 0x6c,
	0x6f, 0x62, 0x61, 0x6c, 0x54, 0x6f, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x52, 0x75, 0x6e, 0x4a, 0x53,
	0x5f, 0x52, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6a, 0x73, 0x53, 0x74, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x73, 0x53, 0x74, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x73,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x69, 0x73, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x28, 0x0a, 0x0f,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x44, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x49, 0x44, 0x22, 0x5e, 0x0a, 0x1f, 0x47, 0x6c, 0x5f, 0x47, 0x6c, 0x6f,
	0x62, 0x61, 0x6c, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x49, 0x6e, 0x66, 0x6f, 0x5f, 0x52, 0x65, 0x73, 0x12, 0x3b, 0x0a, 0x10, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x50, 0x42, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x10, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x6e,
	0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x53, 0x0a, 0x1c, 0x47, 0x6c, 0x5f, 0x47, 0x6c, 0x6f,
	0x62, 0x61, 0x6c, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x6e,
	0x66, 0x6f, 0x5f, 0x52, 0x65, 0x73, 0x12, 0x33, 0x0a, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x50,
	0x42, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xea, 0x01, 0x0a, 0x0f,
	0x47, 0x4c, 0x5f, 0x43, 0x44, 0x4b, 0x65, 0x79, 0x55, 0x73, 0x65, 0x5f, 0x52, 0x65, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x2d, 0x0a, 0x08, 0x69,
	0x74, 0x65, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x50, 0x42, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x55, 0x73, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x55, 0x73, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x74, 0x0a, 0x1a, 0x47, 0x6c, 0x5f, 0x47,
	0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x43, 0x68, 0x61, 0x74, 0x42, 0x61, 0x6e, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x5f, 0x52, 0x65, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x55, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x55, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x74, 0x42, 0x61, 0x6e, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x74, 0x42,
	0x61, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x60,
	0x0a, 0x14, 0x47, 0x6c, 0x5f, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x57, 0x65, 0x61, 0x74, 0x68,
	0x65, 0x72, 0x5f, 0x52, 0x65, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x57, 0x65, 0x61,
	0x74, 0x68, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x75,
	0x72, 0x57, 0x65, 0x61, 0x74, 0x68, 0x65, 0x72, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x6e, 0x65,
	0x78, 0x74, 0x57, 0x65, 0x61, 0x74, 0x68, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x57, 0x65, 0x61, 0x74, 0x68, 0x65, 0x72, 0x49, 0x64,
	0x22, 0xde, 0x02, 0x0a, 0x1b, 0x47, 0x4c, 0x5f, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x42, 0x72,
	0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x52, 0x65, 0x73,
	0x12, 0x2d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19,
	0x2e, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x1c, 0x0a, 0x09, 0x69, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x31, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x69, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x31, 0x12, 0x1c, 0x0a,
	0x09, 0x69, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x32, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x69, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x32, 0x12, 0x1c, 0x0a, 0x09, 0x69,
	0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x33, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x69, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x33, 0x12, 0x1e, 0x0a, 0x0a, 0x6c, 0x6f, 0x6e,
	0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x31, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6c,
	0x6f, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x31, 0x12, 0x1e, 0x0a, 0x0a, 0x6c, 0x6f, 0x6e,
	0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x32, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6c,
	0x6f, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x32, 0x12, 0x1e, 0x0a, 0x0a, 0x6c, 0x6f, 0x6e,
	0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x33, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6c,
	0x6f, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x33, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x31, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x31, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x32, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x32, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x33, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x33, 0x42, 0x25, 0x5a, 0x23, 0x6c, 0x69, 0x74, 0x65, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x2f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x63, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_GLProtocol_proto_rawDescOnce sync.Once
	file_GLProtocol_proto_rawDescData = file_GLProtocol_proto_rawDesc
)

func file_GLProtocol_proto_rawDescGZIP() []byte {
	file_GLProtocol_proto_rawDescOnce.Do(func() {
		file_GLProtocol_proto_rawDescData = protoimpl.X.CompressGZIP(file_GLProtocol_proto_rawDescData)
	})
	return file_GLProtocol_proto_rawDescData
}

var file_GLProtocol_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_GLProtocol_proto_goTypes = []interface{}{
	(*GL_Test_Res)(nil),                     // 0: GamePackage.GL_Test_Res
	(*Gl_GlobalMailNeedReload_Res)(nil),     // 1: GamePackage.Gl_GlobalMailNeedReload_Res
	(*Gl_NoticeSyncFriend_Res)(nil),         // 2: GamePackage.Gl_NoticeSyncFriend_Res
	(*Gl_GlobalKickPlayer_Res)(nil),         // 3: GamePackage.Gl_GlobalKickPlayer_Res
	(*GL_GlobalToLobbyRunJS_Res)(nil),       // 4: GamePackage.GL_GlobalToLobbyRunJS_Res
	(*Gl_GlobalGetAllActivityInfo_Res)(nil), // 5: GamePackage.Gl_GlobalGetAllActivityInfo_Res
	(*Gl_GlobalGetActivityInfo_Res)(nil),    // 6: GamePackage.Gl_GlobalGetActivityInfo_Res
	(*GL_CDKeyUse_Res)(nil),                 // 7: GamePackage.GL_CDKeyUse_Res
	(*Gl_GlobalChatBanPlayer_Res)(nil),      // 8: GamePackage.Gl_GlobalChatBanPlayer_Res
	(*Gl_GlobalWeather_Res)(nil),            // 9: GamePackage.Gl_GlobalWeather_Res
	(*GL_GlobalBroadcastEvent_Res)(nil),     // 10: GamePackage.GL_GlobalBroadcastEvent_Res
	(*public.PBCommonLongKeyValue)(nil),     // 11: PBCommonLongKeyValue
	(*public.PBActivityInfo)(nil),           // 12: PBActivityInfo
	(*public.PBCommonKeyValue)(nil),         // 13: PBCommonKeyValue
	(public.GlobalBroadcastEventType)(0),    // 14: GlobalBroadcastEventType
}
var file_GLProtocol_proto_depIdxs = []int32{
	11, // 0: GamePackage.Gl_NoticeSyncFriend_Res.playerUidInfo:type_name -> PBCommonLongKeyValue
	12, // 1: GamePackage.Gl_GlobalGetAllActivityInfo_Res.activityInfoList:type_name -> PBActivityInfo
	12, // 2: GamePackage.Gl_GlobalGetActivityInfo_Res.activityInfo:type_name -> PBActivityInfo
	13, // 3: GamePackage.GL_CDKeyUse_Res.itemList:type_name -> PBCommonKeyValue
	14, // 4: GamePackage.GL_GlobalBroadcastEvent_Res.type:type_name -> GlobalBroadcastEventType
	5,  // [5:5] is the sub-list for method output_type
	5,  // [5:5] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_GLProtocol_proto_init() }
func file_GLProtocol_proto_init() {
	if File_GLProtocol_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_GLProtocol_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GL_Test_Res); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GLProtocol_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Gl_GlobalMailNeedReload_Res); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GLProtocol_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Gl_NoticeSyncFriend_Res); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GLProtocol_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Gl_GlobalKickPlayer_Res); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GLProtocol_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GL_GlobalToLobbyRunJS_Res); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GLProtocol_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Gl_GlobalGetAllActivityInfo_Res); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GLProtocol_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Gl_GlobalGetActivityInfo_Res); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GLProtocol_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GL_CDKeyUse_Res); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GLProtocol_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Gl_GlobalChatBanPlayer_Res); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GLProtocol_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Gl_GlobalWeather_Res); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_GLProtocol_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GL_GlobalBroadcastEvent_Res); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_GLProtocol_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_GLProtocol_proto_goTypes,
		DependencyIndexes: file_GLProtocol_proto_depIdxs,
		MessageInfos:      file_GLProtocol_proto_msgTypes,
	}.Build()
	File_GLProtocol_proto = out.File
	file_GLProtocol_proto_rawDesc = nil
	file_GLProtocol_proto_goTypes = nil
	file_GLProtocol_proto_depIdxs = nil
}
