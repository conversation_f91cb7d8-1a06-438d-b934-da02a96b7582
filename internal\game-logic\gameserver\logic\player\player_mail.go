package player

import (
	"liteframe/internal/common/error_code"
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/common/protos/dbstruct"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/common/rpc_def"
	"liteframe/internal/common/table"
	"math/rand/v2"
	"time"

	"liteframe/pkg/log"
)

// Mail 角色邮件模块
type Mail struct {
	player       *Player
	MailDataList map[int64]*dbstruct.MailData
}

func NewMail(p *Player) *Mail {
	return &Mail{
		player:       p,
		MailDataList: make(map[int64]*dbstruct.MailData),
	}
}

// InitDB 初始化模块数据
func (m *Mail) InitDB(db *dbstruct.UserDB) {
	if db.Game == nil {
		db.Game = &dbstruct.GameDB{}
	}
	if db.Game.Mail == nil {
		db.Game.Mail = &dbstruct.Mail{
			Mails: make([]*dbstruct.MailData, 0),
		}
	}
	for _, mailInfo := range db.Game.Mail.Mails {
		m.MailDataList[mailInfo.Id] = mailInfo
	}
	m.HandleGlobalActivityMails()
	m.HandleGlobalSystemMails()
	log.Info("Mail InitDB")
}

// SaveDB 存储模块数据
func (m *Mail) SaveDB() {
	m.player.GetUserDB().Game.Mail.Mails = make([]*dbstruct.MailData, 0)
	for _, mailInfo := range m.MailDataList {
		m.player.GetUserDB().Game.Mail.Mails = append(m.player.GetUserDB().Game.Mail.Mails, mailInfo)
	}
	log.Info("Mail SaveDB")
}

// OnCrossDay 处理跨天逻辑
func (m *Mail) OnCrossDay(natural bool, nowUnix int64) {
	// 处理跨天逻辑，如清理过期道具等
	// TODO: 实现跨天逻辑
}

// SendMail 发送邮件
func (m *Mail) SendMail(mailType public.MailType, title string, content string, createTime int64, expireTime int64, senderGid int64, senderName string, receiverGid int64, receiver string, itemList map[int32]int32, mailReason public.MailReasonType, syncClient bool) bool {
	mailData := &dbstruct.MailData{}
	mailGid := rand.Int64()
	mailData.Id = mailGid
	mailData.Type = int32(mailType)
	mailData.SubType = 0
	mailData.TitleStr = title
	mailData.ContentStr = content
	mailData.Status = int32(public.MailStateType_MailStateType_UnRead)
	mailData.CreateTime = createTime
	mailData.ExpireTime = expireTime
	mailData.OfflineFlag = 1
	mailData.SenderId = senderGid
	mailData.SenderName = senderName
	mailData.ReceiverId = receiverGid
	mailData.Reason = int32(mailReason)
	mailData.Level = 0
	mailData.PlatId = 0
	mailData.SenderHeadIcon = ""
	for itemId, itemNum := range itemList {
		mailData.GoodsItems = append(mailData.GoodsItems, &dbstruct.CommonKeyValue{
			Key:   itemId,
			Value: int32(itemNum),
		})
	}
	m.MailDataList[mailGid] = mailData
	m.UpdateExcessMail()
	m.SaveDB()
	return true
}

// GetMailData 获取一封邮件
func (m *Mail) GetMailData(mailId int64) *dbstruct.MailData {
	if mailData, exists := m.MailDataList[mailId]; exists {
		return mailData
	}
	return nil
}

// UpdatePastDueMail 更新过期邮件
func (m *Mail) UpdatePastDueMail() {
	nowTime := time.Now().Unix()
	needRemoveMailMap := make(map[int64]*dbstruct.MailData)
	for mailId, mailData := range m.MailDataList {
		if mailData.GetCreateTime()+mailData.GetExpireTime() < nowTime {
			needRemoveMailMap[mailId] = mailData
		}
	}
	if len(needRemoveMailMap) > 0 {
		for mailID := range needRemoveMailMap {
			delete(m.MailDataList, mailID) // 从邮件列表中移除
		}
	}
	m.SaveDB()
}

// UpdateExcessMail 更新过量邮件
func (m *Mail) UpdateExcessMail() {
	m.UpdatePastDueMail()
	if int32(len(m.MailDataList)) > table.GetMailMaxNum() {
		needRemoveMailMap := make(map[int64]*dbstruct.MailData)
		needRemoveMailNum := int32(len(m.MailDataList)) - table.GetMailMaxNum()
		for itemId, mailData := range m.MailDataList {
			needRemoveMailMap[itemId] = mailData
			if int32(len(needRemoveMailMap)) >= needRemoveMailNum {
				break
			}
		}
		if len(needRemoveMailMap) > 0 {
			for mailID := range needRemoveMailMap {
				delete(m.MailDataList, mailID) // 从邮件列表中移除
			}
		}
	}
	m.SaveDB()
}

// FillUnitMailData 填充单位邮件数据
func (m *Mail) FillUnitMailData(mailId int64) *public.PBMail {
	mailData := m.GetMailData(mailId)
	if mailData == nil {
		return nil
	}
	protoMail := &public.PBMail{
		Id:          mailData.Id,
		Type:        public.MailType(mailData.Type),
		Title:       mailData.TitleStr,
		Content:     mailData.ContentStr,
		Status:      public.MailStateType(mailData.Status),
		Reason:      public.MailReasonType(mailData.Reason),
		CreateTime:  mailData.CreateTime,
		ExpireTime:  mailData.ExpireTime,
		SenderGuild: mailData.SenderId,
		Sender:      mailData.SenderName,
		Recerguild:  mailData.ReceiverId,
		Recer:       mailData.SenderName,
	}
	for _, good := range mailData.GoodsItems {
		protoMail.Goods = append(protoMail.Goods, &public.PBCommonKeyValue{
			Key:   good.Key,   // 道具的 Key
			Value: good.Value, // 道具的 Value
		})
	}
	return protoMail
}

// GmGetFirstMail Gm专用：获取第一封邮件
func (m *Mail) GmGetFirstMail() *dbstruct.MailData {
	if len(m.MailDataList) > 0 {
		for _, mailInfo := range m.MailDataList {
			return mailInfo
		}
	}
	return nil
}

// HandleGlobalActivityMails 处理全局活动邮件
func (m *Mail) HandleGlobalActivityMails() {

}

// HandleGlobalSystemMails 处理全局活动邮件
func (m *Mail) HandleGlobalSystemMails() {

}

// SyncMailAllList 同步全部邮件列表
// @Export
func (m *Mail) SyncMailAllList() {
	if !m.player.IsOnline() {
		return
	}
	m.UpdatePastDueMail()
	msg := &cs.LCMailListRes{
		Mails: []*public.PBMail{},
	}
	for _, mailData := range m.MailDataList {
		protoMail := m.FillUnitMailData(mailData.Id)
		msg.Mails = append(msg.Mails, protoMail)
	}
	m.player.SendToClient(rpc_def.LCMailListRes, msg, false)
}

// ReadMail 阅读一封邮件
// @Export
func (m *Mail) ReadMail(mailId int64) error_code.Code {
	mailData := m.GetMailData(mailId)
	if mailData == nil {
		return error_code.ERROR_PARAMS
	}
	// 前置检查
	nowTime := time.Now().Unix()
	// 1.是否过期
	if mailData.GetCreateTime()+mailData.GetExpireTime() < nowTime {
		return error_code.ERROR_PARAMS
	}
	// 2.邮件状态是否为未读
	if mailData.Status != int32(public.MailStateType_MailStateType_UnRead) {
		return error_code.ERROR_PARAMS
	}
	// 更新状态
	mailData.Status = int32(public.MailStateType_MailStateType_Read)
	m.SaveDB()
	// 同步客户端
	msg := &cs.LCReadMailRes{
		MailId: mailId,
	}
	m.player.SendToClient(rpc_def.LCReadMailRes, msg, false)
	return error_code.ERROR_OK
}

// ReceiveMail 领取一封邮件
// @Export
func (m *Mail) ReceiveMail(mailId int64) error_code.Code {
	mailData := m.GetMailData(mailId)
	if mailData == nil {
		return error_code.ERROR_PARAMS
	}
	// 前置检查
	nowTime := time.Now().Unix()
	// 1.是否过期
	if mailData.GetCreateTime()+mailData.GetExpireTime() < nowTime {
		return error_code.ERROR_PARAMS
	}
	// 2.是否已经领过奖励
	if mailData.Status == int32(public.MailStateType_MailStateType_GotAward) {
		return error_code.ERROR_PARAMS
	}
	// 更新状态
	mailData.Status = int32(public.MailStateType_MailStateType_GotAward)
	// 领取奖励
	dropItemList := make(map[int32]int32)
	for _, itemInfo := range mailData.GoodsItems {
		m.player.AddItem(itemInfo.Key, itemInfo.Value, 1)
		dropItemList[itemInfo.Key] = itemInfo.Value
	}
	if len(dropItemList) > 0 {
		m.player.SendCommonDropItemList(dropItemList, 4)
	}
	m.SaveDB()
	// 同步客户端
	msg := &cs.LCReceiveMailRes{
		MailId:    mailId,
		MailState: public.MailStateType_MailStateType_GotAward,
	}
	m.player.SendToClient(rpc_def.LCReceiveMailRes, msg, false)
	m.SaveDB()
	return error_code.ERROR_OK
}

// ReceiveAllMail 领取所有邮件
// @Export
func (m *Mail) ReceiveAllMail() error_code.Code {
	// 前置检查
	nowTime := time.Now().Unix()
	mailList := make([]int64, 0)
	dropItemList := make(map[int32]int32)
	for _, mailData := range m.MailDataList {
		// 1.是否过期
		if mailData.GetCreateTime()+mailData.GetExpireTime() < nowTime {
			continue
		}
		// 2.是否已经领过奖励
		if mailData.Status == int32(public.MailStateType_MailStateType_GotAward) {
			continue
		}
		// 3.是否有奖励
		if len(mailData.GoodsItems) > 0 {
			mailData.Status = int32(public.MailStateType_MailStateType_GotAward)
			for _, itemInfo := range mailData.GoodsItems {
				m.player.AddItem(itemInfo.Key, itemInfo.Value, 1)
				if currentNum, exists := dropItemList[itemInfo.Key]; exists {
					currentNum += itemInfo.Value
					dropItemList[itemInfo.Key] = currentNum
				} else {
					dropItemList[itemInfo.Key] = itemInfo.Value
				}
			}
			mailList = append(mailList, mailData.Id)
		}
	}
	if len(dropItemList) > 0 {
		m.player.SendCommonDropItemList(dropItemList, 4)
	}
	m.SaveDB()
	// 同步客户端
	msg := &cs.LCReceiveAllMailRes{
		Mails: []*public.PBMail{},
	}
	for _, mailData := range m.MailDataList {
		protoMail := m.FillUnitMailData(mailData.Id)
		msg.Mails = append(msg.Mails, protoMail)
	}
	m.player.SendToClient(rpc_def.LCReceiveAllMailRes, msg, false)
	m.SaveDB()
	return error_code.ERROR_OK
}

// DelMail 删除一封邮件
// @Export
func (m *Mail) DelMail(mailId int64) error_code.Code {
	// 前置条件判断
	mailData := m.GetMailData(mailId)
	if mailData == nil {
		return error_code.ERROR_PARAMS
	}
	nowTime := time.Now().Unix()
	if mailData.GetCreateTime()+mailData.GetExpireTime() < nowTime {
		return error_code.ERROR_PARAMS
	}
	// 更新数据
	delete(m.MailDataList, mailId)
	m.SaveDB()
	// 同步客户端
	msg := &cs.LCDelMailRes{
		MailId: mailId,
	}
	m.player.SendToClient(rpc_def.LCDelMailRes, msg, false)
	return error_code.ERROR_OK
}

// DelAllReadMail 删除所有已读邮件
// @Export
func (m *Mail) DelAllReadMail() error_code.Code {
	mailList := make([]int64, 0)
	for _, mailData := range m.MailDataList {
		if len(mailData.GoodsItems) == 0 && mailData.Status == int32(public.MailStateType_MailStateType_Read) {
			mailList = append(mailList, mailData.Id)
		} else if len(mailData.GoodsItems) > 0 && mailData.Status == int32(public.MailStateType_MailStateType_GotAward) {
			mailList = append(mailList, mailData.Id)
		}
	}
	for _, mailId := range mailList {
		delete(m.MailDataList, mailId)
	}
	m.SaveDB()
	// 同步客户端
	msg := &cs.LCDelAllReadMailRes{
		Mails: []*public.PBMail{},
	}
	for _, mailData := range m.MailDataList {
		protoMail := m.FillUnitMailData(mailData.Id)
		msg.Mails = append(msg.Mails, protoMail)
	}
	m.player.SendToClient(rpc_def.LCDelAllReadMailRes, msg, false)
	return error_code.ERROR_OK
}

// MailQuestionAward 问卷奖励邮件
// @Export
func (m *Mail) MailQuestionAward() error_code.Code {
	// 同步客户端
	msg := &cs.LCSyncQuestList{}
	m.player.SendToClient(rpc_def.LCSyncQuestList, msg, false)
	return error_code.ERROR_OK
}
