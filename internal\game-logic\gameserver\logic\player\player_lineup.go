package player

import (
	"liteframe/internal/common/error_code"
	"liteframe/internal/common/protos/dbstruct"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/common/table"
	"liteframe/internal/common/table/table_data"
	"liteframe/pkg/log"
)

// Lineup 阵容管理模块
type Lineup struct {
	player *Player
	db     *dbstruct.Lineup
}

// NewLineup 创建阵容模块
func NewLineup(p *Player) *Lineup {
	return &Lineup{
		player: p,
		db: &dbstruct.Lineup{
			UnlockedIds: make([]int32, 0),
			CurrentId:   1,
			LineupList:  make([]*public.PBLineupInfo, 0),
		},
	}
}

// InitDB 初始化DB数据
func (t *Lineup) InitDB(db *dbstruct.UserDB) {
	if db.Game == nil {
		db.Game = &dbstruct.GameDB{}
	}
	if db.Game.Lineup == nil {
		db.Game.Lineup = &dbstruct.Lineup{
			UnlockedIds: make([]int32, 0),
			CurrentId:   1,
			LineupList:  make([]*public.PBLineupInfo, 0),
		}
	}
	t.db = db.Game.Lineup

	// 验证并清理阵容中的无效英雄（在英雄模块初始化之后）
	if len(t.db.LineupList) > 0 {
		t.ValidateAndCleanLineups()
	}

	log.Info("Lineup InitDB")
}

// OnCrossDay 跨天处理
func (t *Lineup) OnCrossDay(natural bool, nowUnix int64) {}

// LineupList 获取阵容列表并返回给客户端
func (t *Lineup) LineupList() (error_code.Code, []*public.PBLineupInfo, []int32, int32) {
	// 如果是首次访问，需要初始化阵容数据
	if len(t.db.UnlockedIds) == 0 {
		if err := t.initPlayerLineup(); err != error_code.ERROR_OK {
			return err, nil, nil, 0
		}
	}

	log.Info("Get lineup list", log.Kv("player_id", t.player.Uid()))

	// 返回成功和阵容数据
	return error_code.ERROR_OK, t.db.LineupList, t.db.UnlockedIds, t.db.CurrentId
}

// initPlayerLineup 初始化玩家阵容数据
func (t *Lineup) initPlayerLineup() error_code.Code {
	log.Info("Initializing player lineup", log.Kv("player_id", t.player.Uid()))

	// 获取初始可解锁的槽位 ID
	initialUnlockedIds := make([]int32, 0)
	firstLineupHeroes := make([]int32, 0)

	// 查询表格，获取初始解锁的槽位（CostItem为空的）
	table.GetTable().TableTeamHole.Foreach(func(tbData *table_data.TableTeamHole) bool {
		if len(tbData.CostItem) == 0 || (len(tbData.CostItem) >= 2 && tbData.CostItem[0] == 0) {
			initialUnlockedIds = append(initialUnlockedIds, tbData.ID)
		}
		return true
	})

	// 如果没有初始槽位，添加默认的ID=1的槽位
	if len(initialUnlockedIds) == 0 {
		initialUnlockedIds = append(initialUnlockedIds, 1)
	}

	// 设置已解锁的槽位ID
	t.db.UnlockedIds = initialUnlockedIds

	// 设置当前激活的槽位为第一个槽位
	t.db.CurrentId = initialUnlockedIds[0]

	// 从配置中获取阵容中英雄的最少数量
	maxHeroesInLineup := int(table.GetHeroLineUpNum())
	if maxHeroesInLineup <= 0 {
		maxHeroesInLineup = 8 // 默认值
	}

	// 优先使用配置中的初始英雄ID作为初始阵容
	initialHeroIds := table.GetHeroLineUpId()
	if len(initialHeroIds) > 0 {
		// 使用配置中的初始英雄ID，但不超过maxHeroesInLineup数量
		count := len(initialHeroIds)
		if count > maxHeroesInLineup {
			count = maxHeroesInLineup
		}

		for i := 0; i < count; i++ {
			// 检查英雄ID是否在hero表中存在
			heroData := table.GetTable().TableHero.GetById(initialHeroIds[i])
			if heroData != nil {
				firstLineupHeroes = append(firstLineupHeroes, initialHeroIds[i])
			} else {
				log.Error("Invalid hero ID in initial lineup config",
					log.Kv("player_id", t.player.Uid()),
					log.Kv("hero_id", initialHeroIds[i]))
			}
		}

		log.Info("Initial lineup heroes set from config",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("hero_count", len(firstLineupHeroes)),
			log.Kv("hero_ids", firstLineupHeroes))
	} else {
		// 如果配置中没有初始英雄ID，则从Hero模块的db中获取英雄列表
		heroList := t.player.Hero().db.HeroList
		if len(heroList) > 0 {
			// 取前 maxHeroesInLineup 个英雄，如果不足 maxHeroesInLineup 个，则取全部
			count := len(heroList)
			if count > maxHeroesInLineup {
				count = maxHeroesInLineup
			}

			for i := 0; i < count; i++ {
				firstLineupHeroes = append(firstLineupHeroes, heroList[i].HeroId)
			}

			log.Info("Initial lineup heroes set from hero list",
				log.Kv("player_id", t.player.Uid()),
				log.Kv("hero_count", len(firstLineupHeroes)),
				log.Kv("hero_ids", firstLineupHeroes))
		}
	}

	// 创建初始阵容，每个解锁的槽位一个阵容
	for _, id := range initialUnlockedIds {
		lineupName := "自定义" + string(rune(id))
		lineup := &public.PBLineupInfo{
			Id:      id,
			Name:    lineupName,
			HeroIds: make([]int32, 0),
		}

		// 为第一个阵容填充初始英雄
		if id == initialUnlockedIds[0] {
			lineup.HeroIds = firstLineupHeroes
		} else {
			// 后续阵容复制第一个阵容的英雄列表
			lineup.HeroIds = append(lineup.HeroIds, firstLineupHeroes...)
		}

		t.db.LineupList = append(t.db.LineupList, lineup)
	}

	log.Info("Player lineup initialized",
		log.Kv("player_id", t.player.Uid()),
		log.Kv("unlocked_ids", t.db.UnlockedIds),
		log.Kv("current_id", t.db.CurrentId))

	return error_code.ERROR_OK
}

// LineupSwitch 切换当前使用的阵容
func (t *Lineup) LineupSwitch(id int32) (error_code.Code, int32) {
	// 检查阵容ID是否已解锁
	isUnlocked := false
	for _, unlockId := range t.db.UnlockedIds {
		if unlockId == id {
			isUnlocked = true
			break
		}
	}

	if !isUnlocked {
		log.Error("Lineup ID not unlocked",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("lineup_id", id))
		return error_code.ERROR_LINEUP_SLOT_NOT_FOUND, 0
	}

	// 更新当前阵容ID
	t.db.CurrentId = id
	log.Info("Lineup switched",
		log.Kv("player_id", t.player.Uid()),
		log.Kv("new_lineup_id", id))

	return error_code.ERROR_OK, id
}

// LineupSet 设置阵容中的英雄
func (t *Lineup) LineupSet(heroId int32, replaceHeroId int32) (error_code.Code, *public.PBLineupInfo) {
	// 获取当前激活的阵容
	currentLineup := t.getLineupById(t.db.CurrentId)
	if currentLineup == nil {
		log.Error("Current lineup not found",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("lineup_id", t.db.CurrentId))
		return error_code.ERROR_LINEUP_NOT_FOUND, nil
	}

	// 检查英雄所有权（玩家是否拥有该英雄）
	if heroId != 0 { // heroId=0表示移除英雄
		hero := t.player.Hero().GetHeroById(heroId)
		if hero == nil {
			log.Error("Hero not found",
				log.Kv("player_id", t.player.Uid()),
				log.Kv("hero_id", heroId))
			return error_code.ERROR_HERO_NOT_FOUND, nil
		}

		// 检查英雄是否已经在阵容中
		for _, existingHeroId := range currentLineup.HeroIds {
			if existingHeroId == heroId {
				log.Error("Hero already in lineup",
					log.Kv("player_id", t.player.Uid()),
					log.Kv("hero_id", heroId))
				return error_code.ERROR_LINEUP_HERO_DUPLICATE, nil
			}
		}
	}

	// 如果replaceHeroId=0，表示添加到空位
	if replaceHeroId == 0 {
		// 添加英雄到阵容
		currentLineup.HeroIds = append(currentLineup.HeroIds, heroId)
	} else {
		// 查找替换位置
		found := false
		for i, existingHeroId := range currentLineup.HeroIds {
			if existingHeroId == replaceHeroId {
				// 替换英雄
				if heroId == 0 {
					// 移除英雄
					currentLineup.HeroIds = append(currentLineup.HeroIds[:i], currentLineup.HeroIds[i+1:]...)
				} else {
					// 替换英雄
					currentLineup.HeroIds[i] = heroId
				}
				found = true
				break
			}
		}

		// 如果要替换的英雄不在阵容中
		if !found {
			log.Error("Hero to replace not found in lineup",
				log.Kv("player_id", t.player.Uid()),
				log.Kv("replace_hero_id", replaceHeroId))
			return error_code.ERROR_LINEUP_HERO_NOT_FOUND, nil
		}
	}

	log.Info("Lineup updated",
		log.Kv("player_id", t.player.Uid()),
		log.Kv("lineup_id", t.db.CurrentId),
		log.Kv("hero_id", heroId),
		log.Kv("replace_hero_id", replaceHeroId))

	return error_code.ERROR_OK, currentLineup
}

// LineupRename 重命名阵容
func (t *Lineup) LineupRename(id int32, newName string) (error_code.Code, string) {
	// 检查阵容ID是否已解锁
	isUnlocked := false
	for _, unlockId := range t.db.UnlockedIds {
		if unlockId == id {
			isUnlocked = true
			break
		}
	}

	if !isUnlocked {
		log.Error("Lineup ID not unlocked",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("lineup_id", id))
		return error_code.ERROR_LINEUP_SLOT_NOT_FOUND, ""
	}

	// 获取对应ID的阵容
	lineup := t.getLineupById(id)
	if lineup == nil {
		log.Error("Lineup not found",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("lineup_id", id))
		return error_code.ERROR_LINEUP_NOT_FOUND, ""
	}

	// 修改阵容名称
	lineup.Name = newName
	log.Info("Lineup renamed",
		log.Kv("player_id", t.player.Uid()),
		log.Kv("lineup_id", id),
		log.Kv("new_name", newName))

	return error_code.ERROR_OK, newName
}

// LineupUnlockSlot 解锁新的阵容槽位
func (t *Lineup) LineupUnlockSlot(id int32) (error_code.Code, []int32) {
	// 检查该ID是否已解锁
	for _, unlockId := range t.db.UnlockedIds {
		if unlockId == id {
			log.Error("Lineup slot already unlocked",
				log.Kv("player_id", t.player.Uid()),
				log.Kv("slot_id", id))
			return error_code.ERROR_PARAMS, nil
		}
	}

	// 查询表格，获取解锁槽位所需资源
	var tbData *table_data.TableTeamHole
	table.GetTable().TableTeamHole.Foreach(func(info *table_data.TableTeamHole) bool {
		if info.ID == id {
			tbData = info
			return false
		}
		return true
	})

	if tbData == nil {
		log.Error("Lineup slot config not found",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("slot_id", id))
		return error_code.ERROR_PARAMS, nil
	}

	// 检查资源是否足够
	if len(tbData.CostItem) >= 2 && tbData.CostItem[0] > 0 && tbData.CostItem[1] > 0 {
		// 获取资源类型和数量
		itemType := tbData.CostItem[0]
		itemCount := tbData.CostItem[1]

		// 检查资源是否足够
		if !t.player.Money().IsCanDelMoney(itemType, itemCount) {
			log.Error("Not enough resources to unlock lineup slot",
				log.Kv("player_id", t.player.Uid()),
				log.Kv("slot_id", id),
				log.Kv("cost_item", itemType),
				log.Kv("cost_num", itemCount))
			return error_code.ERROR_NO_MONEY, nil
		}

		// 扣除资源
		if !t.player.Money().DelMoney(itemType, itemCount, 4) {
			log.Error("Fail to deduct resources",
				log.Kv("player_id", t.player.Uid()),
				log.Kv("cost_item", itemType),
				log.Kv("cost_num", itemCount))
			return error_code.ERROR_OPT_FAILE, nil
		}
	}

	// 解锁槽位
	t.db.UnlockedIds = append(t.db.UnlockedIds, id)

	// 创建新的阵容
	newLineup := &public.PBLineupInfo{
		Id:      id,
		Name:    "自定义" + string(rune(id)),
		HeroIds: make([]int32, 0),
	}

	// 如果有其他阵容，复制第一个阵容的英雄列表
	if len(t.db.LineupList) > 0 {
		firstLineup := t.db.LineupList[0]
		newLineup.HeroIds = append(newLineup.HeroIds, firstLineup.HeroIds...)
	}

	// 添加到阵容列表
	t.db.LineupList = append(t.db.LineupList, newLineup)

	log.Info("Lineup slot unlocked",
		log.Kv("player_id", t.player.Uid()),
		log.Kv("slot_id", id))

	return error_code.ERROR_OK, t.db.UnlockedIds
}

// getLineupById 根据ID获取阵容
func (t *Lineup) getLineupById(id int32) *public.PBLineupInfo {
	for _, lineup := range t.db.LineupList {
		if lineup.Id == id {
			return lineup
		}
	}
	return nil
}

// getCurrentLineup 获取当前激活的阵容
func (t *Lineup) getCurrentLineup() *public.PBLineupInfo {
	return t.getLineupById(t.db.CurrentId)
}

// ValidateAndCleanLineups 验证并清理阵容中的无效英雄
func (t *Lineup) ValidateAndCleanLineups() {
	if t.db == nil || len(t.db.LineupList) == 0 {
		return
	}

	cleanedCount := 0
	for _, lineup := range t.db.LineupList {
		originalCount := len(lineup.HeroIds)
		validHeroIds := make([]int32, 0, originalCount)

		// 检查每个英雄是否仍然存在且不重复
		seenHeroes := make(map[int32]bool)
		for _, heroId := range lineup.HeroIds {
			// 检查英雄是否存在
			hero := t.player.Hero().GetHeroById(heroId)
			if hero != nil && !seenHeroes[heroId] {
				validHeroIds = append(validHeroIds, heroId)
				seenHeroes[heroId] = true
			} else if hero == nil {
				log.Info("Removing invalid hero from lineup",
					log.Kv("player_id", t.player.Uid()),
					log.Kv("lineup_id", lineup.Id),
					log.Kv("hero_id", heroId))
				cleanedCount++
			} else {
				log.Info("Removing duplicate hero from lineup",
					log.Kv("player_id", t.player.Uid()),
					log.Kv("lineup_id", lineup.Id),
					log.Kv("hero_id", heroId))
				cleanedCount++
			}
		}

		lineup.HeroIds = validHeroIds
	}

	if cleanedCount > 0 {
		log.Info("Lineup validation completed",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("cleaned_count", cleanedCount))
	}
}

// TODO: 一键替换阵容功能
// 未来可能需要增加一个设置整个阵容的方法，接收完整的hero_ids列表
// 方法签名可能为：LineupSetWhole(id int32, heroIds []int32) error_code.Code
