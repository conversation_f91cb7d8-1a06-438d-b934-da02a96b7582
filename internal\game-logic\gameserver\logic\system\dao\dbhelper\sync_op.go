package dbhelper

import (
	"encoding/json"
	"fmt"
	"strconv"

	"liteframe/internal/common/constant/def"
	"liteframe/internal/common/error_code"
	"liteframe/internal/common/protos/dbstruct"
	"liteframe/internal/common/protos/g2m"
	"liteframe/internal/game-logic/gameserver/game_def/actor_def"
	"liteframe/internal/game-logic/gameserver/gameutil/dbutil"
	"liteframe/pkg/actor"
	"liteframe/pkg/log"

	"github.com/pkg/errors"

	"github.com/gomodule/redigo/redis"
	"google.golang.org/protobuf/proto"
)

// db 操作的同步接口 慎用！！！ 不在公共goroutine里操作

// LoadUserSync 同步加载玩家数据 // 只能在player actor里调用
func LoadUserSync(pid actor.PID, uid uint64) (user *dbstruct.UserDB, err error) {
	d := &g2m.G2M_LoadUser{
		Uid: uid,
	}

	msg := &actor.Message{
		Id:   uint32(def.MsgId_Mongo_LoadUser),
		Uid:  uid,
		Data: d,
	}

	resp := actor.SyncRequest(pid, actor_def.DBSystemPID, msg)
	if resp.Err != nil {
		return nil, resp.Err
	}
	ret := resp.Data.(*g2m.M2G_LoadUser)
	return ret.User, nil
}

func LoadOrCreateUserSync(pid actor.PID, account string, serverId, uid uint64) (*dbstruct.UserDB, bool, error) {
	resp := actor.SyncRequest(pid, actor_def.DBSystemPID, &actor.Message{
		Id:  uint32(def.MsgId_Mongo_LoadOrCreateUser),
		Uid: 0,
		Data: &g2m.G2M_LoadOrCreateUser{
			Account:  account,
			ServerId: serverId,
			Uid:      uid,
		},
	})
	if resp.Data == nil {
		return nil, false, errors.Wrap(resp.Err, fmt.Sprintf("account %s load data failed", account))
	}
	msg := resp.Data.(*g2m.M2G_LoadOrCreateUser)
	if resp.Err != nil {
		return nil, false, errors.Wrap(resp.Err, fmt.Sprintf("account %s load failed, code %d", account, msg.Code))
	}

	if msg.Code != uint32(error_code.ERROR_OK) {
		return nil, false, fmt.Errorf("load account %s failed, code %d", account, msg.Code)
	}

	return msg.User, msg.IsNew, nil
}

// SaveUser 保存玩家数据
func SaveUserSync(pid actor.PID, uid uint64, user *dbstruct.UserDB) error {
	base, game, _ := dbutil.Marshal(user)
	tmp, _ := dbutil.UnmarshalMerge(base, game)
	d := &g2m.G2M_SaveUser{
		Data: [][]byte{base, game},
		User: tmp,
	}
	msg := &actor.Message{
		Id:   uint32(def.MsgId_Mongo_SaveUser),
		Uid:  uid,
		Data: d,
	}

	resp := actor.SyncRequest(pid, actor_def.DBSystemPID, msg)
	return resp.Err
}

/*
// LoadDataSync 同步加载数据，只能服务器启动时调用！！！  cb 的参数out 必须是引用类型!!! 否则panic
func LoadDataSync[V any](pid actor.PID, key string) (out V, err error) {
	msg := actor.Message{
		Id:   uint32(def.MsgId_Mongo_LoadData),
		Data: &g2m.G2M_LoadData{Key: key},
	}

	respMsg := actor.SyncRequest(pid, gamedef.DBSystemPID, msg)

	var ev V

	if respMsg.Err != nil {
		return ev, respMsg.Err
	}

	d := respMsg.Data.(*g2m.M2G_LoadData)
	if len(d.Data) == 0 {
		return ev, fmt.Errorf("pid %v sync load key %s data empty", pid, key)
	}

	rt := reflect.ValueOf(ev).Type()
	rv := reflect.New(rt.Elem()).Interface()

	if pb, ok := rv.(proto.Message); ok {
		err := proto.Unmarshal(d.Data, pb)
		return rv.(V), err
	}

	err = json.Unmarshal(d.Data, rv)
	return rv.(V), err
}
*/

// LoadDataSync 同步加载数据，只能服务器启动时调用！！！
func LoadDataSync(pid actor.PID, key string) ([]byte, error) {
	sid, _ := strconv.ParseUint(key, 10, 64)
	msg := &actor.Message{
		Id:   uint32(def.MsgId_Mongo_LoadData),
		Data: &g2m.G2M_LoadData{ServerId: sid},
	}

	respMsg := actor.SyncRequest(pid, actor_def.DBSystemPID, msg)
	if respMsg.Err != nil && respMsg.Err != redis.ErrNil {
		return nil, respMsg.Err
	}

	d := respMsg.Data.(*g2m.M2G_LoadData)
	return d.Data, nil
}

// SaveDataSync 同步存储数据接口 只有服务器启动时用！！！
func SaveDataSync(key string, data interface{}) error {
	sid, _ := strconv.ParseUint(key, 10, 64)
	d := &g2m.G2M_SaveData{
		ServerId: sid,
	}
	msg := &actor.Message{
		Id:   uint32(def.MsgId_Mongo_SaveData),
		Uid:  0,
		Data: d,
	}

	switch v := data.(type) {
	case []byte:
		d.Data = v
	case proto.Message:
		var err error
		d.Data, err = proto.Marshal(v)
		if err != nil {
			log.Error("proto marshal error", log.Err(err))
			return err
		}
	default:
		var err error
		d.Data, err = json.Marshal(v)
		if err != nil {
			return fmt.Errorf("json marshal error %v", err.Error())
		}
	}
	respMsg := actor.SyncRequest(actor_def.SystemPID, actor_def.DBSystemPID, msg)
	return respMsg.Err
}

// DeleteDataSync 同步删除接口 只有服务器启动时用！！！
func DeleteDataSync(pid actor.PID, key string) error {
	sid, _ := strconv.ParseUint(key, 10, 64)
	d := &g2m.G2M_DeleteData{
		ServerId: sid,
	}
	msg := &actor.Message{
		Id:   uint32(def.MsgId_Mongo_DeleteData),
		Data: d,
	}
	respMsg := actor.SyncRequest(pid, actor_def.DBSystemPID, msg)
	return respMsg.Err
}

// LoadGuildDataSync 同步加载公会数据
func LoadGuildDataSync(pid actor.PID) ([]byte, error) {
	msg := &actor.Message{
		Id:   uint32(def.MsgId_Mongo_LoadGuildData),
		Data: &g2m.G2M_LoadGuildData{},
	}

	respMsg := actor.SyncRequest(pid, actor_def.DBSystemPID, msg)
	if respMsg.Err != nil {
		return nil, respMsg.Err
	}

	d := respMsg.Data.(*g2m.M2G_LoadGuildData)
	return d.GuildData, nil
}

// LoadPaymentDataSync 同步加载支付数据
func LoadPaymentDataSync(pid actor.PID) ([]byte, error) {
	msg := &actor.Message{
		Id:   uint32(def.MsgId_Mongo_LoadPaymentData),
		Data: &g2m.G2M_LoadPaymentData{},
	}

	respMsg := actor.SyncRequest(pid, actor_def.DBSystemPID, msg)
	if respMsg.Err != nil {
		return nil, respMsg.Err
	}

	d := respMsg.Data.(*g2m.M2G_LoadPaymentData)
	return d.PaymentData, nil
}
func LoadTipOffSystem(pid actor.PID) ([]byte, error) {
	msg := &actor.Message{
		Id:   uint32(def.MsgId_Mongo_LoadTipOffData),
		Data: &g2m.G2M_LoadTipOffData{},
	}

	respMsg := actor.SyncRequest(pid, actor_def.DBSystemPID, msg)
	if respMsg.Err != nil {
		return nil, respMsg.Err
	}

	d := respMsg.Data.(*g2m.M2G_LoadTipOffData)
	return d.TipOffData, nil
}

// LoadSeasonBuffDB 同步加载赛季buff数据
func LoadSeasonBuffDB(pid actor.PID) ([]byte, error) {
	msg := &actor.Message{
		Id:   uint32(def.MsgId_Mongo_LoadSeasonBuffData),
		Data: &g2m.G2M_LoadSeasonBuffData{},
	}

	respMsg := actor.SyncRequest(pid, actor_def.DBSystemPID, msg)
	if respMsg.Err != nil {
		return nil, respMsg.Err
	}

	d := respMsg.Data.(*g2m.M2G_LoadSeasonBuffData)
	return d.SeasonBuffData, nil
}

// LoadUserSnapDataBatchSync 分页加载用户快照数据
func LoadUserSnapDataBatchSync(fromPID actor.PID, lastUid uint64, limit int64) ([]byte, bool, error) {

	msg := &actor.Message{
		Id: uint32(def.MsgId_Mongo_LoadUserSnapDataBatch),
		Data: &g2m.G2M_LoadUserSnapDataBatch{
			LastUid: lastUid, // 上次加载的最后一个用户ID
			Limit:   limit,   // 每批次加载的数量
		},
	}

	resp := actor.SyncRequest(fromPID, actor_def.DBSystemPID, msg)
	if resp.Err != nil {
		return nil, false, resp.Err
	}

	result, ok := resp.Data.(*g2m.M2G_LoadUserSnapDataBatch)
	if !ok {
		return nil, false, fmt.Errorf("invalid response type: %T", resp.Data)
	}

	return result.GetUserData(), result.GetHasMore(), nil
}

func LoadAllAccountMap(fromPID actor.PID, serverId uint64) (map[string]uint64, error) {
	msg := &actor.Message{
		Id: uint32(def.MsgId_Mongo_LoadAllAccountMap),
		Data: &g2m.G2M_LoadAllAccountMap{
			ServerId: serverId,
		},
	}
	resp := actor.SyncRequest(fromPID, actor_def.DBSystemPID, msg)
	if resp.Err != nil {
		return nil, resp.Err
	}
	if resp.Data == nil {
		return make(map[string]uint64), nil
	}
	ret := resp.Data.(*g2m.M2G_LoadAllAccountMap)
	return ret.AccountMap, nil
}

func LoadGlobalMailDataSync(pid actor.PID) (*dbstruct.MailList, error) {
	msg := &actor.Message{
		Id: uint32(def.MsgId_Mongo_LoadGlobalMail),
	}

	respMsg := actor.SyncRequest(pid, actor_def.DBSystemPID, msg)
	if respMsg.Err != nil {
		return nil, respMsg.Err
	}

	ret := respMsg.Data.(*g2m.M2G_LoadGlobalMail)
	return ret.List, nil
}

func SaveGlobalMailDataSync(pid actor.PID, db *dbstruct.MailList) error {
	msg := &actor.Message{
		Id: uint32(def.MsgId_Mongo_SaveGlobalMail),
		Data: &g2m.G2M_SaveGlobalMail{
			List: db,
		},
	}

	respMsg := actor.SyncRequest(pid, actor_def.DBSystemPID, msg)
	return respMsg.Err
}
