package global

import (
	"liteframe/internal/common/protos/dbstruct"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/common/table"
	"liteframe/pkg/uuid"
)

func GenMailInfoByTid(tid int32, sendTime int64, params []string, reward []*dbstruct.CommonKeyValue) *dbstruct.MailData {
	if sendTime <= 0 {
		sendTime = Now().Unix()
	}
	mailConf := table.GetTable().TableMail.GetById(tid)
	if mailConf == nil {
		return nil
	}
	if reward == nil && mailConf.Reward != nil {
		reward = make([]*dbstruct.CommonKeyValue, 0, len(mailConf.Reward))
		for _, v := range mailConf.Reward {
			reward = append(reward, &dbstruct.CommonKeyValue{Key: v[0], Value: v[1]})
		}
	}

	mailInfo := &dbstruct.MailData{
		Id:              int64(uuid.GenMailID()),
		TemplateId:      tid,
		CreateTime:      sendTime,
		ExpireTime:      int64(mailConf.Duration*86400) + sendTime,
		StringParamList: params,
		GoodsItems:      reward,
		Status:          int32(public.MailStateType_MailStateType_UnRead),
	}
	return mailInfo
}
