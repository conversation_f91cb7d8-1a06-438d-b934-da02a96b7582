package actor_def

import "liteframe/pkg/actor"

const (
	Actor_System_Begin   = iota // 系统 actor id 从1-999
	Actor_System_Default        // 系统发送 不需要返回
	Actor_System_Player
	Actor_System_DB
	Actor_System_Monitor            //监控系统
	Actor_System_Grpc               //grpcClient
	Actor_System_MailMgr            //globalMail
	Actor_System_Guild              //guildSystem
	Actor_System_Payment            //paymentSystem
	Actor_System_UserSnap           //玩家信息检索
	Actor_System_Arena              //竞技场系统
	Actor_System_TipOff             //举报系统
	Actor_System_SeasonBuff         //赛季buff系统
	Actor_System_Season             //赛季系统
	Actor_System_End        = 1000  // 系统 actor id结束
	Actor_Player_Begin      = 10000 // 玩家 actor id开始
)

var (
	SystemPID         = actor.NewPID(Actor_System_Default, "Actor_System_Default")
	PlayerSystemPID   = actor.NewPID(Actor_System_Player, "Actor_System_Player")
	DBSystemPID       = actor.NewPID(Actor_System_DB, "Actor_System_DB")
	MonitorSystemPID  = actor.NewPID(Actor_System_Monitor, "Actor_System_Monitor")
	GrpcSystemPID     = actor.NewPID(Actor_System_Grpc, "Actor_System_Grpc")
	MailMgrSystemPID  = actor.NewPID(Actor_System_MailMgr, "Actor_System_MailMgr")
	GuildSystemPID    = actor.NewPID(Actor_System_Guild, "Actor_System_Guild")
	PaymentSystemPID  = actor.NewPID(Actor_System_Payment, "Actor_System_Payment")
	UserSnapSystemPID = actor.NewPID(Actor_System_UserSnap, "Actor_System_UserSnap")
	ArenaSystemPID    = actor.NewPID(Actor_System_Arena, "Actor_System_Arena")
	TipOffSystem      = actor.NewPID(Actor_System_TipOff, "Actor_System_TipOff")
	SeasonBuffSystem  = actor.NewPID(Actor_System_SeasonBuff, "Actor_System_SeasonBuff")
	SeasonSystemPID   = actor.NewPID(Actor_System_Season, "Actor_System_Season")
)
