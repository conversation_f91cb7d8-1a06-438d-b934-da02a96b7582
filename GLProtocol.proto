// 协议ID类型为short，-32767 到 32767
//StartMessageID = 3000; // 必须以;分号结束
//MaxMessageID = 3999; // 必须以;分号结束
syntax = "proto3";
option go_package = "liteframe/internal/common/protos/cs";

package GamePackage;

import "PublicMessage.proto";
import "PublicEnum.proto";
import "DBProtocol.proto";

//Test
message GL_Test_Res
{
	int32 result = 1;	//1: 成功, 其他错误
}
//通知 lobyy玩家是否需要重新laod 全局邮件
message Gl_GlobalMailNeedReload_Res
{
	repeated int64 playerUidList = 1;			 //玩家的list 信息
	int32 param1 = 2;	//额外参数1，INT型，一般用做唯一处理类型标识
	int64 param2 = 3;	//额外参数2，LONG型
	int64 param3 = 4;	//额外参数3，LONG型
}
// 通知去请求好友信息
message Gl_NoticeSyncFriend_Res
{
	PBCommonLongKeyValue playerUidInfo = 1;							//玩家的id 和附加数据
}
// 踢人信息
message Gl_GlobalKickPlayer_Res
{
	int32	type = 1;										// 0提所有人  1 按照玩家id踢
	repeated int64 playerUid = 2;							//玩家的id
	int32 specialArgs = 3;									//特殊字段的信息
	int64 banTime = 4;
	string reason = 5;
}
// 通知Lobby执行JS脚本信息
message GL_GlobalToLobbyRunJS_Res
{
	string jsStr = 1;							//要运行的脚本信息
	int32 isClassCode = 2;						//是否为Class文件
	string scriptExecuteID = 3;					//脚本唯一ID，CMS用于提取结果
}
//通知 lobby 服务器状态信息
message Gl_GlobalGetAllActivityInfo_Res
{
	repeated PBActivityInfo activityInfoList = 1;			 // 所有活动的信息
}
//通知 lobby 服务器状态信息
message Gl_GlobalGetActivityInfo_Res
{
	PBActivityInfo activityInfo = 1;			 // 所有活动的信息
}
//请求兑换码兑换
message GL_CDKeyUse_Res
{
	int32 result						= 1;	//处理结果：0.成功可以发奖 1.校验失败 2.单码已达兑换上限 3.此批已达兑换上限 4.不符合条件
	int64 platformId 					= 2;	//当前玩家ID
	string code							= 3;	//兑换码
	repeated PBCommonKeyValue itemList	= 4;	//奖励列表
	int64 groupId						= 5;	//当前所属批次ID
	int32 groupUseLimit					= 6;	//当前批次使用上限
	string groupName					= 7;	//礼包类型：BI埋点用
}
// 通知禁言信息
message Gl_GlobalChatBanPlayer_Res
{
	int64 playerUid =1;							//玩家的id
	int64 chatBanTime = 2;
	string reason = 3;
}
// 通知天气信息
message Gl_GlobalWeather_Res
{
	int32 curWeatherId = 1;	 //当前天气的id
	int32 nextWeatherId = 2; //下一次的天气id
}
//全局广播事件信息GlobalToAllLobby
message GL_GlobalBroadcastEvent_Res
{
	GlobalBroadcastEventType type = 1;//事件类型
	int64 id = 2;//操作ID
	int32 intParam1 = 3;//INT参数1
	int32 intParam2 = 4;//INT参数2
	int32 intParam3 = 5;//INT参数3
	int64 longParam1 = 6;//LONG参数1
	int64 longParam2 = 7;//LONG参数2
	int64 longParam3 = 8;//LONG参数3
	string param1 = 9;//String参数1
	string param2 = 10;//String参数2
	string param3 = 11;//String参数3
}
