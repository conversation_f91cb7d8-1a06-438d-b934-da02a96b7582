﻿//*********************************************************
// Game
// Author:  Jasen
// Date  :  2022-2-1
//*********************************************************

using BattleServer.Framework;
using BattleServer.Server;
using Game.Core;
using Aurora.Framework;
using System;
using System.Collections.Generic;
using System.Text;

namespace BattleServer.Game
{
    public class Scene : IWorkUnitOwner//Entity
    {
        private string m_strLogName;
        private WorkUnitBase m_WorkUnit;

        private StateComponent mStateComponent;

        // 场景中的玩家
        private Dictionary<ulong, BattlePlayer> mPlayers = new Dictionary<ulong, BattlePlayer>();
        private List<BattlePlayer> mDeadPlayers = new List<BattlePlayer>();
        //场景中的棋盘
        private List<BattleChess> mBattles = new List<BattleChess>();

        private int mRound = 0;

        public ushort SceneID { get; set; }
        public int ConfigId { get; set; }

        public static int SCENE_ROW { get; set; }
        public static int SCENE_COl { get; set; }

        public TablePlayMode PlayMode { get; set; }

        public Scene()
        {
            mStateComponent = new StateComponent(this);

            // 棋盘行数
            SCENE_ROW = 10;
            // 棋盘列数
            SCENE_COl = 6;
        }

        public string LogName
        {
            get { return m_strLogName; }
            set { m_strLogName = value; }
        }

        public WorkUnitBase WorkUnit
        {
            get { return m_WorkUnit; }
            set { m_WorkUnit = value; }
        }

        void IWorkUnitOwner.Tick(int nDeltaTime)
        {
            // 调用虚方法，允许子类重写
            OnTick(nDeltaTime);
        }

        /// <summary>
        /// 虚方法，供子类重写实现自己的 Tick 逻辑
        /// </summary>
        /// <param name="nDeltaTime">时间增量（毫秒）</param>
        protected virtual void OnTick(int nDeltaTime)
        {
            // 基类默认实现为空
            mStateComponent.OnUpdate(nDeltaTime);

            //for ()
            //{
            //    mChessBoard[i].OnUpdate();
            //}
        }

        public void OnUpdate(int nDeltaTime)
        {
            mStateComponent.OnUpdate(nDeltaTime);
        }

        void IWorkUnitOwner.InitWorkUnit()
        {
            Log.Debug($"[Scene] InitWorkUnit sceneId {SceneID} configId {ConfigId}");
            WorkUnitManager.Instance.CreateWorkUnit(this);

            //WorkUnit.EntitySystem.AddRootEntity(this);
        }

        void IWorkUnitOwner.ClearWorkUnit()
        {
            Log.Debug($"[Scene] ClearWorkUnit sceneId {SceneID} configId {ConfigId}");
            //WorkUnit.EntitySystem.RemoveRootEntity(this);

            WorkUnitManager.Instance.DestroyWorkUnit(this);
        }

        void IWorkUnitOwner.OnStart()
        {
            Log.Debug($"[Scene] OnStart sceneId {SceneID} configId {ConfigId}");

            // 切换到等待状态
            //mStateComponent.ChangeState(StateType.Wait);
        }

        void IWorkUnitOwner.OnStop()
        {
            Log.Debug($"[Scene] OnStop sceneId {SceneID} configId {ConfigId}");
        }

        void IWorkUnitOwner.OnDestroy()
        {
            Log.Debug($"[Scene] OnDestroy sceneId {SceneID} configId {ConfigId}");
            //SceneHasShutDown msg = new SceneHasShutDown();
            //{
            //    msg.Scene = this;
            //}
            //Packet packet = Packet.Create(msg);
            //ServerPacketHelper.ToSelfServerGlobalMgr(packet, GlobalManagerType.SERVER_SCENE_MANAGER);
            //PacketDispatcherManager.Dispatch(packet);
        }

        void IWorkUnitOwner.OnException(Exception e)
        {
            Log.Error($"[Scene] OnException sceneId {SceneID} configId {ConfigId} Exception: {e.Message}");
            //if (SceneType == SceneType.Copy)
            //{
            //    S2S_ReqDestroyCopyScene msg = new S2S_ReqDestroyCopyScene();
            //    {
            //        msg.SceneId = SceneID;
            //    }
            //    Packet packet = Packet.Create(msg);
            //    ServerPacketHelper.ToSelfServerGlobalMgr(packet, GlobalManagerType.SERVER_SCENE_MANAGER);
            //    PacketDispatcherManager.Dispatch(packet);
            //}
        }

        // 场景向场景管理器发消息
        public void SendMessageToSceneManager(IMessage msg)
        {
            if (msg == null)
            {
                return;
            }

            //SceneSessionComponent sceneSessionCom = GetComponent<SceneSessionComponent>();
            //if (sceneSessionCom == null)
            //{
            //    return;
            //}

            //Packet packet = Packet.Create(msg);
            //ServerPacketHelper.ToSelfServerGlobalMgr(packet, GlobalManagerType.SERVER_SCENE_MANAGER);
            //PacketDispatcherManager.Dispatch(packet);
        }

        // 给场景中所有玩家都广播消息
        public void BroadCastToAllPlayer(IMessage msg)
        {
            if (msg == null)
            {
                return;
            }

            //UnitsComponent unitsCom = GetComponent<UnitsComponent>();
            //if (unitsCom == null)
            //{
            //    return;
            //}

            //foreach (Unit unit in unitsCom.Units.Values)
            //{
            //    UnitTypeComponent unitTypeCom = unit.GetComponent<UnitTypeComponent>();
            //    if (unitTypeCom == null || unitTypeCom.Type != EUnitType.Player)
            //    {
            //        continue;
            //    }

            //    Packet packet = Packet.Create(msg);
            //    ServerPacketHelper.ToSelfServerGamePlayer(packet, unit.GamePlayerID);
            //    PacketDispatcherManager.Dispatch(packet);
            //}
        }


        public void InitPlayers(List<PBBattlePlayerInfo> players, List<PBBattleTeamInfo> teams)
        {
            if (players.Count < 4)
            {
                Log.Error($"Scene Create Error: player count is less than 4 now {players.Count}");
                return;
            }

            for (int i = 0; i < players.Count; i++)
            {
                BattlePlayer player = new BattlePlayer(players[i], teams[i]);
                if (i != 0)
                {
                    player.IsRobot = true;
                }
                mPlayers.Add(player.Info.Uid, player);
            }

            mBattles.Add(new BattleChess(SceneID * 10 + 1, this, 10, 6));
            mBattles.Add(new BattleChess(SceneID * 10 + 2, this, 10, 6));

            mStateComponent.ChangeState(StateType.Wait);
        }


        public void OnPlayerEnter(ulong uid)
        {
            Log.Debug($"[Scene]{SceneID} OnPlayerEnter uid {uid}");

            BattlePlayer? player;
            mPlayers.TryGetValue(uid, out player);
            if (player == null)
            {
                Log.Error($"[Scene]{SceneID} OnPlayerEnter Not find player uid {uid}");
                return;
            }

            player.OnEnterBattle();
        }

        public void OnPlayerSelectBuffer(ulong uid, int bufferID)
        {
            Log.Debug($"[Scene] OnPlayerSelectBuffer uid {uid} buffer id {bufferID}");

            BattlePlayer player;
            mPlayers.TryGetValue(uid, out player);
            if (player == null)
            {
                Log.Error($"[Scene]{SceneID} OnPlayerSelectBuffer Not find player uid {uid}");
                return;
            }

            player.OnSelectBuffer(bufferID);
        }

        public void OnPlayerMoveHero(ulong uid, List<PBMoveOperation> moveOps)
        {
            Log.Debug($"[Scene] OnPlayerMoveHero uid {uid} moveOps count {moveOps.Count}");

            BattlePlayer player;
            mPlayers.TryGetValue(uid, out player);
            if (player == null)
            {
                Log.Error($"[Scene]{SceneID} OnPlayerMoveHero Not find player uid {uid}");
                return;
            }

            player.OnMoveHero(moveOps);
        }

        public List<Hero> OnPlayerMergeHero(ulong uid, int from, int to)
        {
            Log.Debug($"[Scene] OnPlayerMergeHero uid {uid} from {from} to {to}");

            BattlePlayer player;
            mPlayers.TryGetValue(uid, out player);
            if (player == null)
            {
                Log.Error($"[Scene]{SceneID} OnPlayerMergeHero Not find player uid {uid}");
                return null;
            }

            return player.OnMergeHero(from, to);
        }

        public void OnPlayerReady(ulong uid)
        {
            Log.Debug($"[Scene] OnPlayerReady uid {uid}");
            BattlePlayer player;
            mPlayers.TryGetValue(uid, out player);
            if (player == null)
            {
                Log.Error($"[Scene]{SceneID} OnPlayerReady Not find player uid {uid}");
                return;
            }

            player.OnBattleReady();
        }

        public void OnPlayerBattleEnd(ulong uid, bool win)
        {
            Log.Debug($"[Scene] OnPlayerBattleEnd uid {uid} win {win}");

            BattlePlayer player;
            mPlayers.TryGetValue(uid, out player);
            if (player == null)
            {
                Log.Error($"[Scene] OnPlayerBattleEnd Not find player uid {uid} win {win}");
                return;
            }

            player.battle.OnPlayerBattleEnd(uid, win);
        }

        public void OnPlayerDeath(ulong uid)
        {
            Log.Debug($"[Scene] OnPlayerDeath uid {uid}");
            BattlePlayer player;
            mPlayers.TryGetValue(uid, out player);
            if (player == null)
            {
                Log.Error($"[Scene]{SceneID} OnPlayerDeath Not find player uid {uid}");
                return;
            }

            mPlayers.Remove(uid);
            mDeadPlayers.Add(player);
        }

        public void OnDestory()
        {
            Log.Debug($"[Scene] OnDestory sceneId {SceneID}");
            // 清理所有玩家
            foreach (var player in mPlayers.Values)
            {
                //player.OnBattleEnd();
                SceneManager.Instance.RemovePlayerScene(player.Info.Uid);
            }

            foreach (var deadPlayer in mDeadPlayers)
            {
                SceneManager.Instance.RemovePlayerScene(deadPlayer.Info.Uid);
            }
            mPlayers.Clear();
            // 清理所有棋盘
            mBattles.Clear();

            mRound = 0;

            SceneManager.Instance.DestoryScene(SceneID);
        }

        public void Match()
        {
            mRound += 1;

            Log.Debug($"[Scene] Match round {mRound} sceneId {SceneID}");

            MatchManager.Match(GetPlayers(), GetDeadPlayers(), this);
        }

        public BattlePlayer? GetPlayer(ulong uid)
        {
            BattlePlayer? player;
            mPlayers.TryGetValue(uid, out player);
            return player;
        }

        public int GetRound()
        {
            return mRound;
        }

        //public void GeneratedHeros()
        //{
        //    foreach (var player in mPlayers.Values)
        //    {
        //        if (player.IsDead())
        //            continue;

        //        player.GeneratedHeros();
        //    }
        //}

        //public void FlashBuffer()
        //{
        //    foreach (var player in mPlayers.Values)
        //    {
        //        if (player.IsDead())
        //            continue;
        //        if (player.IsRobot)
        //            continue;

        //        player.RandomBuffers();
        //    }
        //}

        public Dictionary<ulong, BattlePlayer> GetPlayers()
        {
            return mPlayers;
        }

        public List<BattlePlayer> GetDeadPlayers()
        {
            return mDeadPlayers;
        }

        public List<BattleChess> GetBattles()
        {
            return mBattles;
        }
    }


    public static class SceneFactory
    {
        public static Scene CreateScene(int nConfigID, ushort usSceneID)
        {
            // this will be modified to use a factory method or DI container in the future
            Scene scene = new Scene();
            (scene as IWorkUnitOwner).InitWorkUnit();
            scene.ConfigId = nConfigID;
            scene.SceneID = usSceneID;

            return scene;
        }

        public static void DestroyScene(Scene scene)
        {
            if (scene != null)
            {
                (scene as IWorkUnitOwner).ClearWorkUnit();
            }
        }
    }
}
