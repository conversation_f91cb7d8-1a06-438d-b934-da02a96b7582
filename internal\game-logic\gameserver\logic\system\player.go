package system

import (
	"context"
	"fmt"
	"time"

	"liteframe/internal/common/constant/def"
	"liteframe/internal/game-logic/gameserver/logic/system/dao/dbhelper"

	"liteframe/internal/game-logic/gameserver/game_def/actor_def"
	"liteframe/internal/game-logic/gameserver/gameutil/metrics"
	"liteframe/internal/game-logic/gameserver/global"
	"liteframe/pkg/actor"
	"liteframe/pkg/listmap"
	"liteframe/pkg/log"
)

const (
	cacheSeconds    = 60    // 缓存1小時 TODO 恢复3600
	batchCount      = 20    // 每个tick淘汰数 一秒10个tick
	maxAccountCache = 50000 // 账号最大缓存个数
	maxActorCache   = 10    // 最大Actor数  TODO 恢复10000
	allSaveSeconds  = 600   // 一秒10个tick 一分钟存全部

	// 预备登录缓存，用于存储账号和token信息，用于登录时验证
	prepareLoginCacheExpireSec = 300 // 5分钟过期
)

// playerSystem 管理在线玩家 和 玩家缓存
type playerSystem struct {
	dispatch *actor.Dispatcher

	accounts map[string]uint64       // account uid map 服务器有注册上限,内存占用不到1M，启服加载 不做lru
	sessions map[uint64]*accountInfo // session id account map

	// 预备登录缓存，用于存储账号和token信息，用于登录时验证
	prepareLoginCache map[string]*prepareLoginInfo // account -> login info map

	actors        map[uint64]*playerInfo // 在线 或离线的  player 信息，tick中删除离线且长时间未有数据变化的
	onlineActors  map[uint64]*playerInfo // 在线player信息
	offlineActors map[uint64]*playerInfo // 离线player信息 踢除离线玩家也需要存盘 避免回调导致玩家数据变更不能存储的问题

	changeList  *listmap.ListMap // 变更的玩家 包括在线离线
	offlineList *listmap.ListMap // offline 玩家列表 按照时间排序， 用于淘汰 uid -> lastTime

	loginIndex uint32
	saveIndex  uint32
	exiting    bool

	// 登录速率统计
	loginCount    int       // 当前秒内登录人数
	loginRateTime time.Time // 最后一次登录速率重置时间
}

// prepareLoginInfo 预备登录信息
type prepareLoginInfo struct {
	Token      string    // 令牌
	CreateTime time.Time // 创建时间
}

func NewPlayerSystem() *playerSystem {
	p := &playerSystem{
		dispatch: actor.NewDispatcher(100, func(uid uint64, id uint32, ms int64) {
			metrics.SetRpcCost(fmt.Sprintf("playersys_%d", id), ms)
			if ms > 100 {
				log.Debug("player system", log.Kv("id", id), log.Kv("cost", ms))
			}
		}),

		accounts:          make(map[string]uint64),
		sessions:          make(map[uint64]*accountInfo),
		prepareLoginCache: make(map[string]*prepareLoginInfo),
		actors:            make(map[uint64]*playerInfo),
		offlineActors:     make(map[uint64]*playerInfo),
		onlineActors:      make(map[uint64]*playerInfo),
		changeList:        listmap.NewListMap(),
		offlineList:       listmap.NewListMap(),
		loginCount:        0,
		loginRateTime:     time.Now(),
	}
	p.loadAccountMap()

	p.Register()
	return p
}
func (s *playerSystem) loadAccountMap() {
	accountMap, err := dbhelper.LoadAllAccountMap(s.PID(), uint64(global.ServerId))
	if err != nil {
		log.Error("load account map failed", log.Err(err))
		return
	}
	s.accounts = accountMap
	log.Info("load account map", log.Kv("count", len(s.accounts)))
}
func (s *playerSystem) PID() actor.PID {
	return actor_def.PlayerSystemPID
}

func (s *playerSystem) Process(msg *actor.Message) {
	if s.exiting && ((msg.Id >= uint32(def.MsgId_Game_Begin) &&
		msg.Id <= uint32(def.MsgId_Game_End)) ||
		msg.Id == uint32(def.MsgId_Actor_GM)) { //游戏退出状态下玩家相关的消息不处理
		log.Warn("player system exiting", log.Kv("msg_id", msg.Id))
		return
	}
	if msg.Id >= uint32(def.MsgId_Game_Begin) &&
		msg.Id <= uint32(def.MsgId_Game_End) { // client -> game msg
		s.clientMsgDispatch(msg)
	} else { // login or actor -> actor
		s.dispatch.Dispatch(context.TODO(), msg)
	}
}

func (s *playerSystem) Register() {
	s.dispatch.Register(uint32(def.MsgId_Actor_SyncConfig), s.syncHandler)
	s.dispatch.Register(uint32(def.MsgId_Gate_Login), s.loginHandler)
	s.dispatch.Register(uint32(def.MsgId_Gate_Logout), s.logoutHandler)
	s.dispatch.Register(uint32(def.MsgId_Gate_PrepareLogin), s.prepareLoginHandler)
	s.dispatch.Register(uint32(def.MsgId_Actor_PlayerOnline), s.playerOnlineHandler)
	s.dispatch.Register(uint32(def.MsgId_Actor_PlayerOffline), s.playerOfflineHandler)

	s.dispatch.Register(uint32(def.MsgId_Actor_GM), s.gmHandle)
	s.dispatch.Register(uint32(def.MsgId_Actor_GMT), s.gmtHandle)

	s.dispatch.Register(uint32(def.MsgId_Actor_ServerTick), s.serverTickHandler)
	s.dispatch.Register(uint32(def.MsgId_Actor_Second), s.secondHandler)
	s.dispatch.Register(uint32(def.MsgId_Actor_CrossDay), s.crossDayHandler)
	s.dispatch.Register(uint32(def.MsgId_Actor_CrossWeek), s.crossWeekHandler)
	s.dispatch.Register(uint32(def.MsgId_Actor_CrossMonth), s.crossMonthHandler)

	s.dispatch.Register(uint32(def.MsgId_Actor_Player_MatchResult), s.sendToOnlinePlayerHandler)
	s.dispatch.Register(uint32(def.MsgId_Actor_Player_BattleStart), s.sendToOnlinePlayerHandler)
	s.dispatch.Register(uint32(def.MsgId_Actor_Player_RoundStart), s.sendToOnlinePlayerHandler)
	s.dispatch.Register(uint32(def.MsgId_Actor_Player_RoundEnd), s.sendToOnlinePlayerHandler)
	s.dispatch.Register(uint32(def.MsgId_Actor_Player_BattleEnd), s.sendToOnlinePlayerHandler)
	//s.dispatch.Register(uint32(def.MsgId_Actor_Player_RoomDestroy_Notify), s.sendToOnlinePlayerHandler)
	//s.dispatch.Register(uint32(def.MsgId_Actor_Player_BattleResult), s.sendToOnlinePlayerHandler)
	//s.dispatch.Register(uint32(def.MsgId_Actor_Player_BattleReady_Timeout), s.sendToOnlinePlayerHandler)
	//s.dispatch.Register(uint32(def.MsgId_Actor_Player_DouPveLike), s.sendToOnlinePlayerHandler)

	s.dispatch.Register(uint32(def.MsgId_Actor_StopGame), s.stopGameHandler)
	s.dispatch.Register(uint32(def.MsgId_Actor_PlayerSystem_Exit_Status), s.exitStatusHandler)

	s.dispatch.Register(uint32(def.MsgId_Actor_GuildSystem2PlayerSystem), s.guildSystemMessageHandler)

	s.dispatch.Register(uint32(def.MsgId_Actor_PaymentSystem2PlayerSystem), s.paymentSystemMessageHandler)

	s.dispatch.Register(uint32(def.MsgId_Actor_SeasonBuffSystem2PlayerSystem), s.seasonBuffSystemMessageHandler)

	s.dispatch.Register(uint32(def.MsgId_Actor_SeasonSystem2PlayerSystem), s.seasonSystemMessageHandler)

	// 注册处理全局邮件系统消息
	//s.dispatch.Register(uint32(def.MsgId_Actor_Mail_AddGlobalMail), s.globalMailSystemMessageHandler)
}

func (s *playerSystem) StopPlayer(pid actor.PID) {
}

func (s *playerSystem) OnStop() {
	log.Info("player system stop")
}

func (s *playerSystem) Tick(t time.Time) {
	// 清理过期的预备登录缓存
	s.cleanExpiredPrepareLoginCache(t)

	// 重置登录计数
	if t.Sub(s.loginRateTime).Seconds() >= 1 {
		s.loginCount = 0
		s.loginRateTime = t
	}

	for _, info := range s.onlineActors { // batch save online
		if info.loginIdx%allSaveSeconds != s.saveIndex {
			continue
		}
		actor.Send(s.PID(), info.PID(), &actor.Message{
			Id:  uint32(def.MsgId_Actor_AsyncSaveUser),
			Uid: info.uid,
		})
		s.changeList.RemoveByKey(info.uid)
	}

	count := batchCount
	for ; count > 0 && s.changeList.Len() > 0; count-- { // batch save changes
		kv := s.changeList.FrontPair()
		s.changeList.RemoveByKey(kv.K)
		uid := kv.K.(uint64)
		info := s.actors[uid]
		if info == nil {
			log.Error("can't find online user", log.Kv("uid", uid))
			continue
		}

		actor.Send(s.PID(), info.PID(), &actor.Message{
			Id:  uint32(def.MsgId_Actor_AsyncSaveUser),
			Uid: uid,
		})
	}

	if s.offlineList.Len() >= maxActorCache {
		s.evictOfflineActor(t)
	}
}

// cleanExpiredPrepareLoginCache 清理过期的预备登录缓存
func (s *playerSystem) cleanExpiredPrepareLoginCache(t time.Time) {
	for account, info := range s.prepareLoginCache {
		if t.Sub(info.CreateTime).Seconds() > prepareLoginCacheExpireSec {
			log.Debug("clean expired prepare login cache", log.Kv("account", account))
			delete(s.prepareLoginCache, account)
		}
	}
}

func (s *playerSystem) addSession(sid uint64, account string) {
	s.sessions[sid] = &accountInfo{
		account: account,
	}
	metrics.SetConnectGauge(len(s.sessions))
}

func (s *playerSystem) removeSession(sid uint64) {
	delete(s.sessions, sid)
	metrics.SetConnectGauge(len(s.sessions))
}

func (s *playerSystem) addOffline(uid uint64, info *playerInfo) {
	s.offlineActors[uid] = info                      // update offline info
	s.offlineList.PushBack(uid, global.Now().Unix()) // update offline time
	metrics.SetOfflineGauge(len(s.offlineActors))
}

func (s *playerSystem) removeOffline(uid uint64) {
	delete(s.offlineActors, uid)
	s.offlineList.RemoveByKey(uid)
	metrics.SetOfflineGauge(len(s.offlineActors))
}

//	func (s *playerSystem) evictAccountMap() {
//		count := batchCount
//		for k, v := range s.accounts {
//			if _, ok := s.offlineActors[v]; ok { //TODO check offlineActors
//				delete(s.accounts, k)
//				count--
//				if count <= 0 {
//					return
//				}
//			}
//		}
//	}
func (s *playerSystem) addAccountMap(account string, uid uint64) {
	//if len(account) >= maxAccountCache {
	//	s.evictAccountMap()
	//}
	s.accounts[account] = uid
}

func (s *playerSystem) evictOfflineActor(t time.Time) {
	count := batchCount
	for e := s.offlineList.Front(); e != nil; e = e.Next() { // delete not active offline
		kv := e.Value.(*listmap.Pair)
		lt := kv.V.(int64)
		if lt+cacheSeconds > t.Unix() {
			break
		}

		uid := kv.K.(uint64)
		info := s.actors[uid]
		if info == nil || info.st != ps_offline {
			continue
		}
		actor.AsyncRequest(s.PID(), info.PID(), &actor.Message{
			Id:  uint32(def.MsgId_Actor_PlayerExit),
			Uid: uid,
		}, func(msg actor.RespMessage) {
			s.evictOfflineCallBack(info, t, msg)
		})

		if count <= 0 {
			return
		}
		count--
	}
}

func (s *playerSystem) evictOfflineCallBack(info *playerInfo, t time.Time, msg actor.RespMessage) {
	uid := info.uid
	if msg.Err != nil {
		log.Error("player exit failed", log.Kv("uid", uid), log.Err(msg.Err))
		delete(s.actors, uid)
		s.removeOffline(uid)
		actor.StopActor(info.PID())
		return
	}
	v := s.offlineList.GetValue(uid)
	if v == nil {
		log.Warn("player not in offline list", log.Kv("uid", uid)) // may login again
		return
	}
	ct := v.(int64)
	if ct+cacheSeconds > t.Unix() { // change between exit return, can't destroy
		return
	}
	if info.st != ps_offline { // change between exit return, can't destroy
		return
	}
	delete(s.actors, uid)
	s.removeOffline(uid)
	actor.StopActor(info.PID())
	log.Debug("delete actor", log.Kv("uid", info.uid))
}
